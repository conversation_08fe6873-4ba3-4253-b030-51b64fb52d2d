import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
} from "react-native";
import React from "react";
import { z } from "zod";
import { useForm, FormProvider, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useUpdateUserProfileMutation } from "@/generated/graphql";
import {
  FormDatePicker,
  FormInput,
  FormSelect,
  FormSubmitButton,
} from "@/components/forms";
import { enumToOptions, errorToast, successToast } from "@/lib/utils";
import { Button } from "@/components/ui/Button";
import { Colors } from "@/constants/Colors";

// Custom options with proper display labels
const genderOptions = [
  { value: "male", label: "Male" },
  { value: "female", label: "Female" },
  { value: "other", label: "Other" },
];

const maritalStatusOptions = [
  { value: "single", label: "Single" },
  { value: "married", label: "Married" },
  { value: "divorced", label: "Divorced" },
  { value: "widowed", label: "Widowed" },
];
enum CountryCode {
  MY = "+60",
  IN = "+91",
  US = "+1",
  UK = "+44",
  SG = "+65",
}

interface UserProfileFormProps {
  userId: string;
  initialData?: {
    ic?: string;
    ID?: string;
    passport?: string;
    passportExpiresAt?: Date;
    permitNumber?: string;
    permitExpiresAt?: Date;
    gender?: "male" | "female" | "other";
    dob?: Date;
    placeOfBirth?: string;
    currentAddress?: string;
    joinedAt?: Date;
    maritalStatus?: "single" | "married" | "divorced" | "widowed";
    bankAccNumber?: string;
    bankName?: string;
    emergencyContact?: Array<{
      name: string;
      relation: string;
      contact: {
        countryCode: string;
        phone: string;
      };
    }>;
  };
}

const contactSchema = z.object({
  countryCode: z.string().default("+60"),
  phone: z.string().optional(),
});

const emergencyContactSchema = z.object({
  name: z.string().optional(),
  relation: z.string().optional(),
  contact: contactSchema,
});

const userProfileSchema = z.object({
  ic: z.string().optional(),
  ID: z.string().optional(),
  passport: z.string().optional(),
  passportExpiresAt: z
    .union([z.date(), z.string()])
    .optional()
    .transform((val) => {
      if (!val) return undefined;
      if (typeof val === "string") return new Date(val);
      return val;
    }),
  permitNumber: z.string().optional(),
  permitExpiresAt: z
    .union([z.date(), z.string()])
    .optional()
    .transform((val) => {
      if (!val) return undefined;
      if (typeof val === "string") return new Date(val);
      return val;
    }),
  gender: z.enum(["male", "female", "other"]).optional(),
  dob: z
    .union([z.date(), z.string()])
    .optional()
    .transform((val) => {
      if (!val) return undefined;
      if (typeof val === "string") return new Date(val);
      return val;
    }),
  placeOfBirth: z.string().optional(),
  currentAddress: z.string().optional(),
  joinedAt: z
    .union([z.date(), z.string()])
    .optional()
    .transform((val) => {
      if (!val) return undefined;
      if (typeof val === "string") return new Date(val);
      return val;
    }),
  maritalStatus: z
    .enum(["single", "married", "divorced", "widowed"])
    .optional(),
  bankAccNumber: z.string().optional(),
  bankName: z.string().optional(),
  emergencyContact: z.array(emergencyContactSchema).optional(),
});

// Type for form input (before transformation)
type UserProfileFormInput = {
  ic?: string;
  ID?: string;
  passport?: string;
  passportExpiresAt?: Date;
  permitNumber?: string;
  permitExpiresAt?: Date;
  gender?: "male" | "female" | "other";
  dob?: Date;
  placeOfBirth?: string;
  currentAddress?: string;
  joinedAt?: Date;
  maritalStatus?: "single" | "married" | "divorced" | "widowed";
  bankAccNumber?: string;
  bankName?: string;
  emergencyContact?: Array<{
    name?: string;
    relation?: string;
    contact: {
      countryCode: string;
      phone?: string;
    };
  }>;
};

export const useUpdateUserProfileFormMethods = (
  initialData?: UserProfileFormProps["initialData"]
) => {
  return useForm<UserProfileFormInput>({
    resolver: zodResolver(userProfileSchema) as any,
    defaultValues: {
      ic: initialData?.ic ?? "",
      ID: initialData?.ID ?? "",
      passport: initialData?.passport ?? "",
      passportExpiresAt:
        initialData?.passportExpiresAt ?? (undefined as unknown as Date),
      permitNumber: initialData?.permitNumber ?? "",
      permitExpiresAt:
        initialData?.permitExpiresAt ?? (undefined as unknown as Date),
      gender: initialData?.gender ?? undefined,
      dob: initialData?.dob ?? (undefined as unknown as Date),
      placeOfBirth: initialData?.placeOfBirth ?? "",
      currentAddress: initialData?.currentAddress ?? "",
      joinedAt: initialData?.joinedAt ?? (undefined as unknown as Date),
      maritalStatus: initialData?.maritalStatus ?? undefined,
      bankAccNumber: initialData?.bankAccNumber ?? "",
      bankName: initialData?.bankName ?? "",
      emergencyContact: initialData?.emergencyContact ?? [],
    },
  });
};

export default function UserProfileForm({
  userId,
  initialData,
}: UserProfileFormProps) {
  const scrollRef = React.useRef<ScrollView>(null);

  const methods = useUpdateUserProfileFormMethods(initialData);
  const { mutateAsync: updateUserProfile } = useUpdateUserProfileMutation();

  const { fields, append, remove } = useFieldArray({
    control: methods.control,
    name: "emergencyContact",
  });

  const onSubmit = async (data: UserProfileFormInput) => {
    try {
      // Filter out empty emergency contacts and ensure proper typing
      const validEmergencyContacts =
        data.emergencyContact
          ?.filter(
            (contact) =>
              contact.name && contact.relation && contact.contact.phone
          )
          .map((contact) => ({
            name: contact.name!,
            relation: contact.relation!,
            contact: {
              countryCode: contact.contact.countryCode,
              phone: contact.contact.phone!,
            },
          })) || [];

      await updateUserProfile({
        userId,
        updateUserProfileInput: {
          ...data,
          passportExpiresAt: data?.passportExpiresAt ?? undefined,
          permitExpiresAt: data?.permitExpiresAt ?? undefined,
          dob: data?.dob ?? undefined,
          joinedAt: data?.joinedAt ?? undefined,
          emergencyContact:
            validEmergencyContacts.length > 0
              ? validEmergencyContacts
              : undefined,
        },
      });
      successToast("Profile updated successfully!");
      methods.reset(data); // Reset form with updated data
    } catch (error) {
      errorToast(error);
    }
  };

  return (
    <FormProvider {...methods}>
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        keyboardVerticalOffset={100}
        style={styles.container}
      >
        <ScrollView ref={scrollRef} showsVerticalScrollIndicator={false}>
          <FormInput
            control={methods.control}
            name="ic"
            label="IC Number"
            placeholder="Enter IC number"
          />
          <FormInput
            control={methods.control}
            name="ID"
            label="ID Number"
            placeholder="Enter ID number"
          />
          <FormInput
            control={methods.control}
            name="passport"
            label="Passport Number"
            placeholder="Enter passport number"
          />
          <FormDatePicker
            control={methods.control}
            name="passportExpiresAt"
            label="Passport Expiry Date"
            placeholder="Select passport expiry date"
          />
          <FormInput
            control={methods.control}
            name="permitNumber"
            label="Permit Number"
            placeholder="Enter permit number"
          />
          <FormDatePicker
            control={methods.control}
            name="permitExpiresAt"
            label="Permit Expiry Date"
            placeholder="Select permit expiry date"
          />
          <FormSelect
            control={methods.control}
            name="gender"
            options={genderOptions}
            label="Select Gender"
          />
          <FormDatePicker
            control={methods.control}
            name="dob"
            label="Date of birth"
            placeholder="Select date of birth"
          />
          <FormInput
            control={methods.control}
            name="placeOfBirth"
            label="Place of Birth"
            placeholder="Enter place of birth"
          />
          <FormInput
            control={methods.control}
            name="currentAddress"
            label="Current Address"
            placeholder="Enter current address"
          />
          <FormDatePicker
            control={methods.control}
            name="joinedAt"
            label="Joined Date"
            placeholder="Select joined date"
          />
          <FormSelect
            control={methods.control}
            name="maritalStatus"
            options={maritalStatusOptions}
            label="Select marital status"
          />
          <FormInput
            control={methods.control}
            name="bankAccNumber"
            label="Bank Account Number"
            placeholder="Enter bank account number"
          />
          <FormInput
            control={methods.control}
            name="bankName"
            label="Bank Name"
            placeholder="Enter bank name"
          />
          <View>
            <Text
              style={{
                fontSize: 18,
                color: "#333",
                textDecorationStyle: "solid",
                textDecorationLine: "underline",
                marginVertical: 16,
                fontWeight: "500",
              }}
            >
              Emergency Contacts
            </Text>
            {fields.length === 0 ? (
              <Button
                style={{
                  backgroundColor: Colors.primaryDark,
                  marginTop: 8,
                  marginBottom: 16,
                }}
                title="Add Emergency Contact"
                textStyle={{ fontSize: 14 }}
                onPress={() => {
                  append({
                    name: "",
                    relation: "",
                    contact: { countryCode: "+60", phone: "" },
                  });

                  // Wait for UI to update before scrolling
                  setTimeout(() => {
                    scrollRef.current?.scrollToEnd({ animated: true });
                  }, 100);
                }}
              />
            ) : (
              fields.map((field, index) => (
                <View key={field.id} style={{ marginBottom: 16 }}>
                  <FormInput
                    control={methods.control}
                    name={`emergencyContact.${index}.name`}
                    label="Name"
                    placeholder="Enter contact name"
                  />
                  <FormInput
                    control={methods.control}
                    name={`emergencyContact.${index}.relation`}
                    label="Relation"
                    placeholder="Enter relation"
                  />
                  <FormSelect
                    control={methods.control}
                    name={`emergencyContact.${index}.contact.countryCode`}
                    label="Country Code"
                    options={enumToOptions(CountryCode)}
                    placeholder="Select country code"
                  />
                  <FormInput
                    control={methods.control}
                    name={`emergencyContact.${index}.contact.phone`}
                    label="Phone Number"
                    placeholder="Enter phone number"
                  />
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "center",
                      gap: 8,
                    }}
                  >
                    {index === fields.length - 1 && (
                      <Button
                        style={{
                          backgroundColor: Colors.primaryDark,
                          marginTop: 4,
                          width: 150,
                        }}
                        title="Add Contact"
                        textStyle={{ fontSize: 14 }}
                        onPress={() => {
                          append({
                            name: "",
                            relation: "",
                            contact: { countryCode: "+60", phone: "" },
                          });

                          // Wait for UI to update before scrolling
                          setTimeout(() => {
                            scrollRef.current?.scrollToEnd({ animated: true });
                          }, 100);
                        }}
                      />
                    )}
                    <Button
                      style={{
                        backgroundColor: Colors.error,
                        marginTop: 4,
                        width: 150,
                      }}
                      textStyle={{
                        fontSize: 14,
                        color: Colors.white,
                      }}
                      onPress={() => remove(index)}
                      title="Remove Contact"
                    />
                  </View>
                </View>
              ))
            )}
          </View>
          <FormSubmitButton
            submitLabel={initialData ? "Update" : "Submit"}
            onSubmit={methods.handleSubmit(onSubmit)}
            isValid={methods.formState.isValid}
            style={styles.submitButton}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </FormProvider>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 16,
    paddingHorizontal: 16,
  },
  submitButton: { marginBottom: 20 },
});

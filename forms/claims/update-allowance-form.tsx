import {
  FormDatePicker,
  Form<PERSON>magePicker,
  FormInput,
  FormSubmitButton,
} from "@/components/forms";
import { Colors } from "@/constants/Colors";
import {
  ClaimType,
  useClaimQuery,
  useCreateClaimMutation,
  useUpdateClaimMutation,
} from "@/generated/graphql";
import { errorToast, successToast } from "@/lib/utils";
import { useSession } from "@/providers/auth-provider";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigation, useRouter } from "expo-router";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  View,
} from "react-native";
import * as z from "zod";
import AllowanceForm from "./allowance-form";

const schema = z.object({
  from: z.coerce.date({ invalid_type_error: "From Date is required" }),
  to: z.coerce.date({ invalid_type_error: "To Date is required" }),
  purpose: z.string().min(1, { message: "Reason is required" }),
  workingHours: z.number().min(1, { message: "Working hours is required" }),
  amount: z.number().min(1, { message: "Amount is required" }),
  receipts: z.array(z.string()).nullable(),
  user: z.string().min(1),
  claimType: z.nativeEnum(ClaimType),
});

type AllowanceForm = z.infer<typeof schema>;

export default function UpdateAllowanceForm({ claimId }: { claimId: string }) {
  const router = useRouter();
  const { session } = useSession();

  const { data, isLoading, error } = useClaimQuery({ input: claimId });
  const { mutateAsync: updateAllowanceClaim } = useUpdateClaimMutation();
  const claimData = data?.claim.claimData;

  const preLoadData: AllowanceForm = {
    from:
      claimData && "from" in claimData && claimData.from
        ? new Date(claimData.from)
        : new Date(),
    to:
      claimData && "to" in claimData && claimData.to
        ? new Date(claimData.to)
        : new Date(),
    purpose: claimData?.purpose || "dawadwdwdadaw",
    workingHours:
      claimData &&
      "workingHours" in claimData &&
      typeof claimData.workingHours === "number"
        ? claimData.workingHours
        : 0,
    amount: claimData?.amount || 0,
    receipts: claimData?.receipts || null,
    user: session?.userId || "",
    claimType: data?.claim.claimType || ClaimType.Allowance, // Assuming default type
  };

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { isValid },
  } = useForm<AllowanceForm>({
    defaultValues: preLoadData,
    resolver: zodResolver(schema),
    mode: "onChange",
  });

  // Handle form submission
  const onSubmit = async (data: AllowanceForm) => {
    try {
      await updateAllowanceClaim({ id: claimId, input: { ...data } });
      successToast("Allowance claim updated successfully!");
      router.replace("/"); // Navigate back to claims list
    } catch (error) {
      errorToast(error);
    }
  };

  // Show loading state while fetching data
  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  // Show error state if data fetch failed
  if (error) {
    return (
      <View style={[styles.container, styles.centered]}>
        {/* You can add an error component here */}
      </View>
    );
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={100}
      style={styles.container}
    >
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* From Date Input */}
        <FormDatePicker
          name={`from`}
          control={control}
          label="From Date"
          placeholder="dd-mm-yy"
        />
        {/* To Date Input */}
        <FormDatePicker
          name={`to`}
          control={control}
          label="To Date"
          placeholder="dd-mm-yy"
        />

        {/* Reason Input */}
        <FormInput
          name={`purpose`}
          control={control}
          label="Purpose"
          placeholder="Type something..."
          multiline
          numberOfLines={3}
        />

        {/* Working Hours Input */}
        <FormInput
          name={`workingHours`}
          control={control}
          label="Working Hours"
          placeholder="Enter Hours"
          keyboardType="numeric"
        />

        {/* Amount Input */}
        <FormInput
          name={`amount`}
          control={control}
          label="Amount (RM)"
          placeholder="Enter Amount"
          keyboardType="numeric"
        />

        {/* Upload receipts */}
        <FormImagePicker
          name={`receipts`}
          control={control}
          label="Upload Receipts"
        />

        {/* Submit Button */}
        <FormSubmitButton
          submitLabel="Update Claim"
          onSubmit={handleSubmit(onSubmit)}
          isValid={isValid}
          style={styles.submitButton}
        />
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 50,
    paddingTop: 16,
  },
  centered: {
    justifyContent: "center",
    alignItems: "center",
  },
  submitButton: {
    marginTop: 20,
  },
});

import React from "react";
import {
  StyleSheet,
  Text,
  TextInput,
  View,
  TextInputProps,
  StyleProp,
  ViewStyle,
} from "react-native";
import { useController, Control, FieldValues, Path } from "react-hook-form";
import { Colors } from "@/constants/Colors";

interface FormInputProps<T extends FieldValues> extends TextInputProps {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  containerStyle?: StyleProp<ViewStyle>;
  rules?: object;
  multiline?: boolean;
  numberOfLines?: number;
}

export function FormInput<T extends FieldValues>({
  name,
  control,
  label,
  containerStyle,
  rules,
  multiline = false,
  numberOfLines = 1,
  ...inputProps
}: FormInputProps<T>) {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
  });

  return (
    <View style={[styles.container, containerStyle]}>
      {label && <Text style={styles.label}>{label}</Text>}
      <TextInput
        style={[
          styles.input,
          multiline && {
            minHeight: 24 * numberOfLines,
            textAlignVertical: "top",
          },
          error && styles.inputError,
        ]}
        value={field.value}
        onChangeText={field.onChange}
        onBlur={field.onBlur}
        multiline={multiline}
        numberOfLines={numberOfLines}
        {...inputProps}
      />
      {error && <Text style={styles.errorText}>{error.message}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.text,
    marginBottom: 8,
    textTransform: "capitalize",
  },
  input: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: Colors.text,
    backgroundColor: Colors.white,
  },
  inputError: {
    borderColor: Colors.error,
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    marginTop: 4,
  },
});

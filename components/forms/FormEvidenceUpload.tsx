import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from "react-native";
import { useController, Control, FieldValues, Path } from "react-hook-form";
import * as ImagePicker from "expo-image-picker";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";

import {
  EvidenceType,
  useCreateSignedUploadUrlMutation,
} from "@/generated/graphql";

interface FormEvidenceUploadProps<T extends FieldValues> {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  evidenceType: EvidenceType;
}

export function FormEvidenceUpload<T extends FieldValues>({
  name,
  control,
  label,
  placeholder = "Upload evidence",
  evidenceType,
}: FormEvidenceUploadProps<T>) {
  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
  });

  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const { mutateAsync: getSignedUrl } = useCreateSignedUploadUrlMutation();

  const uploadFileToS3 = async (
    uri: string,
    fileName: string,
    mimeType: string
  ): Promise<string> => {
    try {
      // Get signed URL
      const data = await getSignedUrl({
        input: {
          key: `uploads/evidence/${Date.now()}-${fileName}`,
          contentType: mimeType,
          expiresIn: 300,
        },
      });

      if (
        !data?.createSignedUploadUrl?.url ||
        !data?.createSignedUploadUrl?.fields
      ) {
        throw new Error("Failed to get upload URL");
      }

      // Create FormData for React Native
      const formData = new FormData();
      const fields = data.createSignedUploadUrl.fields;

      // Add fields in correct order for S3
      formData.append("key", fields.key);
      formData.append("bucket", fields.bucket);
      formData.append("acl", fields.acl);
      formData.append("X-Amz-Algorithm", fields.algorithm);
      formData.append("X-Amz-Credential", fields.credential);
      formData.append("X-Amz-Date", fields.date);
      formData.append("Policy", fields.Policy);
      formData.append("X-Amz-Signature", fields.signature);
      formData.append("Content-Type", mimeType);

      // For React Native, append the file as an object with uri, type, and name
      const fileObject = {
        uri: uri,
        type: mimeType,
        name: fileName,
      };
      formData.append("file", fileObject as any);

      // Upload using XMLHttpRequest with progress tracking
      return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open("POST", data.createSignedUploadUrl.url);

        xhr.upload.addEventListener("progress", (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setUploadProgress(progress);
          }
        });

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            // Construct the final S3 URL
            const s3Url = `https://${fields.bucket}.s3.amazonaws.com/${fields.key}`;
            resolve(s3Url);
          } else {
            reject(new Error("Upload failed"));
          }
        };

        xhr.onerror = () => reject(new Error("Upload failed"));
        xhr.send(formData);
      });
    } catch (error) {
      console.error("Upload failed:", error);
      throw error;
    }
  };

  const handleImagePicker = async () => {
    try {
      setIsUploading(true);
      setUploadProgress(0);

      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "We need access to your gallery to upload evidence."
        );
        setIsUploading(false);
        return;
      }

      // Determine media types based on evidence type
      let mediaTypes: string[];
      if (evidenceType === EvidenceType.Video) {
        mediaTypes = ["videos"];
      } else if (evidenceType === EvidenceType.Image) {
        mediaTypes = ["images"];
      } else {
        mediaTypes = ["images", "videos"];
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: mediaTypes as any,
        allowsEditing: false,
        quality: 0.8,
        videoQuality: ImagePicker.UIImagePickerControllerQualityType.High,
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        const asset = result.assets[0];

        // Prepare file info
        const fileName =
          asset.fileName ||
          `evidence-${Date.now()}.${asset.type === "video" ? "mp4" : "jpg"}`;
        const mimeType =
          asset.mimeType ||
          (asset.type === "video" ? "video/mp4" : "image/jpeg");

        try {
          // Upload to S3
          const s3Url = await uploadFileToS3(asset.uri, fileName, mimeType);

          // Update the form field with S3 URL
          field.onChange(s3Url);

          // Show success message
        } catch (uploadError) {
          console.error("Failed to upload evidence:", uploadError);
        }
      }

      setIsUploading(false);
      setUploadProgress(0);
    } catch (error) {
      console.error("Gallery picker error:", error);
      Alert.alert("Error", "Failed to open gallery. Please try again.");
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleCamera = async () => {
    try {
      setIsUploading(true);
      setUploadProgress(0);

      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "We need camera access to capture evidence."
        );
        setIsUploading(false);
        return;
      }

      // Determine media types based on evidence type
      console.log("Camera - evidenceType:", evidenceType);
      console.log("Camera - EvidenceType.Video:", EvidenceType.Video);
      console.log(
        "Camera - comparison result:",
        evidenceType === EvidenceType.Video
      );

      let mediaTypes: string[];
      if (evidenceType === EvidenceType.Video) {
        mediaTypes = ["videos"];
        console.log("Camera - Using video mode");
      } else {
        mediaTypes = ["images"];
        console.log("Camera - Using image mode");
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: mediaTypes as any,
        allowsEditing: false,
        quality: 0.8,
        videoQuality: ImagePicker.UIImagePickerControllerQualityType.High,
        videoMaxDuration: evidenceType === EvidenceType.Video ? 120 : undefined, // 2 minutes for video evidence
      });

      if (!result.canceled && result.assets && result.assets[0]) {
        const asset = result.assets[0];

        // Prepare file info
        const fileName =
          asset.fileName ||
          `evidence-${Date.now()}.${asset.type === "video" ? "mp4" : "jpg"}`;
        const mimeType =
          asset.mimeType ||
          (asset.type === "video" ? "video/mp4" : "image/jpeg");

        try {
          // Upload to S3
          const s3Url = await uploadFileToS3(asset.uri, fileName, mimeType);

          // Update the form field with S3 URL
          field.onChange(s3Url);
        } catch (uploadError) {
          console.error("Failed to upload evidence:", uploadError);
        }
      }

      setIsUploading(false);
      setUploadProgress(0);
    } catch (error) {
      console.error("Camera error:", error);
      Alert.alert("Error", "Failed to open camera. Please try again.");
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const showUploadOptions = () => {
    console.log("showUploadOptions - evidenceType:", evidenceType);
    console.log("showUploadOptions - EvidenceType.Video:", EvidenceType.Video);
    console.log("showUploadOptions - EvidenceType.Image:", EvidenceType.Image);

    const options: any[] = [];

    if (evidenceType === EvidenceType.Image) {
      options.push(
        {
          text: "Take Photo",
          onPress: () => {
            handleCamera();
          },
        },
        {
          text: "Choose from Gallery",
          onPress: () => {
            handleImagePicker();
          },
        }
      );
    } else if (evidenceType === EvidenceType.Video) {
      options.push(
        {
          text: "Record Video",
          onPress: () => {
            handleCamera();
          },
        },
        {
          text: "Choose from Gallery",
          onPress: () => {
            handleImagePicker();
          },
        }
      );
    } else {
      // For mixed types or documents
      options.push(
        {
          text: "Take Photo/Video",
          onPress: () => {
            handleCamera();
          },
        },
        {
          text: "Choose from Gallery",
          onPress: () => {
            handleImagePicker();
          },
        }
      );
    }

    options.push({ text: "Cancel", style: "cancel" as const });

    const title =
      evidenceType === EvidenceType.Video
        ? "Upload Video Evidence"
        : evidenceType === EvidenceType.Image
        ? "Upload Image Evidence"
        : "Upload Evidence";

    console.log(
      "Showing alert with title:",
      title,
      "and options:",
      options.length
    );
    Alert.alert(title, "Choose upload method:", options);
  };

  const getIconName = () => {
    switch (evidenceType) {
      case EvidenceType.Image:
        return "image-outline";
      case EvidenceType.Video:
        return "videocam-outline";
      case EvidenceType.Document:
        return "document-outline";
      default:
        return "attach-outline";
    }
  };

  const getSuccessMessage = () => {
    switch (evidenceType) {
      case EvidenceType.Video:
        return "Video uploaded successfully";
      case EvidenceType.Image:
        return "Image uploaded successfully";
      case EvidenceType.Document:
        return "Document uploaded successfully";
      default:
        return "Evidence uploaded successfully";
    }
  };

  const isS3Url =
    field.value &&
    typeof field.value === "string" &&
    field.value.startsWith("http");

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}

      <TouchableOpacity
        style={[styles.uploadButton, isS3Url && styles.uploadButtonSuccess]}
        onPress={() => {
          showUploadOptions();
        }}
        disabled={isUploading}
      >
        {isUploading ? (
          <View style={styles.uploadingContainer}>
            <ActivityIndicator size="small" color={Colors.primary} />
            <Text style={styles.uploadingText}>
              Uploading {evidenceType.toLowerCase()}...
            </Text>
            {uploadProgress > 0 && (
              <Text style={styles.progressText}>{uploadProgress}%</Text>
            )}
          </View>
        ) : (
          <View style={styles.uploadContent}>
            <Ionicons
              name={isS3Url ? "checkmark-circle" : getIconName()}
              size={24}
              color={isS3Url ? Colors.success : Colors.primary}
            />
            <Text
              style={[styles.uploadText, isS3Url && styles.uploadTextSuccess]}
            >
              {isS3Url ? getSuccessMessage() : placeholder}
            </Text>
            {isS3Url && evidenceType === EvidenceType.Video && (
              <Text style={styles.videoInfo}>
                📹 Video evidence ready for submission
              </Text>
            )}
            {isS3Url && (
              <Text style={styles.s3UrlInfo}>
                ✅ File stored securely in S3
              </Text>
            )}
          </View>
        )}
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error.message}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
    marginBottom: 8,
  },
  uploadButton: {
    borderWidth: 2,
    borderColor: Colors.border,
    borderStyle: "dashed",
    borderRadius: 12,
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.background,
  },
  uploadButtonSuccess: {
    borderColor: Colors.success,
    backgroundColor: Colors.successLight,
  },
  uploadContent: {
    alignItems: "center",
    gap: 8,
  },
  uploadText: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: "center",
  },
  uploadTextSuccess: {
    color: Colors.success,
    fontWeight: "500",
  },
  uploadingContainer: {
    alignItems: "center",
    gap: 8,
  },
  uploadingText: {
    fontSize: 14,
    color: Colors.primary,
  },
  progressText: {
    fontSize: 12,
    color: Colors.primary,
    fontWeight: "500",
  },
  errorText: {
    fontSize: 12,
    color: Colors.error,
    marginTop: 4,
  },
  videoInfo: {
    fontSize: 12,
    color: Colors.success,
    fontWeight: "500",
    textAlign: "center",
    marginTop: 4,
  },
  s3UrlInfo: {
    fontSize: 12,
    color: Colors.success,
    fontWeight: "500",
    textAlign: "center",
    marginTop: 2,
  },
});

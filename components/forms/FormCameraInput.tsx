import React, { useState } from "react";
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
} from "react-native";
import { Control, useController, Path } from "react-hook-form";
import * as ImagePicker from "expo-image-picker";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";

type Props<T extends Record<string, any>> = {
  name: Path<T>;
  control: Control<T>;
  label?: string;
};

const { height: screenHeight } = Dimensions.get("window");
export function FormCameraInput<T extends Record<string, any>>({
  name,
  control,
  label,
}: Props<T>) {
  const { field } = useController({ name, control });
  const [loading, setLoading] = useState(false);

  const openCamera = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission Denied", "Camera permission is required.");
      return;
    }

    try {
      setLoading(true);
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        base64: true,
        aspect: [1, 1],
        quality: 0.7,
        cameraType: ImagePicker.CameraType.front,
      });

      if (!result.canceled && result.assets?.[0]?.base64) {
        const base64 = `data:image/jpeg;base64,${result.assets[0].base64}`;
        field.onChange(base64);
      }
    } catch (error) {
      console.error("Camera error:", error);
      Alert.alert("Error", "Unable to open camera.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      {label && <Text style={styles.label}>{label}</Text>}

      {field.value ? (
        <Image source={{ uri: field.value }} style={styles.image} />
      ) : (
        <View style={styles.placeholder}>
          <Ionicons name="camera-outline" size={40} color="#999" />
          <Text style={styles.placeholderText}>No image captured</Text>
        </View>
      )}

      <TouchableOpacity style={styles.button} onPress={openCamera}>
        <Text style={styles.buttonText}>
          {loading ? "Opening..." : "Open Camera"}
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
    paddingHorizontal: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
    color: "#444",
  },
  image: {
    width: "100%",
    height: screenHeight * 0.4,
    borderRadius: 8,
    marginBottom: 12,
  },
  placeholder: {
    height: 200,
    borderRadius: 8,
    backgroundColor: "#eee",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 12,
  },
  placeholderText: {
    color: "#888",
    marginTop: 4,
  },
  button: {
    backgroundColor: Colors.primaryDark,
    paddingVertical: 10,
    borderRadius: 6,
    alignItems: "center",
  },
  buttonText: {
    color: "#fff",
    fontWeight: "500",
  },
});

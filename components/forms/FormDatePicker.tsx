import React, { useState } from "react";
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Modal,
  StyleProp,
  ViewStyle,
} from "react-native";
import { useController, Control, FieldValues, Path } from "react-hook-form";
import { Calendar } from "react-native-calendars";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";
import { Button } from "../ui/Button";

interface FormDatePickerProps<T extends FieldValues> {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  placeholder?: string;
  containerStyle?: StyleProp<ViewStyle>;
  rules?: object;
  isRange?: boolean;
  endDateName?: Path<T>;
  minDate?: string;
  maxDate?: string;
}

export function FormDatePicker<T extends FieldValues>({
  name,
  control,
  label,
  placeholder = "Select date",
  containerStyle,
  rules,
  isRange = false,
  endDateName,
  minDate,
  maxDate,
}: FormDatePickerProps<T>) {
  const [modalVisible, setModalVisible] = useState(false);
  const [tempEndDate, setTempEndDate] = useState<string | null>(null);

  const {
    field,
    fieldState: { error },
  } = useController({
    name,
    control,
    rules,
  });

  // Always call useController to avoid React hooks rules violation
  // but only use it when isRange is true
  const endDateController = useController({
    name: (endDateName || name) as Path<T>, // Fallback to name if endDateName is not provided
    control,
  });

  const startDate = field.value;
  // Only use endDateController when isRange is true
  const endDate = isRange ? endDateController.field.value : null;

  const formatDate = (date: string | null) => {
    if (!date) return placeholder;
    const options: Intl.DateTimeFormatOptions = {
      year: "numeric",
      month: "long",
      day: "numeric",
    };
    return new Date(date).toLocaleDateString("en-US", options);
  };

  const handleDayPress = (day: { dateString: string }) => {
    const selectedDate = day.dateString;

    if (!isRange) {
      field.onChange(selectedDate);
      // For half-day leave, we set the same date for both start and end
      if (endDateName) {
        endDateController.field.onChange(selectedDate);
      }
      setModalVisible(false);
      return;
    }

    if (!startDate || (startDate && endDate)) {
      // Start new selection
      field.onChange(selectedDate);
      endDateController.field.onChange(null);
      setTempEndDate(null);
    } else {
      // Complete the selection
      const start = new Date(startDate);
      const end = new Date(selectedDate);

      if (end < start) {
        // If end date is before start date, swap them
        field.onChange(selectedDate);
        endDateController.field.onChange(startDate);
      } else {
        setTempEndDate(selectedDate);
      }
    }
  };

  const confirmDateRange = () => {
    if (isRange) {
      if (tempEndDate) {
        endDateController.field.onChange(tempEndDate);
      } else if (startDate && !endDate) {
        // If only start date is selected, set end date same as start date
        endDateController.field.onChange(startDate);
      }
    }
    setModalVisible(false);
  };

  const getMarkedDates = () => {
    const markedDates: { [date: string]: any } = {};

    if (startDate) {
      markedDates[startDate] = {
        selected: true,
        startingDay: true,
        color: Colors.primary,
      };

      const displayEndDate = tempEndDate || endDate;

      if (displayEndDate) {
        markedDates[displayEndDate] = {
          selected: true,
          endingDay: true,
          color: Colors.primary,
        };

        // Mark dates in between
        if (startDate !== displayEndDate) {
          const start = new Date(startDate);
          const end = new Date(displayEndDate);

          for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
            const dateString = d.toISOString().split("T")[0];

            if (dateString !== startDate && dateString !== displayEndDate) {
              markedDates[dateString] = {
                selected: true,
                color: Colors.primary,
              };
            }
          }
        }
      }
    }

    return markedDates;
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && <Text style={styles.label}>{label}</Text>}
      <TouchableOpacity
        style={[styles.dateSelector, error && styles.dateSelectorError]}
        onPress={() => setModalVisible(true)}
      >
        <Text style={styles.dateText}>
          {startDate
            ? isRange && endDate
              ? `${formatDate(startDate)} - ${formatDate(endDate)}`
              : formatDate(startDate)
            : placeholder}
        </Text>
        <Ionicons name="calendar-outline" size={20} color={Colors.primary} />
      </TouchableOpacity>
      {error && <Text style={styles.errorText}>{error.message}</Text>}

      <Modal
        visible={modalVisible}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <TouchableOpacity
            style={styles.modalBackground}
            onPress={() => setModalVisible(false)}
            activeOpacity={1}
          />
          <View style={styles.calendarContainer}>
            <View style={styles.calendarHeader}>
              <Text style={styles.calendarTitle}>
                {isRange ? "Select Date Range" : "Select Date"}
              </Text>
              <TouchableOpacity
                onPress={() => setModalVisible(false)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons name="close" size={24} color={Colors.text} />
              </TouchableOpacity>
            </View>

            <Calendar
              onDayPress={handleDayPress}
              markedDates={getMarkedDates()}
              markingType="period"
              minDate={minDate || undefined}
              maxDate={maxDate || undefined}
              theme={{
                calendarBackground: Colors.white,
                textSectionTitleColor: Colors.textSecondary,
                selectedDayBackgroundColor: Colors.primary,
                selectedDayTextColor: Colors.white,
                todayTextColor: Colors.primary,
                dayTextColor: Colors.text,
                textDisabledColor: Colors.textLight,
                dotColor: Colors.primary,
                selectedDotColor: Colors.white,
                arrowColor: Colors.primary,
                monthTextColor: Colors.text,
                indicatorColor: Colors.primary,
              }}
            />

            <View style={styles.calendarFooter}>
              <Button
                title={
                  startDate && !endDate && isRange
                    ? "Select End Date"
                    : "Confirm Selection"
                }
                onPress={
                  isRange ? confirmDateRange : () => setModalVisible(false)
                }
                disabled={!startDate}
                icon="checkmark-outline"
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    color: Colors.text,
    marginBottom: 8,
  },
  dateSelector: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    padding: 12,
    backgroundColor: Colors.white,
  },
  dateSelectorError: {
    borderColor: Colors.error,
  },
  dateText: {
    fontSize: 16,
    color: Colors.text,
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    marginTop: 4,
  },
  modalOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    zIndex: 1000,
  },
  modalBackground: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.5)",
  },
  calendarContainer: {
    width: "90%",
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  calendarHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  calendarTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.text,
  },
  calendarFooter: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
});

import React from "react";
import { StyleSheet, View, StyleProp, ViewStyle } from "react-native";
import { Button } from "../ui/Button";
import { Colors } from "@/constants/Colors";

interface FormSubmitButtonProps {
  submitLabel: string;
  cancelLabel?: string;
  onSubmit: () => void;
  onCancel?: () => void;
  isSubmitting?: boolean;
  isValid?: boolean;
  style?: StyleProp<ViewStyle>;
  submitButtonStyle?: StyleProp<ViewStyle>;
  submitIcon?: string;
  cancelIcon?: string;
}

export function FormSubmitButton({
  submitLabel,
  cancelLabel,
  onSubmit,
  onCancel,
  isSubmitting = false,
  isValid = true,
  style,
  submitButtonStyle,
  submitIcon = "paper-plane-outline",
  cancelIcon = "close-outline",
}: FormSubmitButtonProps) {
  return (
    <View style={[styles.buttonGroup, style]}>
      {cancelLabel && onCancel && (
        <Button
          title={cancelLabel}
          variant="outline"
          onPress={onCancel}
          icon={cancelIcon}
          disabled={isSubmitting}
          fullWidth
        />
      )}
      <Button
        title={submitLabel}
        onPress={onSubmit}
        icon={isSubmitting ? undefined : submitIcon}
        disabled={!isValid || isSubmitting}
        style={[
          submitButtonStyle,
          { backgroundColor: isValid ? Colors.primaryDark : Colors.diabledBg },
        ]}
        loading={isSubmitting}
        fullWidth
      />
    </View>
  );
}

const styles = StyleSheet.create({
  buttonGroup: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 20,
    gap: 8,
  },
});

// Shared types and interfaces for attendance components

// Define attendance status types
export enum AttendanceStatus {
  ONTIME = "ontime",
  LATE = "late",
  ABSENT = "absent",
  FUTURE = "future",
}

// Define attendance data structure
export interface DailyAttendance {
  day: string;
  records: AttendanceStatus[];
}

// Define attendance summary data structure
export interface AttendanceSummary {
  ontime: number;
  late: number;
  absent: number;
}

// Type for the GraphQL attendance data (matching the generated types)
export interface GraphQLAttendance {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  date: Date;
  endTime?: Date | null;
  overTime?: Date | null;
  overTimeSpentInMinutes: number;
  startTime: Date;
  timeSpentInMinutes: number;
  actualClockInTime: Date;
  actualClockOutTime?: Date | null;
  shift?: {
    startDateTime: Date;
    endDateTime: Date;
  } | null;
  location?: {
    id: string;
    name: string;
  } | null;
}

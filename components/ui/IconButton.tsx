import React from "react";
import { StyleSheet, TouchableOpacity, Text, View } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Link } from "expo-router";
import { Colors } from "@/constants/Colors";

interface IconButtonProps {
  icon: keyof typeof Ionicons.glyphMap;
  label: string;
  href: string;
  color?: string;
  onPress?: () => void;
  iconSize?: number;
  itemWidth?: number;
}

/**
 * Icon button component for navigation menu
 */
export function IconButton({
  icon,
  label,
  href,
  color = Colors.primary,
  onPress,
  iconSize = 22,
  itemWidth,
}: IconButtonProps) {
  // Handle both navigation and custom press animation
  const handlePress = () => {
    if (onPress) {
      onPress();
    }
  };

  // Calculate container width based on itemWidth prop if provided
  const containerStyle = [
    styles.container,
    itemWidth ? { width: itemWidth } : null,
  ];

  // Calculate icon container size based on iconSize
  const iconContainerSize = iconSize * 2;
  const iconContainerStyle = [
    styles.iconContainer,
    {
      backgroundColor: hexToRGBA(color, 0.1),
      width: iconContainerSize,
      height: iconContainerSize,
      borderRadius: iconContainerSize / 2,
    },
  ];

  return (
    <Link href={href as any} asChild>
      <TouchableOpacity
        style={containerStyle}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <View style={iconContainerStyle}>
          <Ionicons name={icon} size={iconSize} color={color} />
        </View>
        <Text style={styles.label} numberOfLines={2} ellipsizeMode="tail">
          {label}
        </Text>
      </TouchableOpacity>
    </Link>
  );
}

/**
 * Helper function to convert hex to rgba
 */
function hexToRGBA(hex: string, alpha: number): string {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    padding: 4, // Reduced padding to allow for better spacing between items
    // Width is now dynamically calculated and passed as prop
  },
  iconContainer: {
    // Base styles - size is dynamically calculated
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 6,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.03)",
  },
  label: {
    fontSize: 12,
    fontWeight: "500",
    color: Colors.text,
    textAlign: "center",
    width: "100%",
    marginTop: 4,
    height: 32, // Increased height to accommodate 2 lines
    paddingHorizontal: 2, // Add a bit of padding for better text display
  },
});

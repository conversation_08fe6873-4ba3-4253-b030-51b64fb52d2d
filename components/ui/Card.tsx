import React from "react";
import { StyleSheet, View, ViewProps } from "react-native";
import { Colors } from "@/constants/Colors";

interface CardProps extends ViewProps {
  children: React.ReactNode;
}

/**
 * Card component with shadow and rounded corners
 */
export function Card({ children, style, ...props }: CardProps) {
  return (
    <View style={[styles.card, style]} {...props}>
      {children}
    </View>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: Colors.cardBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
});

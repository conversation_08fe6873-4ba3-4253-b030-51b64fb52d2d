import React from "react";
import {
  StyleSheet,
  Text,
  TouchableOpacity,
  StyleProp,
  ViewStyle,
  TextStyle,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";

type ButtonVariant = "primary" | "secondary" | "outline";

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: ButtonVariant;
  icon?: string;
  disabled?: boolean;
  loading?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  fullWidth?: boolean;
}

export function Button({
  title,
  onPress,
  variant = "primary",
  icon,
  disabled = false,
  loading = false,
  style,
  textStyle,
  fullWidth = false,
}: ButtonProps) {
  // Get button styles based on variant
  const getButtonStyles = () => {
    switch (variant) {
      case "secondary":
        return {
          button: [
            styles.button,
            styles.secondaryButton,
            disabled && styles.disabledButton,
          ],
          text: [
            styles.buttonText,
            styles.secondaryButtonText,
            disabled && styles.disabledButtonText,
          ],
        };
      case "outline":
        return {
          button: [
            styles.button,
            styles.outlineButton,
            disabled && styles.disabledOutlineButton,
          ],
          text: [
            styles.buttonText,
            styles.outlineButtonText,
            disabled && styles.disabledButtonText,
          ],
        };
      case "primary":
      default:
        return {
          button: [
            styles.button,
            styles.primaryButton,
            disabled && styles.disabledButton,
          ],
          text: [
            styles.buttonText,
            styles.primaryButtonText,
            disabled && styles.disabledButtonText,
          ],
        };
    }
  };

  const buttonStyles = getButtonStyles();
  const iconColor = variant === "primary" ? Colors.white : Colors.primary;

  return (
    <TouchableOpacity
      style={[
        buttonStyles.button,
        fullWidth && styles.fullWidth,
        style,
      ]}
      onPress={onPress}
      disabled={disabled || loading}
      activeOpacity={0.8}
    >
      {loading ? (
        <ActivityIndicator
          size="small"
          color={variant === "outline" ? Colors.primary : Colors.white}
        />
      ) : (
        <>
          {icon && (
            <Ionicons
              name={icon as any}
              size={20}
              color={disabled ? Colors.textLight : iconColor}
              style={styles.buttonIcon}
            />
          )}
          <Text style={[buttonStyles.text, textStyle]}>{title}</Text>
        </>
      )}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    minWidth: 120,
  },
  fullWidth: {
    flex: 1,
  },
  primaryButton: {
    backgroundColor: Colors.primary,
  },
  secondaryButton: {
    backgroundColor: Colors.secondary,
  },
  outlineButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  disabledButton: {
    backgroundColor: Colors.lightGray,
  },
  disabledOutlineButton: {
    borderColor: Colors.textLight,
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "600",
  },
  primaryButtonText: {
    color: Colors.white,
  },
  secondaryButtonText: {
    color: Colors.white,
  },
  outlineButtonText: {
    color: Colors.primary,
  },
  disabledButtonText: {
    color: Colors.textLight,
  },
  buttonIcon: {
    marginRight: 8,
  },
});

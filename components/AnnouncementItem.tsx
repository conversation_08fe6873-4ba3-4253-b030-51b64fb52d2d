import { Colors } from "@/constants/Colors";
import { format } from "date-fns";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View, Image } from "react-native";
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withSequence,
  withTiming,
} from "react-native-reanimated";
import { extractInitials } from "@/lib/utils";

// Extended interface to include createdBy field until GraphQL types are updated
interface AnnouncementWithCreatedBy {
  id: string;
  title: string;
  description: string;
  date: Date;
  userRoles?: Array<any> | null;
  document?: string | null;
  createdBy?: {
    id: string;
    fullname: string;
    role: string;
    profilePicture?: string | null;
  };
}

export interface AnnouncementItemProps {
  announcement: AnnouncementWithCreatedBy;
  onPress: (announcement: AnnouncementWithCreatedBy) => void;
}

/**
 * Announcement item component for the announcement list with animations
 */
export function AnnouncementItem({
  onPress,
  announcement,
}: AnnouncementItemProps) {
  const { date, description, id, createdBy } = announcement;

  // Animation values
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);

  // Handle press animation
  const handlePress = () => {
    // Scale animation
    scale.value = withSequence(
      withTiming(0.98, { duration: 100, easing: Easing.inOut(Easing.quad) }),
      withTiming(1, { duration: 200, easing: Easing.out(Easing.quad) })
    );

    // Subtle horizontal movement
    translateX.value = withSequence(
      withTiming(5, { duration: 100 }),
      withTiming(0, { duration: 200, easing: Easing.out(Easing.quad) })
    );

    // Call the onPress callback
    onPress(announcement);
  };

  // Animated style
  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }, { translateX: translateX.value }],
  }));

  return (
    <Animated.View style={animatedStyle} key={id}>
      <TouchableOpacity
        style={styles.container}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        {/* Profile Picture or Initials */}
        <View style={styles.avatarContainer}>
          {createdBy?.profilePicture ? (
            <Image
              source={{ uri: createdBy.profilePicture }}
              style={styles.avatar}
              resizeMode="cover"
            />
          ) : (
            <View style={styles.initialsContainer}>
              <Text style={styles.initials}>
                {extractInitials(createdBy?.fullname || "Unknown")}
              </Text>
            </View>
          )}
        </View>

        <View style={styles.contentContainer}>
          <View style={styles.header}>
            <Text style={styles.senderName}>
              {createdBy?.fullname || "Unknown User"}
            </Text>
            <Text style={styles.time}>{format(date, "hh:mm a")}</Text>
          </View>

          <Text style={styles.message} numberOfLines={2}>
            {description}
          </Text>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
  },
  avatarContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
    overflow: "hidden",
  },
  avatar: {
    width: "100%",
    height: "100%",
  },
  initialsContainer: {
    width: "100%",
    height: "100%",
    backgroundColor: Colors.primaryLight,
    justifyContent: "center",
    alignItems: "center",
  },
  initials: {
    fontSize: 18,
    fontWeight: "bold",
    color: Colors.white,
  },
  contentContainer: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 4,
  },
  senderName: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
    textTransform: "capitalize",
  },
  time: {
    fontSize: 12,
    color: Colors.textLight,
  },
  message: {
    fontSize: 14,
    color: Colors.textSecondary,
    lineHeight: 20,
  },
});

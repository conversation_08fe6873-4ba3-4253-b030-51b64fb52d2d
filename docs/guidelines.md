## 🧠 Developer Context

**Role**: Senior React Native Developer  
**Principles**: Clean, concise, modular, DRY, maintainable, debug-friendly

---

## 📁 Project Structure Guidelines

Each module follows this structure:

/src/modules/{ModuleName}/
├── components/ # Plug-and-play logic-heavy UI components
├── elements/ # Reusable small UI units (e.g., Button, Input)
├── forms/ # Forms with validation logic (Formik/Yup etc.)
├── modals/ # Modals scoped to this module
├── charts/ # Chart components (if needed)
├── data-tables/ # Tables or list-based UIs
└── utils/ # Module-specific utilities or transformers

**Other folders:**

/src/common/ # Global UI elements used across modules
/src/lib/ # Services, clients, helpers, configs

---

## 🔠 Component & Code Practices

✅ **Component Rules**

- Use `types` (not `interfaces`) for props and inputs.
- Components should be modular and pluggable.
- Reuse components across modules—don’t duplicate logic.
- Use subtle animations (with Reanimated).
- Prefer `elevation` or `borders` over `shadows` (no shadows allowed).
- If folder (`components`, `elements`, etc.) does **not** exist, create it as needed.
- Keep element logic minimal—delegate heavy-lifting to parent components.

🧩 **Component Naming & Access**

- Use namespaced component access for clarity and maintainability.
- Example usage:

tsx
<UserForms.CreateForm />
<UserModals.ConfirmDeleteModal />
<AttendanceCharts.DailySummary />

- This pattern keeps module usage predictable and easy to debug.

📦 **Utility Functions**

- Export all **data transformer** functions from `utils/` or relevant folders.
- Transformers accept raw data and return UI-ready data.

🧹 **Code Style**

- Clean, minimal, readable.
- Comment **only when necessary** (e.g., non-obvious logic).
- Follow DRY, SoC (Separation of Concerns), and other clean code principles.
- No "comment pollution".

---

## 🤊 Animations & UI

- Use **Reanimated** for animations (e.g., transitions, subtle feedback).
- All animations must be **non-blocking** and **non-intrusive**.
- Avoid using visual noise like shadows. Use `elevation` or `border` instead.

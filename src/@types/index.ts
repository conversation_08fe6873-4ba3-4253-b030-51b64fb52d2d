import { FieldValues, SubmitHandler, UseFormReturn } from 'react-hook-form';

export type Form<T> = {
  name: string;
  placeholder?: string;
  label?: string;
  type?: React.HTMLInputTypeAttribute | undefined;
} & T;

export type FormComponentProps<T extends FieldValues> = {
  methods: UseFormReturn<T>;
  onSubmit: SubmitHandler<T>;
};
export type UserStatuses = 'active' | 'inactive';
export type UserRoles =
  | 'admin'
  | 'operations-admin'
  | 'operations-manager'
  | 'hr-admin'
  | 'local-guard'
  | 'nepal-guard'
  | 'buffer-guard';
export type Gender = 'male' | 'female';
export type MaritalStatus = 'un-married' | 'married';
export type UserStatus = 'active' | 'inactive';

import { createUnionType } from '@nestjs/graphql';
import { AllowanceClaim } from '../entities/claims.entity';
import { ExpenseClaim } from '../entities/claims.entity';
import { TravelClaim } from '../entities/claims.entity';
import { SiteClaim, Claims } from '../entities/claims.entity';

// Create the union type
export const ClaimUnion = createUnionType({
  name: 'ClaimUnion', // name of the union
  types: () => [AllowanceClaim, TravelClaim, ExpenseClaim, SiteClaim] as const, // the types for the union
  resolveType(value: Claims) {
    if ('workingHours' in value) {
      return AllowanceClaim;
    }
    if ('distance' in value) {
      return TravelClaim;
    }
    if ('date' in value) {
      return ExpenseClaim;
    }
    if ('site' in value) {
      return SiteClaim;
    }
    return null;
  },
});

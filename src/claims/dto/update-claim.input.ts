import { InputType, Field, PartialType } from '@nestjs/graphql';
import { ClaimStatus, ClaimType } from '../entities/claims.entity';
import { ClaimInput } from './create-claim.input';

@InputType()
export class UpdateClaimInput extends PartialType(ClaimInput) {
  @Field(() => ClaimType)
  claimType: ClaimType;

  @Field(() => ClaimStatus, {
    nullable: true,
    description: 'Updated claim status',
  })
  status?: ClaimStatus;

  @Field(() => String, {
    nullable: true,
    description: 'User ID of the person who processed the claim',
  })
  processedBy?: string;

  @Field(() => String, {
    nullable: true,
    description: 'Reason for rejection, if applicable',
  })
  rejectedReason?: string;
}

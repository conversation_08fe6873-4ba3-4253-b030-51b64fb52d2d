import { InputType, Field } from '@nestjs/graphql';
import { ClaimType } from '../entities/claims.entity';

@InputType()
export class FindClaimsInput {
  @Field(() => String, { nullable: true })
  user?: string;

  @Field(() => ClaimType, { nullable: true })
  claimType?: ClaimType;

  @Field(() => String, { nullable: true })
  status?: string;

  @Field(() => Date, { nullable: true })
  from?: Date;

  @Field(() => Date, { nullable: true })
  to?: Date;
}

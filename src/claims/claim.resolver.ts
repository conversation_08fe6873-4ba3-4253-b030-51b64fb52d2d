import {
  Resolver,
  Mu<PERSON>,
  Args,
  Query,
  ResolveField,
  Parent,
  Context,
} from '@nestjs/graphql';
import { ClaimService } from './claim.service';
import { Claims } from './entities/claims.entity';
import { ClaimInput } from './dto/create-claim.input';
import { FindClaimsInput } from './dto/find-claims.input';
import { UpdateClaimInput } from './dto/update-claim.input';
import { User } from '../users/entities/user.entity';
import { GqlContext } from 'src/app.module';

@Resolver(() => Claims)
export class ClaimResolver {
  constructor(private readonly claimsService: ClaimService) {}

  @ResolveField(() => User, { name: 'user' })
  getUser(@Parent() claim: Claims, @Context() context: GqlContext) {
    return context.loaders.usersLoader.load(claim.user);
  }

  @Mutation(() => Claims)
  createClaim(@Args('input') input: ClaimInput): Promise<Claims> {
    return this.claimsService.create(input);
  }

  @Query(() => [Claims], { name: 'claims' })
  findAll(
    @Args('filter', { type: () => FindClaimsInput, nullable: true })
    filter?: FindClaimsInput,
  ) {
    return this.claimsService.findAll(filter);
  }

  @Query(() => Claims, { name: 'claim' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.claimsService.findOne({ _id: id });
  }

  @Mutation(() => Claims)
  updateClaim(
    @Args('id') id: string,
    @Args('updateClaimInput') updateClaimInput: UpdateClaimInput,
  ) {
    return this.claimsService.update(id, updateClaimInput);
  }
}

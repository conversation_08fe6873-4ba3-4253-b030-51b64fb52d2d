import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ClaimService } from './claim.service';
import { ClaimResolver } from './claim.resolver';
import { Claims, ClaimsSchema } from './entities/claims.entity';
import { User, UserSchema } from 'src/users/entities/user.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Claims.name, schema: ClaimsSchema },
      { name: User.name, schema: UserSchema },
    ]),
  ],
  providers: [ClaimService, ClaimResolver],
})
export class ClaimsModule {}

import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import {
  ClaimType,
  Claims,
  AllowanceClaim,
  TravelClaim,
  ExpenseClaim,
  SiteClaim,
} from './entities/claims.entity';
import { ClaimInput } from './dto/create-claim.input';
import { FindClaimsInput } from './dto/find-claims.input';
import { UpdateClaimInput } from './dto/update-claim.input';

@Injectable()
export class ClaimService {
  constructor(
    @InjectModel('Claims')
    private claimsModel: Model<Claims>,
  ) {}
  async create(input: ClaimInput): Promise<Claims> {
    const { claimType, user, ...commonFields } = input;
    let claimData: AllowanceClaim | TravelClaim | ExpenseClaim | SiteClaim;
    switch (claimType) {
      case ClaimType.ALLOWANCE:
        claimData = {
          ...commonFields,
          from: input?.from,
          to: input?.to,
          workingHours: input?.workingHours,
          receipts: input?.receipts,
        } as AllowanceClaim;
        break;
      case ClaimType.TRAVEL:
        claimData = {
          ...commonFields,
          from: input?.from,
          to: input?.to,
          client: input?.client,
          toll: input?.toll,
          distance: input?.distance,
          receipts: input?.receipts,
        } as TravelClaim;
        break;
      case ClaimType.EXPENSE:
        claimData = {
          ...commonFields,
          items: input?.items,
          date: input?.date,
          receipts: input?.receipts,
        } as ExpenseClaim;
        break;
      case ClaimType.SITE:
        claimData = {
          ...commonFields,
          site: input?.site,
          items: input?.items,
          receipts: input?.receipts,
        } as SiteClaim;
        break;
      default:
        throw new Error('Invalid claim type');
    }

    return await this.claimsModel.create({
      user,
      claimType,
      claimData,
    });
  }

  async findAll(filter: FindClaimsInput | undefined): Promise<Claims[]> {
    const query: Record<string, any> = {};

    if (filter?.user) {
      query.user = filter.user;
    }

    if (filter?.claimType) {
      query.claimType = filter.claimType;
    }

    if (filter?.status) {
      query.status = filter.status;
    }

    return await this.claimsModel.find(query);
  }

  findOne(filter: FilterQuery<Claims>) {
    return this.claimsModel.findOne(filter);
  }

  async update(
    id: string,
    updateClaimInput: UpdateClaimInput,
  ): Promise<Claims> {
    const { claimType, status, processedBy, rejectedReason, ...commonFields } =
      updateClaimInput;

    // Find the existing claim to merge with
    const existingClaim = await this.claimsModel.findById(id);
    if (!existingClaim) {
      throw new Error(`Claim with id ${id} not found`);
    }

    let claimData: AllowanceClaim | TravelClaim | ExpenseClaim | SiteClaim =
      existingClaim.claimData;

    // Update specific fields based on claim type
    switch (claimType) {
      case ClaimType.ALLOWANCE:
        claimData = claimData as AllowanceClaim;
        claimData = {
          ...claimData,
          ...commonFields,
          from: updateClaimInput?.from ?? claimData?.from,
          to: updateClaimInput?.to ?? claimData?.to,
          workingHours:
            updateClaimInput?.workingHours ?? claimData?.workingHours,
          receipts: updateClaimInput?.receipts ?? claimData?.receipts,
        };
        break;
      case ClaimType.TRAVEL:
        claimData = claimData as TravelClaim;
        claimData = {
          ...claimData,
          ...commonFields,
          from: updateClaimInput?.from ?? claimData?.from,
          to: updateClaimInput?.to ?? claimData?.to,
          client: updateClaimInput?.client ?? claimData?.client,
          toll: updateClaimInput?.toll ?? claimData?.toll,
          distance: updateClaimInput?.distance ?? claimData?.distance,
          receipts: updateClaimInput?.receipts ?? claimData?.receipts,
        };
        break;
      case ClaimType.EXPENSE:
        claimData = claimData as ExpenseClaim;
        claimData = {
          ...claimData,
          ...commonFields,
          items: updateClaimInput?.items ?? claimData?.items,
          date: updateClaimInput?.date ?? claimData?.date,
          receipts: updateClaimInput?.receipts ?? claimData?.receipts,
        };
        break;
      case ClaimType.SITE:
        claimData = claimData as SiteClaim;
        claimData = {
          ...claimData,
          ...commonFields,
          site: updateClaimInput?.site ?? claimData?.site,
          items: updateClaimInput?.items ?? claimData?.items,
          receipts: updateClaimInput?.receipts ?? claimData?.receipts,
        };
        break;
      default:
        throw new Error('Invalid claim type');
    }

    const updatePayload: Partial<Claims> = {
      claimType,
      claimData: claimData,
    };
    if (status !== undefined) updatePayload.status = status;
    if (processedBy !== undefined) updatePayload.processedBy = processedBy;
    if (rejectedReason !== undefined)
      updatePayload.rejectedReason = rejectedReason;

    const updatedClaim = await this.claimsModel.findByIdAndUpdate(
      id,
      { $set: updatePayload },
      { new: true },
    );

    if (!updatedClaim) {
      throw new Error(`Failed to update claim with id ${id}`);
    }
    return updatedClaim;
  }
}

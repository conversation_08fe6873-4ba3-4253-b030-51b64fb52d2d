import { Injectable, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { config, database, up } from 'migrate-mongo';

@Injectable()
export class MigrationService implements OnModuleInit {
  private readonly dbMigrationConfig: config.Config;

  constructor(private configService: ConfigService) {
    this.dbMigrationConfig = {
      mongodb: {
        databaseName: this.configService.get('DATABASE_NAME')!,
        url: this.configService.get('DATABASE_URI')!,
      },
      migrationsDir: `${__dirname}/../../migrations`,
      changelogCollectionName: 'migrations',
      migrationFileExtension: '.js',
    };
  }
  async onModuleInit() {
    console.log('Checking for migrations');

    config.set(this.dbMigrationConfig);
    const { client, db } = await database.connect();
    await up(db, client);
  }
}

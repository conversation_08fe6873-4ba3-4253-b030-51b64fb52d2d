import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { UserProfilesService } from './user-profiles.service';
import { UserProfile } from './entities/user-profile.entity';
import { CreateUserProfileInput } from './dto/create-user-profile.input';
import { UpdateUserProfileInput } from './dto/update-user-profile.input';

@Resolver(() => UserProfile)
export class UserProfilesResolver {
  constructor(private readonly userProfilesService: UserProfilesService) {}

  @Mutation(() => UserProfile)
  createUserProfile(
    @Args('createUserProfileInput')
    createUserProfileInput: CreateUserProfileInput,
  ) {
    return this.userProfilesService.create(createUserProfileInput);
  }

  @Query(() => [UserProfile], { name: 'userProfiles' })
  findAll() {
    return this.userProfilesService.findAll();
  }

  @Query(() => UserProfile, { name: 'userProfile' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.userProfilesService.findOne({ user: id });
  }

  @Mutation(() => UserProfile)
  updateUserProfile(
    @Args('id') id: string,
    @Args('updateUserProfileInput')
    updateUserProfileInput: UpdateUserProfileInput,
  ) {
    return this.userProfilesService.update(id, updateUserProfileInput);
  }

  @Mutation(() => UserProfile)
  removeUserProfile(@Args('id', { type: () => String }) id: string) {
    return this.userProfilesService.remove(id);
  }
}

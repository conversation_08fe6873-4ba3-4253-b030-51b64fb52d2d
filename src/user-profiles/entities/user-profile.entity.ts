import { ObjectType, Field } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { User } from 'src/users/entities/user.entity';
import * as mongoose from 'mongoose';
import { MongooseSchema } from 'src/common/common.entity';

@ObjectType()
export class Contact {
  @Field(() => String)
  @Prop({ required: true, default: '+60' })
  countryCode: string;

  @Field(() => String)
  @Prop({ required: true })
  phone: string;
}

@ObjectType()
export class EmergencyContact {
  @Field(() => String)
  @Prop()
  name: string;

  @Field(() => String)
  @Prop()
  relation: string;

  @Field(() => Contact)
  @Prop({ type: Contact })
  contact: Contact;
}

@ObjectType()
@Schema({ collection: 'user-profile' })
export class UserProfile extends MongooseSchema {
  @Field(() => String)
  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true })
  user: User;

  @Field(() => String, { nullable: true })
  @Prop()
  ic?: string;

  @Field(() => String, { nullable: true })
  @Prop()
  ID?: string;

  @Field(() => String, { nullable: true })
  @Prop()
  passport?: string;

  @Field(() => Date, { nullable: true })
  @Prop()
  passportExpiresAt?: Date;

  @Field(() => String, { nullable: true })
  @Prop()
  permitNumber?: string;

  @Field(() => Date, { nullable: true })
  @Prop()
  permitExpiresAt?: Date;

  @Field(() => String, { nullable: true })
  @Prop()
  gender?: string;

  @Field(() => Date, { nullable: true })
  @Prop()
  dob?: Date;

  @Field(() => String, { nullable: true })
  @Prop()
  placeOfBirth?: string;

  @Field(() => String, { nullable: true })
  @Prop()
  currentAddress?: string;

  @Field(() => Date, { nullable: true })
  @Prop()
  joinedAt?: Date;

  @Field(() => String, { nullable: true })
  @Prop()
  maritalStatus?: string;

  @Field(() => String, { nullable: true })
  @Prop()
  bankAccNumber?: string;

  @Field(() => String, { nullable: true })
  @Prop()
  bankName?: string;

  @Field(() => [EmergencyContact], { nullable: true })
  @Prop({ type: [EmergencyContact] })
  emergencyContact?: EmergencyContact[];
}

export const UserProfileSchema = SchemaFactory.createForClass(UserProfile);

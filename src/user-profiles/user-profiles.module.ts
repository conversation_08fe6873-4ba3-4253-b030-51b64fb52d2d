import { Module } from '@nestjs/common';
import { UserProfilesService } from './user-profiles.service';
import { UserProfilesResolver } from './user-profiles.resolver';
import { UserProfileSchema, UserProfile } from './entities/user-profile.entity';
import { MongooseModule } from '@nestjs/mongoose';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserProfile.name, schema: UserProfileSchema },
    ]),
  ],
  providers: [UserProfilesResolver, UserProfilesService],
  exports: [UserProfilesService],
})
export class UserProfilesModule {}

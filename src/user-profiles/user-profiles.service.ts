import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery } from 'mongoose';
import { CreateUserProfileInput } from './dto/create-user-profile.input';
import { UpdateUserProfileInput } from './dto/update-user-profile.input';
import { UserProfile } from './entities/user-profile.entity';

@Injectable()
export class UserProfilesService {
  constructor(
    @InjectModel(UserProfile.name)
    private readonly userProfileModel: Model<UserProfile>,
  ) {}

  async create(
    createUserProfileInput: CreateUserProfileInput,
  ): Promise<UserProfile> {
    const profile = new this.userProfileModel(createUserProfileInput);
    return profile.save();
  }

  async findAll(filter?: FilterQuery<UserProfile>): Promise<UserProfile[]> {
    return this.userProfileModel.find(filter || {}).exec();
  }

  async findOne(filter: FilterQuery<UserProfile>): Promise<UserProfile> {
    let profile = await this.userProfileModel.findOne(filter).exec();

    if (!profile && filter.user) {
      // Create new profile if user ID is provided
      profile = await this.userProfileModel.create({
        user: filter.user as string,
      });
    } else if (!profile) {
      throw new NotFoundException(`User profile not found`);
    }

    return profile;
  }

  async update(
    id: string,
    updateUserProfileInput: UpdateUserProfileInput,
  ): Promise<UserProfile> {
    const result = await this.userProfileModel
      .updateOne(
        { user: id },
        { $set: updateUserProfileInput },
        {
          upsert: true,
          new: true,
          setDefaultsOnInsert: true,
        },
      )
      .exec();

    if (!result.acknowledged) {
      throw new NotFoundException(
        `Failed to update user profile with ID "${id}"`,
      );
    }

    // Fetch the updated/created document
    const profile = await this.userProfileModel.findOne({ user: id }).exec();
    if (!profile) {
      throw new NotFoundException(`User profile with ID "${id}" not found`);
    }

    return profile;
  }

  async remove(id: string): Promise<boolean> {
    const result = await this.userProfileModel.findByIdAndDelete(id).exec();

    if (!result) {
      throw new NotFoundException(`User profile with ID "${id}" not found`);
    }

    return true;
  }
}

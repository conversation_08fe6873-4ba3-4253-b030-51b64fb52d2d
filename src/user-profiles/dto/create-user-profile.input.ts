import { Field, InputType } from '@nestjs/graphql';
import { Type } from 'class-transformer';
import {
  IsDate,
  IsMongoId,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';

@InputType()
class ContactInput {
  @Field(() => String)
  @IsString()
  countryCode: string;

  @Field(() => String)
  @IsString()
  phone: string;
}

@InputType()
class EmergencyContactInput {
  @Field(() => String)
  @IsString()
  name: string;

  @Field(() => String)
  @IsString()
  relation: string;

  @Field(() => ContactInput)
  @ValidateNested()
  @Type(() => ContactInput)
  contact: ContactInput;
}

@InputType()
export class CreateUserProfileInput {
  @Field(() => String)
  @IsMongoId()
  user: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  ic?: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  ID?: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  passport?: string;

  @Field(() => Date, { nullable: true })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  passportExpiresAt?: Date;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  permitNumber?: string;

  @Field(() => Date, { nullable: true })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  permitExpiresAt?: Date;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  gender?: string;

  @Field(() => Date, { nullable: true })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  dob?: Date;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  placeOfBirth?: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  currentAddress?: string;

  @Field(() => Date, { nullable: true })
  @IsDate()
  @IsOptional()
  @Type(() => Date)
  joinedAt?: Date;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  maritalStatus?: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  bankAccNumber?: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  bankName?: string;

  @Field(() => [EmergencyContactInput], { nullable: true })
  @ValidateNested({ each: true })
  @Type(() => EmergencyContactInput)
  @IsOptional()
  emergencyContact?: EmergencyContactInput[];
}

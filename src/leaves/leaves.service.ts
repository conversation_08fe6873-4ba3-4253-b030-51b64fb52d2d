import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { CreateLeaveInput } from './dto/create-leave.input';
import { UpdateLeaveInput } from './dto/update-leave.input';
import { FindLeavesInput } from './dto/find-leaves.input';
import { Leave } from './entities/leave.entity';
import { FilterQuery, Model } from 'mongoose';

@Injectable()
export class LeavesService {
  constructor(@InjectModel(Leave.name) private leave: Model<Leave>) {}

  create(createLeaveInput: CreateLeaveInput) {
    return this.leave.create(createLeaveInput);
  }

  findAll(filter: FindLeavesInput = {}) {
    const query: FilterQuery<Leave> = {};

    // Handle existing filters
    if (filter.user) {
      query.user = filter.user;
    }

    if (filter.leaveStatus) {
      query.leaveStatus = filter.leaveStatus;
    }

    if (filter.leaveType) {
      query.leaveType = filter.leaveType;
    }

    // Handle date range filtering
    if (filter.dateRange) {
      const dateFilter: any = {};

      if (filter.dateRange.startDate) {
        // Find leaves that end on or after the start date
        dateFilter.$gte = filter.dateRange.startDate;
      }

      if (filter.dateRange.endDate) {
        // Find leaves that start on or before the end date
        if (!query.startDateTime) {
          query.startDateTime = {};
        }
        query.startDateTime.$lte = filter.dateRange.endDate;
      }

      // Apply the date filter to endDateTime for start date filtering
      if (Object.keys(dateFilter).length > 0) {
        query.endDateTime = dateFilter;
      }
    }

    return this.leave.find(query);
  }

  findOne(filter: FilterQuery<Leave>) {
    return this.leave.findOne(filter);
  }

  update(id: string, updateLeaveInput: UpdateLeaveInput) {
    return this.leave.findByIdAndUpdate(id, updateLeaveInput, { new: true });
  }
}

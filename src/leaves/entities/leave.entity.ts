import { Field, registerEnumType, ObjectType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { User } from 'src/users/entities/user.entity';

export enum LeaveType {
  FULLDAY = 'FULLDAY',
  HALFDAY = 'HALFDAY',
}

export enum LeaveStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

registerEnumType(LeaveType, {
  name: 'LeaveType',
});
registerEnumType(LeaveStatus, {
  name: 'LeaveStatus',
});

@ObjectType()
@Schema()
export class Leave extends MongooseSchema {
  @Field(() => User)
  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId })
  user: string;

  @Field()
  @Prop({ required: true, type: String })
  reason: string;

  @Field(() => LeaveType)
  @Prop({ required: true, type: String, enum: LeaveType })
  leaveType: LeaveType;

  @Field(() => Date)
  @Prop({ required: true, type: Date })
  startDateTime: Date;

  @Field(() => Date)
  @Prop({ required: true, type: Date })
  endDateTime: Date;

  @Field(() => LeaveStatus, { nullable: true })
  @Prop({
    required: false,
    type: String,
    enum: LeaveStatus,
    default: LeaveStatus.PENDING,
  })
  leaveStatus?: LeaveStatus;

  @Field(() => String, { nullable: true })
  @Prop({ type: String, required: false })
  rejectedReason?: string;

  @Field(() => User, { nullable: true })
  @Prop({ required: false, type: mongoose.Schema.Types.ObjectId })
  approvedBy?: string;
}

export const LeaveSchema = SchemaFactory.createForClass(Leave);

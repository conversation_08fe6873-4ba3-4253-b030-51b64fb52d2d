import { Field, InputType } from '@nestjs/graphql';
import { LeaveType } from '../entities/leave.entity';
import { IsMongoId, IsNotEmpty } from 'class-validator';

@InputType()
export class CreateLeaveInput {
  @Field()
  @IsMongoId()
  @IsNotEmpty()
  user: string;

  @Field()
  reason: string;

  @Field(() => LeaveType)
  leaveType: LeaveType;

  @Field(() => Date)
  startDateTime: Date;

  @Field(() => Date)
  endDateTime: Date;
}

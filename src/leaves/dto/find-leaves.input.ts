import { InputType, Field } from '@nestjs/graphql';
import { IsMongoId, IsNotEmpty, IsOptional } from 'class-validator';
import { DateRangeInput } from '../../analytics/dto/analytics-filter.input';

@InputType()
export class FindLeavesInput {
  @Field(() => String, { nullable: true })
  @IsMongoId()
  @IsNotEmpty()
  @IsOptional()
  user?: string;

  @Field({ nullable: true })
  leaveStatus?: string;

  @Field({ nullable: true })
  leaveType?: string;

  @Field(() => DateRangeInput, { nullable: true })
  @IsOptional()
  dateRange?: DateRangeInput;
}

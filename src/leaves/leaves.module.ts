import { Module } from '@nestjs/common';
import { LeavesService } from './leaves.service';
import { LeavesResolver } from './leaves.resolver';
import { MongooseModule } from '@nestjs/mongoose';
import { Leave, LeaveSchema } from './entities/leave.entity';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Leave.name, schema: LeaveSchema }]),
  ],
  providers: [LeavesResolver, LeavesService],
})
export class LeavesModule {}

@import url('https://fonts.googleapis.com/css2?family=Work+Sans:ital,wght@0,100..900;1,100..900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  font-family: 'Work Sans', serif;
}

@layer base {
  :root {
    --background: #fafaf7;
    --foreground: #121c0d;
    --card: #ebf2e8;
    --card-foreground: #121c0d;
    --popover: #fafaf7;
    --popover-foreground: #121c0d;
    --primary: #4dde21;
    /* --primary-foreground: #FFEEF0; */
    --primary-foreground: #121c0d;
    --secondary: #ebf2e8;
    --secondary-foreground: #121c0d;
    --muted: #eaeaec;
    --muted-foreground: #76777a;
    --accent: #eaeaec;
    --accent-foreground: #19191a;
    --destructive: #e03131;
    --destructive-foreground: #fafafa;
    --border: #d1d1d9;
    --input: #d1d1d9;
    --ring: #4dde21;
    --radius: 0.85rem;
    --chart-1: #e66f32;
    --chart-2: #209b74;
    --chart-3: #1a4f6b;
    --chart-4: #e6b822;
    --chart-5: #f4a127;
    --sidebar-background: #fafafa;
    --sidebar-foreground: #464b53;
    --sidebar-primary: #16181a;
    --sidebar-primary-foreground: #fafafa;
    --sidebar-accent: #ebf2e8;
    --sidebar-accent-foreground: #16181a;
    --sidebar-border: #d2d6db;
    --sidebar-ring: #388bff;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  addMinutes,
  differenceInMinutes,
  eachDayOfInterval,
  endOfYear,
  startOfToday,
} from 'date-fns';
import { AnyBulkWriteOperation, FilterQuery, Model } from 'mongoose';
import { extractDate } from 'src/utils';
import { CreateShiftInput } from './dto/create-shift.input';
import { UpdateShiftInput } from './dto/update-shift.input';
import { Shift } from './entities/shift.entity';

@Injectable()
export class ShiftsService {
  constructor(@InjectModel(Shift.name) private readonly shift: Model<Shift>) {}

  async create({
    locationId,
    userIds,
    startDateTime,
    endDateTime,
    overTime,
    isRecurring,
    recurringId,
  }: CreateShiftInput) {
    // bulk create shifts
    if (isRecurring) {
      const dates = eachDayOfInterval({
        start: extractDate(startDateTime),
        end: endOfYear(startOfToday()),
      });

      const diffInMins = differenceInMinutes(endDateTime, startDateTime);

      const bulkwriteOperation: AnyBulkWriteOperation<Shift>[] = dates.map(
        (date) => {
          const startTime = addMinutes(
            date,
            startDateTime.getHours() * 60 + startDateTime.getMinutes(),
          );
          const OT = addMinutes(
            date,
            overTime ? overTime.getHours() * 60 + overTime.getMinutes() : 0,
          );
          const endTime = addMinutes(startTime, diffInMins);

          return {
            insertOne: {
              document: new this.shift({
                startDateTime: startTime,
                endDateTime: endTime,
                location: locationId,
                overTime,
                OT,
                recurringId: recurringId || null,
                isRecurring,
                users: userIds,
              }),
            },
          };
        },
      );

      await this.shift.bulkWrite(bulkwriteOperation);
    } else {
      await this.shift.create({
        users: userIds,
        location: locationId,
        startDateTime,
        endDateTime,
        overTime,
        isRecurring,
        recurringId,
      });
    }
  }

  findAll(query: FilterQuery<Shift> = {}) {
    return this.shift.find(query);
  }

  findOne(query: FilterQuery<Shift>) {
    return this.shift.findOne(query);
  }

  async update(id: string, updateShiftInput: UpdateShiftInput): Promise<Shift> {
    const existingShift = await this.shift.findOne({ _id: id });
    if (!existingShift) {
      throw new NotFoundException('Shift not found');
    }

    const { userIds, isRecurring, fromDate, ...restUpdateInput } =
      updateShiftInput;

    // Handle recurring shift updates
    if (isRecurring && existingShift.recurringId) {
      const currentDate = new Date();

      // Determine the effective start date for updates
      // Use fromDate if provided, otherwise use current date
      const effectiveStartDate = fromDate || currentDate;

      // Ensure we only update future shifts (startDateTime must be after current time)
      // This prevents modification of shifts that are already in progress or completed
      const filterDate = new Date(
        Math.max(effectiveStartDate.getTime(), currentDate.getTime()),
      );

      // Update all future recurring shifts from the selected date onwards
      await this.shift.updateMany(
        {
          recurringId: existingShift.recurringId,
          startDateTime: { $gte: filterDate },
        },
        {
          $set: {
            ...restUpdateInput,
            users: userIds,
          },
        },
      );

      const updatedShift = await this.shift.findById(id).exec();
      if (!updatedShift) {
        throw new NotFoundException('Shift not found');
      }
      return updatedShift;
    }

    // Handle single shift update
    const updatedShift = await this.shift
      .findOneAndUpdate(
        { _id: id },
        {
          $set: {
            ...restUpdateInput,
            users: userIds,
          },
        },
        { new: true },
      )
      .exec();

    if (!updatedShift) {
      throw new NotFoundException('Shift not found');
    }

    return updatedShift;
  }

  async remove(id: string) {
    const shift = await this.shift.findOne({ _id: id });
    if (!shift) {
      throw new NotFoundException('Shift not found');
    }

    if (new Date() > shift.startDateTime) {
      throw new BadRequestException(
        'Cannot delete a shift that has already started',
      );
    }
    await this.shift.deleteOne({ _id: id });
  }

  async removeRecurringShifts(id: string, recurringId: string) {
    const shift = await this.shift.findOne({ _id: id, recurringId });
    if (!shift) {
      throw new NotFoundException('Shift not found');
    }
    await this.shift.deleteMany({
      recurringId,
      startDateTime: { $gte: new Date() },
    });
  }
}

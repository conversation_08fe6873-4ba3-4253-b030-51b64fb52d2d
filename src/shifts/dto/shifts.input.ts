import { Field, ID, InputType } from '@nestjs/graphql';
import { IsDate, IsMongoId, IsOptional } from 'class-validator';

@InputType()
export class ShiftsInput {
  @Field(() => ID, { nullable: true })
  @IsMongoId()
  @IsOptional()
  locationId?: string;

  @Field(() => Date)
  @IsDate()
  startDateTime: Date;

  @Field(() => Date)
  @IsDate()
  endDateTime: Date;

  @Field(() => ID, { nullable: true })
  @IsMongoId()
  @IsOptional()
  userId?: string;
}

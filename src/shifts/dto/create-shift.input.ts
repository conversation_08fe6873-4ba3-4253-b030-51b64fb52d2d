import { Field, ID, InputType } from '@nestjs/graphql';
import {
  ArrayNotEmpty,
  IsArray,
  IsBoolean,
  IsDate,
  IsMongoId,
  IsOptional,
  IsString,
} from 'class-validator';

@InputType()
export class CreateShiftInput {
  @Field(() => ID)
  @IsMongoId()
  locationId: string;

  @Field(() => Date)
  @IsDate()
  startDateTime: Date;

  @Field(() => Date)
  @IsDate()
  endDateTime: Date;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  @IsDate()
  overTime?: Date;

  @Field(() => [ID])
  @IsArray()
  @ArrayNotEmpty()
  @IsMongoId({ each: true })
  userIds: string[];

  @Field(() => Boolean, { nullable: true })
  @IsOptional()
  @IsBoolean()
  isRecurring?: boolean;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  recurringId?: string;
}

import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Shift, ShiftSchema } from './entities/shift.entity';
import { ShiftsResolver } from './shifts.resolver';
import { ShiftsService } from './shifts.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Shift.name, schema: ShiftSchema }]),
  ],
  providers: [ShiftsResolver, ShiftsService],
  exports: [ShiftsService],
})
export class ShiftsModule {}

import { ObjectType, Field } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { User } from 'src/users/entities/user.entity';
import { Location } from 'src/location/entities/location.entity';
import { MongooseSchema } from 'src/common/common.entity';

@ObjectType()
@Schema()
export class Shift extends MongooseSchema {
  @Field(() => Location)
  @Prop({ type: mongoose.Schema.Types.ObjectId, required: true })
  location: string;

  @Field(() => Date)
  @Prop({ required: true })
  startDateTime: Date;

  @Field(() => Date)
  @Prop({ required: true })
  endDateTime: Date;

  @Field(() => Date, { nullable: true })
  @Prop(Date)
  overTime?: Date;

  @Field(() => [User])
  @Prop({
    type: [{ type: mongoose.Schema.Types.ObjectId }],
    required: true,
  })
  users: string[];

  @Field(() => Boolean, { nullable: true })
  @Prop()
  isRecurring?: boolean;

  @Field(() => String, { nullable: true })
  @Prop()
  recurringId?: string;
}

export const ShiftSchema = SchemaFactory.createForClass(Shift);

import {
  Args,
  Context,
  Mu<PERSON>,
  <PERSON>rent,
  Query,
  ResolveField,
  Resolver,
} from '@nestjs/graphql';
import { GqlContext } from 'src/app.module';
import { Location } from 'src/location/entities/location.entity';
import { User } from 'src/users/entities/user.entity';
import { CreateShiftInput } from './dto/create-shift.input';
import { ShiftsInput } from './dto/shifts.input';
import { UpdateShiftInput } from './dto/update-shift.input';
import { Shift } from './entities/shift.entity';
import { ShiftsService } from './shifts.service';

@Resolver(() => Shift)
export class ShiftsResolver {
  constructor(private readonly shiftsService: ShiftsService) {}

  @ResolveField(() => Location, { name: 'location', nullable: true })
  getLocation(@Parent() shift: Shift, @Context() context: GqlContext) {
    return context.loaders.locationLoader.load(shift.location);
  }

  @ResolveField(() => [User], { name: 'users', nullable: true })
  getUsers(@Parent() shift: Shift, @Context() context: GqlContext) {
    return context.loaders.usersLoader.loadMany(shift.users);
  }

  @Mutation(() => Boolean, { nullable: true })
  async createShift(
    @Args('createShiftInput') createShiftInput: CreateShiftInput,
  ) {
    await this.shiftsService.create(createShiftInput);
    return true;
  }

  @Query(() => [Shift], { name: 'shifts' })
  findAll(
    @Args('shiftsInput', { type: () => ShiftsInput })
    { locationId, startDateTime, endDateTime }: ShiftsInput,
  ) {
    return this.shiftsService.findAll({
      ...(locationId && { location: locationId }),
      startDateTime: { $gte: startDateTime, $lte: endDateTime },
    });
  }

  @Query(() => Shift, { name: 'shift' })
  findOne(@Args('shiftId', { type: () => String }) shiftId: string) {
    return this.shiftsService.findOne({ _id: shiftId });
  }

  @Mutation(() => Shift)
  updateShift(
    @Args('shiftId', { type: () => String }) shiftId: string,
    @Args('updateShiftInput') updateShiftInput: UpdateShiftInput,
  ) {
    return this.shiftsService.update(shiftId, updateShiftInput);
  }

  @Mutation(() => Boolean, { nullable: true })
  removeShift(@Args('shiftId', { type: () => String }) shiftId: string) {
    return this.shiftsService.remove(shiftId);
  }

  @Mutation(() => Boolean, { nullable: true })
  async removeRecurringShifts(
    @Args('shiftId', { type: () => String }) shiftId: string,
    @Args('recurringId') recurringId: string,
  ) {
    return this.shiftsService.removeRecurringShifts(shiftId, recurringId);
  }

  @Query(() => [Shift])
  async getUserShifts(
    @Args('shiftsInput', { type: () => ShiftsInput })
    { startDateTime, endDateTime, locationId, userId }: ShiftsInput,
  ) {
    return this.shiftsService.findAll({
      ...(locationId && { location: locationId }),
      ...(userId && { users: { $in: [userId] } }),
      startDateTime: { $gte: startDateTime, $lte: endDateTime },
    });
  }

  @Query(() => Shift)
  getShiftsByLocation(
    @Args('locationId', { type: () => String }) locationId: string,
  ) {
    return this.shiftsService.findAll({ location: locationId });
  }
}

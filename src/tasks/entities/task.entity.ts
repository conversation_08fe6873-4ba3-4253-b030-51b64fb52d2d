import { Field, ObjectType, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { Location } from 'src/location/entities/location.entity';
import { Shift } from 'src/shifts/entities/shift.entity';
import { User } from 'src/users/entities/user.entity';

enum TaskStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
}

registerEnumType(TaskStatus, { name: 'TaskStatus' });

@ObjectType()
@Schema()
export class Task {
  @Field(() => Location)
  @Prop({ required: true, type: Types.ObjectId })
  location: string;

  @Field(() => Shift)
  @Prop({ required: true, type: Types.ObjectId })
  shift: string;

  @Field(() => [User])
  @Prop({ required: true, type: [Types.ObjectId] })
  users: string[];

  @Field(() => String)
  @Prop({ required: true, type: String })
  title: string;

  @Field(() => String)
  @Prop({ required: true, type: String })
  description: string;

  @Field(() => TaskStatus)
  @Prop({
    required: true,
    type: String,
    enum: TaskStatus,
    default: TaskStatus.PENDING,
  })
  taskStatus: TaskStatus;

  @Field(() => Boolean)
  @Prop({ required: true, type: Boolean, default: false })
  isRecurring: boolean;

  @Field(() => String, { nullable: true })
  @Prop({ required: false, type: String })
  shiftRecurringId?: string;

  @Field(() => Date)
  @Prop({ required: true, type: Date })
  date: Date;
}

export const TaskSchema = SchemaFactory.createForClass(Task);

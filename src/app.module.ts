/* eslint-disable @typescript-eslint/no-unsafe-argument */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-return */
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { GraphQLModule, GqlModuleOptions } from '@nestjs/graphql';
import { MongooseModule } from '@nestjs/mongoose';
import * as Joi from 'joi';
import { join } from 'path';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { HealthModule } from './health/health.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { APP_GUARD } from '@nestjs/core';
import { AuthGuard } from './auth/guards/auth.guard';
import { JwtService } from '@nestjs/jwt';
import { RolesGuard } from './auth/guards/roles.guard';
import { LocationModule } from './location/location.module';
import { ShiftsModule } from './shifts/shifts.module';
import { AllowancesModule } from './allowances/allowances.module';
import { AnouncementsModule } from './anouncements/anouncements.module';
import { UserDocumentsModule } from './user-documents/user-documents.module';
import { HolidaysModule } from './holidays/holidays.module';
import { InventoryModule } from './inventory/inventory.module';
import { LeavesModule } from './leaves/leaves.module';
import { PaymentCorrectionsModule } from './payment-corrections/payment-corrections.module';
import { TasksModule } from './tasks/tasks.module';
import { UserProfilesModule } from './user-profiles/user-profiles.module';
import { AttendanceModule } from './attendance/attendance.module';
import { PaymentConfigModule } from './payment-config/payment-config.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { StorageModule } from './storage/storage.module';
import { DataloaderModule } from './dataloader/dataloader.module';
import { DataloaderService } from './dataloader/dataloader.service';
import { MigrationService } from './migration/migration.service';
import { MigrationModule } from './migration/migration.module';
import { ClaimsModule } from './claims/claims.module';
import { CheckpointsModule } from './checkpoints/checkpoints.module';
import { CheckpointAttendanceModule } from './checkpoint-attendance/checkpoint-attendance.module';
import { IncidentMonitoringModule } from './incident-monitoring/incident-monitoring.module';
import { AnalyticsModule } from './analytics/analytics.module';
import mongoose from 'mongoose';
import { AwsModule } from './aws/aws.module';
import { ApolloServerPluginInlineTrace } from '@apollo/server/plugin/inlineTrace';
import { ApolloServerPluginLandingPageLocalDefault } from '@apollo/server/plugin/landingPage/default';

const validationSchema = Joi.object({
  DATABASE_URI: Joi.string().required(),
  DATABASE_NAME: Joi.string().required(),
  JWT_SECRET: Joi.string().required(),
  MAX_UPLOAD_FILE_SIZE_IN_MB: Joi.number().required(),
  AWS_ACCESS_KEY_ID: Joi.string().required(),
  AWS_SECRET_ACCESS_KEY: Joi.string().required(),
  AWS_BUCKET: Joi.string().required(),
  AWS_BUCKET_URL: Joi.string().required(),
  AWS_REKOGNITION_COLLECTION_ID: Joi.string().required(),
  AWS_REGION: Joi.string().default('ap-southeast-1'),
});

export type GqlContext = {
  loaders: ReturnType<typeof DataloaderService.prototype.getLoaders>;
};

@Module({
  imports: [
    ConfigModule.forRoot({ validationSchema }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (config: ConfigService) => {
        // Add timestamps plugin
        mongoose.plugin((schema) => schema.set('timestamps', true));

        // Check for existing connection
        if (global['mongooseConnection']) {
          console.log('Using existing mongoose connection');
          return {
            uri: config.get('DATABASE_URI'),
            connectionFactory: () => global['mongooseConnection'],
          };
        }

        // Create new connection
        console.log('Creating new mongoose connection');
        const uri = config.get('DATABASE_URI');
        const dbName = config.get('DATABASE_NAME');

        const connection = await mongoose.connect(uri, {
          dbName,
          // Add recommended settings for Lambda
          serverSelectionTimeoutMS: 5000,
          socketTimeoutMS: 45000,
        });

        // Cache the connection
        global['mongooseConnection'] = connection;

        return {
          uri,
          connectionFactory: () => connection,
        };
      },
      inject: [ConfigService],
    }),
    GraphQLModule.forRootAsync<ApolloDriverConfig>({
      driver: ApolloDriver,
      imports: [DataloaderModule],
      inject: [DataloaderService],
      useFactory(dataloaderService: DataloaderService) {
        return {
          autoSchemaFile: {
            path: join(process.cwd(), 'src/schema.gql'),
          },
          introspection: true,
          playground: false,
          plugins: [
            ApolloServerPluginLandingPageLocalDefault() as any,
            ApolloServerPluginInlineTrace(),
          ],
          context() {
            return { loaders: dataloaderService.getLoaders() };
          },
        };
      },
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'client'),
    }),
    HealthModule,
    AuthModule,
    UsersModule,
    LocationModule,
    ShiftsModule,
    AllowancesModule,
    AnouncementsModule,
    UserDocumentsModule,
    HolidaysModule,
    InventoryModule,
    LeavesModule,
    PaymentCorrectionsModule,
    TasksModule,
    UserProfilesModule,
    AttendanceModule,
    PaymentConfigModule,
    StorageModule,
    DataloaderModule,
    MigrationModule,
    ClaimsModule,
    CheckpointsModule,
    CheckpointAttendanceModule,
    AwsModule,
    IncidentMonitoringModule,
    AnalyticsModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    JwtService,
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
    MigrationService,
  ],
})
export class AppModule {}

import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { UpdateUserInput } from './dto/update-user.input';
import { User } from './entities/user.entity';
import { UsersService } from './users.service';
import { UsersInput } from './dto/users.input';
import { IndexFaceInput } from './dto/index-face.input';
import { ConflictException, NotFoundException } from '@nestjs/common';
import { ClearFaceInput } from './dto/clear-face.input';

@Resolver(() => User)
export class UsersResolver {
  constructor(private readonly usersService: UsersService) {}

  @Query(() => [User], { name: 'users' })
  findAll(
    @Args('usersInput', { type: () => UsersInput, nullable: true })
    usersInput?: UsersInput,
  ) {
    return this.usersService.findAll({
      ...(!!usersInput?.roles?.length && { role: { $in: usersInput.roles } }),
    });
  }

  @Query(() => User, { name: 'user' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.usersService.findOne({ _id: id });
  }

  @Mutation(() => User)
  updateUser(@Args('updateUserInput') updateUserInput: UpdateUserInput) {
    return this.usersService.update(updateUserInput.id, updateUserInput);
  }

  @Mutation(() => User)
  async indexFace(@Args('indexFaceInput') indexFaceInput: IndexFaceInput) {
    const userProfile = await this.usersService.findOne({
      _id: indexFaceInput.userId,
    });

    if (userProfile?.faceInformation?.faceId) {
      throw new ConflictException('Face already registered');
    }
    const faceMatches = await this.usersService.searchFace(
      indexFaceInput.base64Img,
    );
    if (
      Array.isArray(faceMatches?.FaceMatches) &&
      faceMatches.FaceMatches.some(
        (face) => face.Face?.ExternalImageId === indexFaceInput.userId,
      )
    ) {
      throw new ConflictException('Face already registered');
    }

    const face = await this.usersService.indexFace(indexFaceInput);
    const updatedUser = await this.usersService.update(indexFaceInput.userId, {
      id: indexFaceInput.userId,
      faceInformation: { faceId: face.FaceRecords?.[0]?.Face?.FaceId },
    });
    return updatedUser;
  }

  @Mutation(() => Boolean)
  async clearFace(
    @Args('clearFaceInput') clearFaceInput: ClearFaceInput,
  ): Promise<boolean> {
    const userProfile = await this.usersService.findOne({
      _id: clearFaceInput.userId,
    });

    if (!userProfile) {
      throw new NotFoundException('User not found');
    }

    const faceId = userProfile.faceInformation?.faceId;
    if (!faceId) {
      throw new NotFoundException('User does not have a face index');
    }

    await this.usersService.update(clearFaceInput.userId, {
      id: clearFaceInput.userId,
      faceInformation: {},
    });

    await this.usersService.deleteFaceIndex(faceId, clearFaceInput.userId);

    return true;
  }
}

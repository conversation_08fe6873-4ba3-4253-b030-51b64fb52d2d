import { InputType, Field } from '@nestjs/graphql';
import { UserRoles, UserStatus } from '../entities/user.entity';
import { IsString, IsEnum, MinLength } from 'class-validator';

@InputType()
export class CreateUserInput {
  @Field(() => String, { description: 'user fullname' })
  @IsString()
  fullname: string;

  @Field(() => String, { description: 'user phone number' })
  @IsString()
  phone: string;

  @Field(() => UserRoles, { description: 'user role' })
  @IsEnum(UserRoles)
  role: UserRoles;

  @Field(() => String, { description: 'user password' })
  @IsString()
  @MinLength(4)
  password: string;

  @Field(() => UserStatus, { description: 'user active status' })
  @IsEnum(UserStatus)
  userStatus: UserStatus;

  @Field(() => String, { description: 'Employee Id' })
  @IsString()
  employeeId: string;
}

import { CreateUserInput } from './create-user.input';
import { InputType, Field, PartialType } from '@nestjs/graphql';
import { FaceInformationInput } from './face-information.input';

@InputType()
export class UpdateUserInput extends PartialType(CreateUserInput) {
  @Field()
  id: string;

  @Field(() => FaceInformationInput, { nullable: true })
  faceInformation?: FaceInformationInput;

  @Field(() => String, { nullable: true })
  profilePicture?: string;

  @Field(() => String, { nullable: true })
  employeeId?: string;
}

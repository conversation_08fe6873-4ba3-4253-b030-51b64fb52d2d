import { ConflictException, Injectable } from '@nestjs/common';
import { CreateUserInput } from './dto/create-user.input';
import { UpdateUserInput } from './dto/update-user.input';
import { InjectModel } from '@nestjs/mongoose';
import { User } from './entities/user.entity';
import { FilterQuery, Model } from 'mongoose';
import * as argon from 'argon2';
import { AwsService } from 'src/aws/aws.service';
import { IndexFaceInput } from './dto/index-face.input';

@Injectable()
export class UsersService {
  constructor(
    @InjectModel(User.name) private user: Model<User>,
    private readonly awsService: AwsService,
  ) {}

  async create(createUserInput: CreateUserInput) {
    // Check if user exists with same phone or employee ID
    const userExists = await this.user.findOne({
      $or: [
        { phone: createUserInput.phone },
        { employeeId: createUserInput.employeeId },
      ],
    });

    if (userExists) {
      if (userExists.phone === createUserInput.phone) {
        throw new ConflictException(
          'User with this phone number already exists',
        );
      }
      if (userExists.employeeId === createUserInput.employeeId) {
        throw new ConflictException(
          `Employee ID ${createUserInput.employeeId} already exists`,
        );
      }
    }

    return this.user.create(createUserInput);
  }

  findAll(filter: FilterQuery<User> = {}) {
    return this.user.find(filter);
  }

  findOne(filter: FilterQuery<User>) {
    return this.user.findOne(filter);
  }

  async update(id: string, updateUserInput: UpdateUserInput) {
    if (updateUserInput.password)
      updateUserInput.password = await argon.hash(updateUserInput.password);
    return this.user.findByIdAndUpdate(id, updateUserInput, { new: true });
  }

  searchFace(base64Img: string) {
    const base64Data = base64Img.replace(/^data:image\/\w+;base64,/, '');
    return this.awsService.searchFace(Buffer.from(base64Data, 'base64'));
  }

  async indexFace(input: IndexFaceInput) {
    const base64Data = input.base64Img.replace(/^data:image\/\w+;base64,/, '');
    const imageBuffer = Buffer.from(base64Data, 'base64');

    // Upload image to S3 for profile picture
    const s3Key = `profile-pictures/${input.userId}.jpg`;
    const s3Upload = await this.awsService.uploadImageBuffer(
      imageBuffer,
      s3Key,
      'image/jpeg',
    );

    // Index face in Rekognition
    const rekognitionResult = await this.awsService.indexFace(
      imageBuffer,
      input.userId,
    );

    // Update user with profile picture URL
    await this.user.findByIdAndUpdate(input.userId, {
      profilePicture: s3Upload.url,
    });

    return rekognitionResult;
  }

  async deleteFaceIndex(faceId: string, userId: string) {
    // Delete from Rekognition
    await this.awsService.deleteFace(faceId);

    // Delete profile picture from S3
    const s3Key = `profile-pictures/${userId}.jpg`;
    try {
      await this.awsService.deleteFile(s3Key);
    } catch (error) {
      // Log error but don't fail the operation if S3 deletion fails
      console.warn(
        `Failed to delete profile picture from S3: ${error.message}`,
      );
    }

    // Clear profile picture URL from user record
    await this.user.findByIdAndUpdate(userId, {
      profilePicture: null,
    });
  }
}

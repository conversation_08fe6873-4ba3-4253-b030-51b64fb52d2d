import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { IncidentMonitoringService } from './incident-monitoring.service';
import { IncidentMonitoringResolver } from './incident-monitoring.resolver';
import {
  Incident,
  IncidentSchema,
} from './entities/incident-monitoring.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Incident.name, schema: IncidentSchema },
    ]),
  ],
  providers: [IncidentMonitoringResolver, IncidentMonitoringService],
})
export class IncidentMonitoringModule {}

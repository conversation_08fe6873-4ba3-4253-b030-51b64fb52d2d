import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import { Incident } from './entities/incident-monitoring.entity';
import { CreateIncidentInput } from './dto/create-incident.input';
import { UpdateIncidentInput } from './dto/update-incident.input';

@Injectable()
export class IncidentMonitoringService {
  constructor(@InjectModel(Incident.name) private incident: Model<Incident>) {}

  create(userId: string, createIncidentInput: CreateIncidentInput) {
    return this.incident.create({
      ...createIncidentInput,
      reportedBy: userId,
      reportedAt: new Date(),
    });
  }

  findAll(query: FilterQuery<Incident> = {}) {
    return this.incident.find(query);
  }

  findOne(id: string) {
    return this.incident.findById(id);
  }

  update(id: string, updateIncidentInput: UpdateIncidentInput) {
    return this.incident.findByIdAndUpdate(id, updateIncidentInput, {
      new: true,
    });
  }

  remove(id: string) {
    return this.incident.findByIdAndDelete(id);
  }
}

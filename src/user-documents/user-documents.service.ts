import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, FilterQuery } from 'mongoose';
import { CreateUserDocumentInput } from './dto/create-user-document.input';
import { UpdateUserDocumentInput } from './dto/update-user-document.input';
import { UserDocument } from './entities/user-document.entity';

@Injectable()
export class UserDocumentsService {
  constructor(
    @InjectModel(UserDocument.name)
    private readonly userDocumentModel: Model<UserDocument>,
  ) {}

  async create(
    createUserDocumentInput: CreateUserDocumentInput,
  ): Promise<UserDocument> {
    const document = new this.userDocumentModel(createUserDocumentInput);
    return document.save();
  }

  async findAll(filter?: FilterQuery<UserDocument>): Promise<UserDocument[]> {
    return this.userDocumentModel.find(filter || {}).exec();
  }

  async findOne(filter: FilterQuery<UserDocument>): Promise<UserDocument> {
    const document = await this.userDocumentModel.findOne(filter).exec();
    if (!document) {
      throw new NotFoundException(`User document not found`);
    }
    return document;
  }

  async update(
    id: string,
    updateUserDocumentInput: UpdateUserDocumentInput,
  ): Promise<UserDocument> {
    const result = await this.userDocumentModel
      .updateOne(
        { _id: id },
        { $set: updateUserDocumentInput },
        {
          upsert: true,
          new: true,
          setDefaultsOnInsert: true,
        },
      )
      .exec();

    if (!result.acknowledged) {
      throw new NotFoundException(
        `Failed to update user document with ID "${id}"`,
      );
    }

    // Fetch the updated/created document
    const document = await this.userDocumentModel.findById(id).exec();
    if (!document) {
      throw new NotFoundException(`User document with ID "${id}" not found`);
    }

    return document;
  }

  async remove(id: string): Promise<boolean> {
    const result = await this.userDocumentModel.findByIdAndDelete(id).exec();

    if (!result) {
      throw new NotFoundException(`User document with ID "${id}" not found`);
    }

    return true;
  }
}

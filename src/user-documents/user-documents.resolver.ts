import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { UserDocumentsService } from './user-documents.service';
import { UserDocument } from './entities/user-document.entity';
import { CreateUserDocumentInput } from './dto/create-user-document.input';
import { UpdateUserDocumentInput } from './dto/update-user-document.input';
import { UserDocumentFilterInput } from './dto/user-document-filter.input';

@Resolver(() => UserDocument)
export class UserDocumentsResolver {
  constructor(private readonly userDocumentsService: UserDocumentsService) {}

  @Mutation(() => UserDocument)
  createUserDocument(
    @Args('createUserDocumentInput')
    createUserDocumentInput: CreateUserDocumentInput,
  ) {
    return this.userDocumentsService.create(createUserDocumentInput);
  }

  @Query(() => [UserDocument], { name: 'userDocuments' })
  findAll(
    @Args('filter', { type: () => UserDocumentFilterInput, nullable: true })
    filter?: UserDocumentFilterInput,
  ) {
    return this.userDocumentsService.findAll(filter || {});
  }

  @Query(() => UserDocument, { name: 'userDocument' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.userDocumentsService.findOne({ user: id });
  }

  @Mutation(() => UserDocument)
  updateUserDocument(
    @Args('id') id: string,
    @Args('updateUserDocumentInput')
    updateUserDocumentInput: UpdateUserDocumentInput,
  ) {
    return this.userDocumentsService.update(id, updateUserDocumentInput);
  }

  @Mutation(() => UserDocument)
  removeUserDocument(@Args('id', { type: () => String }) id: string) {
    return this.userDocumentsService.remove(id);
  }
}

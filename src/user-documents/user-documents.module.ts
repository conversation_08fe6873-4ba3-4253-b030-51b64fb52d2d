import { Module } from '@nestjs/common';
import { UserDocumentsService } from './user-documents.service';
import { UserDocumentsResolver } from './user-documents.resolver';
import {
  UserDocument,
  UserDocumentSchema,
} from './entities/user-document.entity';
import { MongooseModule } from '@nestjs/mongoose';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UserDocument.name, schema: UserDocumentSchema },
    ]),
  ],
  providers: [UserDocumentsResolver, UserDocumentsService],
  exports: [UserDocumentsService],
})
export class UserDocumentsModule {}

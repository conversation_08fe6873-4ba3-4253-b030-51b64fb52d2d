import { ObjectType, Field } from '@nestjs/graphql';
import { Prop, SchemaFactory, Schema } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { User } from 'src/users/entities/user.entity';

@ObjectType()
@Schema({ collection: 'user-documents' })
export class UserDocument extends MongooseSchema {
  @Field(() => User)
  @Prop({ required: true, type: Types.ObjectId })
  user: User;

  @Field()
  @Prop({ required: true })
  documentName: string;

  @Field()
  @Prop({ required: true })
  url: string;
}

export const UserDocumentSchema = SchemaFactory.createForClass(UserDocument);

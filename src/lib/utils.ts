import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { toast } from 'sonner';
import { isAxiosError } from 'axios';
import { VITE_S3_BASE_URL } from '@/env';
import QRCode from 'qrcode';
import { data } from '@/lib/data'
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function errorHandler(ex: unknown) {
  let errorMessage = 'Something went wrong';
  if (isAxiosError(ex)) {
    errorMessage = ex.response?.data?.message || ex.message || 'Error…';
  } else if (ex instanceof Error) errorMessage = ex.message;

  toast.error(errorMessage);
}

/**
 * function to extract initials from name for ex: Rafeeq <PERSON>haik is SH and if no second name, just first letter initials
 * */
export function extractInitials(name: string): string {
  if (!name) return '';

  const words = name.trim().split(/\s+/);
  if (words.length === 1) {
    return words[0][0].toUpperCase();
  }

  return words[0][0].toUpperCase() + words[words.length - 1][0].toUpperCase();
}

export function formatMinutesToHourMinutes(numberOfMins: number): string {
  if (numberOfMins <= 0) return '0 mins';

  const hours = Math.floor(numberOfMins / 60);
  const minutes = numberOfMins % 60;

  if (hours === 0) return `${minutes} mins`;
  if (minutes === 0) return `${hours} hours`;

  return `${hours} hours, ${minutes} mins`;
}

export const formatFileSize = (size: number): string => {
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
  if (size < 1024 * 1024 * 1024)
    return `${(size / (1024 * 1024)).toFixed(2)} MB`;
  return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
};

export const s3url = (path: string | undefined | null) =>
  path ? `${VITE_S3_BASE_URL}/${path}` : '';

export const enumToOptions = (enumObj: Record<string, string>) =>
  Object.values(enumObj).map(value => ({
    value,
    label: value
  }));

export const combineDateTime = (date: Date, time: string): Date => {
  const [hours, minutes] = time.split(':');
  const newDate = new Date(date);
  newDate.setHours(Number(hours));
  newDate.setMinutes(Number(minutes));
  newDate.setSeconds(0);

  return newDate;
};

export const printCheckpointQRCode = async <T>(payload: T) => {
  try {
    const qrData = JSON.stringify(payload);
    const canvas = document.createElement('canvas');

    await QRCode.toCanvas(canvas, qrData, { 
      margin: 1, 
      width: 400 
    });

    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      throw new Error('Failed to open print window');
    }

    printWindow.document.write(`
      <html>
        <head>
          <title>Print QR Code</title>
          <style>
            body {
              margin: 0;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              min-height: 100vh;
            }
            .container {
              text-align: center;
              display: contents;
            }
            .qr-code {
              width: 100%;
              max-width: 400px;
              margin-bottom: 20px;
            }
            .logo {
              width: 300px;
              margin-bottom: 20px;
            }
            @media print {
              body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
              }
            }
          </style>
        </head>
        <body>
          <div class="container">
            <img 
              id="logoImg" 
              class="logo" 
              src="${data.logo}" 
              alt="Logo"
            />
            <img 
              id="qrCodeImg" 
              class="qr-code" 
              src="${canvas.toDataURL('image/png')}" 
              alt="QR Code"
            />
          </div>
        </body>
      </html>
    `);

    printWindow.document.close();

    // Wait for images to load before printing
    const qrCodeImg = printWindow.document.getElementById('qrCodeImg');
    const logoImg = printWindow.document.getElementById('logoImg');

    if (qrCodeImg && logoImg) {
      Promise.all([
        new Promise(resolve => qrCodeImg.onload = resolve),
        new Promise(resolve => logoImg.onload = resolve)
      ]).then(() => {
        printWindow.print();
        printWindow.onafterprint = () => printWindow.close();
      });
    }

  } catch (error) {
    console.error('Error generating QR code:', error);
    errorHandler(error);
  }
};

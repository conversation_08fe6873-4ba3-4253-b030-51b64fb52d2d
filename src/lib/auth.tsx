import { useMeQuery } from '@/generated/graphql';
import { useCallback } from 'react';

export function useCurrentUser() {
  const { data: me, isLoading } = useMeQuery();

  const getCurrentUserId = useCallback(() => {
    return me?.me?.id || '';
  }, [me?.me?.id]);

  const getCurrentUserRole = useCallback(() => {
    return me?.me?.role || '';
  }, [me?.me?.role]);

  return {
    user: me?.me,
    userId: getCurrentUserId(),
    userRole: getCurrentUserRole(),
    isLoading
  };
}
import { Injectable } from '@nestjs/common';
import { CreateAllowanceInput } from './dto/create-allowance.input';
import { UpdateAllowanceInput } from './dto/update-allowance.input';

@Injectable()
export class AllowancesService {
  create(createAllowanceInput: CreateAllowanceInput) {
    return 'This action adds a new allowance';
  }

  findAll() {
    return `This action returns all allowances`;
  }

  findOne(id: number) {
    return `This action returns a #${id} allowance`;
  }

  update(id: number, updateAllowanceInput: UpdateAllowanceInput) {
    return `This action updates a #${id} allowance`;
  }

  remove(id: number) {
    return `This action removes a #${id} allowance`;
  }
}

import { ObjectType, Field, Int, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { Location } from 'src/location/entities/location.entity';
import { User } from 'src/users/entities/user.entity';

export enum AllowanceStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

registerEnumType(AllowanceStatus, { name: 'AllowanceStatus' });

@ObjectType({ description: 'User/Employee allowances' })
@Schema()
export class Allowance {
  @Field(() => User, { description: 'allowance created by' })
  @Prop({ required: true, type: Types.ObjectId })
  user: User;

  @Field(() => Location, { description: 'location allowance', nullable: true })
  @Prop({ required: false, type: Types.ObjectId })
  location: Location;

  @Field(() => String, { description: 'Type of allowance' })
  @Prop({ required: true })
  type: string;

  @Field(() => Number, { description: 'Amount of allowance' })
  @Prop({ required: true })
  amount: number;

  @Field(() => AllowanceStatus, {
    description: 'Status of allowance',
    nullable: true,
  })
  @Prop({ default: AllowanceStatus.PENDING, enum: AllowanceStatus })
  status: string;

  @Field(() => String, {
    description: 'Description of allowance',
    nullable: true,
  })
  @Prop()
  description?: string;

  @Field(() => String, { description: 'allowance receipt', nullable: true })
  @Prop()
  receipt?: string;
}

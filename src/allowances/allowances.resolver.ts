import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { AllowancesService } from './allowances.service';
import { Allowance } from './entities/allowance.entity';
import { CreateAllowanceInput } from './dto/create-allowance.input';
import { UpdateAllowanceInput } from './dto/update-allowance.input';

@Resolver(() => Allowance)
export class AllowancesResolver {
  constructor(private readonly allowancesService: AllowancesService) {}

  @Mutation(() => Allowance)
  createAllowance(@Args('createAllowanceInput') createAllowanceInput: CreateAllowanceInput) {
    return this.allowancesService.create(createAllowanceInput);
  }

  @Query(() => [Allowance], { name: 'allowances' })
  findAll() {
    return this.allowancesService.findAll();
  }

  @Query(() => Allowance, { name: 'allowance' })
  findOne(@Args('id', { type: () => Int }) id: number) {
    return this.allowancesService.findOne(id);
  }

  @Mutation(() => Allowance)
  updateAllowance(@Args('updateAllowanceInput') updateAllowanceInput: UpdateAllowanceInput) {
    return this.allowancesService.update(updateAllowanceInput.id, updateAllowanceInput);
  }

  @Mutation(() => Allowance)
  removeAllowance(@Args('id', { type: () => Int }) id: number) {
    return this.allowancesService.remove(id);
  }
}

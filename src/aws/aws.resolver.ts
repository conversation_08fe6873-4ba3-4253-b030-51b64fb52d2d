import { Args, Mutation, Resolver } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { AwsService } from './aws.service';
import { SignedUploadUrl } from './entities/signed-upload-url.entity';
import { SignedUploadUrlInput } from './dto/signed-upload-url.input';
import { RolesGuard } from '../auth/guards/roles.guard';
import { Roles } from '../auth/decorators/roles.decorator';
import { UserRoles } from '../users/entities/user.entity';

@Resolver(() => SignedUploadUrl)
export class AwsResolver {
  constructor(private readonly awsService: AwsService) {}

  @Mutation(() => SignedUploadUrl)
  // @UseGuards(RolesGuard)
  // @Roles(
  //   UserRoles.ADMIN,
  //   UserRoles.HR_ADMIN,
  //   UserRoles.OPERATIONS_ADMIN,
  //   UserRoles.OPERATIONS_MANAGER,
  // )
  createSignedUploadUrl(
    @Args('input') input: SignedUploadUrlInput,
  ): Promise<SignedUploadUrl> {
    return this.awsService.getSignedUploadUrl(
      input.key,
      input.contentType,
      input.expiresIn,
    );
  }
}

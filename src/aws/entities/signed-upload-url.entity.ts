import { Field, ObjectType } from '@nestjs/graphql';

@ObjectType()
class PresignedFields {
  @Field(() => String)
  key: string;

  @Field(() => String)
  bucket: string;

  @Field(() => String)
  acl: string;

  @Field(() => String)
  algorithm: string;

  @Field(() => String)
  credential: string;

  @Field(() => String)
  date: string;

  @Field(() => String)
  Policy: string;

  @Field(() => String)
  signature: string;
}

@ObjectType()
export class SignedUploadUrl {
  @Field(() => String)
  url: string;

  @Field(() => PresignedFields)
  fields: PresignedFields;
}

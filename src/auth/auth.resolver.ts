import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { CreateUserInput } from 'src/users/dto/create-user.input';
import { User } from 'src/users/entities/user.entity';
import { AuthService } from './auth.service';
import { CurrentUser } from './decorators/current-user.decorator';
import { SignInInput } from './dto/auth.input';
import { ScanFaceInput } from './dto/scan-face.input';
import { AuthOutput } from './entites/auth.entity';
import { Public } from './guards/auth.guard';

@Resolver(() => AuthOutput)
export class AuthResolver {
  constructor(private readonly authService: AuthService) {}

  @Public()
  @Mutation(() => AuthOutput)
  signIn(@Args('input') input: SignInInput) {
    return this.authService.signIn(input);
  }

  @Public()
  @Mutation(() => AuthOutput)
  signUp(@Args('input') input: CreateUserInput) {
    return this.authService.signUp(input);
  }

  @Public()
  @Mutation(() => AuthOutput)
  scanFace(@Args('input') input: ScanFaceInput) {
    return this.authService.scanFace(input);
  }

  @Query(() => User, { name: 'me', description: 'Logged in  user' })
  me(@CurrentUser() user: User) {
    return user;
  }
}

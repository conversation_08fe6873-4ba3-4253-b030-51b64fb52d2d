import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { hash, verify } from 'argon2';
import { CreateUserInput } from 'src/users/dto/create-user.input';
import { UsersService } from 'src/users/users.service';
import { SignInInput } from './dto/auth.input';
import { ScanFaceInput } from './dto/scan-face.input';
import { UserStatus } from 'src/users/entities/user.entity';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
  ) {}

  async signIn(input: SignInInput) {
    const user = await this.usersService.findOne({ phone: input.phone });

    if (!user) throw new NotFoundException('phone or password is incorrect');

    const isValid = await verify(user.password, input.password);
    if (!isValid) throw new NotFoundException('phone or password is incorrect');

    const tokenPayload = { userId: user.id };
    return { access_token: await this.jwtService.signAsync(tokenPayload) };
  }

  async signUp(input: CreateUserInput) {
    const hashedPassword = await hash(input.password);

    const user = await this.usersService.create({
      ...input,
      password: hashedPassword,
    });

    const tokenPayload = { userId: user.id };
    return { access_token: await this.jwtService.signAsync(tokenPayload) };
  }

  async scanFace(input: ScanFaceInput) {
    try {
      // Search for face matches using AWS Rekognition
      const faceMatches = await this.usersService.searchFace(input.base64Img);

      // Check if any face matches were found
      if (!faceMatches?.FaceMatches || faceMatches.FaceMatches.length === 0) {
        throw new UnauthorizedException('Face not recognized');
      }

      // Get the best match (first one, as they're sorted by confidence)
      const bestMatch = faceMatches.FaceMatches[0];

      // Verify confidence level (AWS returns confidence as percentage)
      if (!bestMatch.Face?.Confidence || bestMatch.Face.Confidence < 95) {
        throw new UnauthorizedException('Face recognition confidence too low');
      }

      // Get user ID from the external image ID
      const userId = bestMatch.Face.ExternalImageId;
      if (!userId) {
        throw new UnauthorizedException('Invalid face data');
      }

      // Find the user in the database
      const user = await this.usersService.findOne({ _id: userId });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Check if user is active
      if (user.userStatus !== UserStatus.ACTIVE) {
        throw new UnauthorizedException('User account is inactive');
      }

      // Verify user has face information registered
      if (!user.faceInformation?.faceId) {
        throw new UnauthorizedException('Face data not properly registered');
      }

      // Generate and return access token
      const tokenPayload = { userId: user.id };
      return { access_token: await this.jwtService.signAsync(tokenPayload) };
    } catch (error) {
      // If it's already a known exception, re-throw it
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }

      // For any other errors (AWS errors, etc.), throw a generic unauthorized error
      throw new UnauthorizedException('Face authentication failed');
    }
  }
}

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu';
import { createFileRoute } from '@tanstack/react-router';
import { Eye } from 'lucide-react';
import Table from '@/components/ui/table/index';
import { format } from 'date-fns';
import { useState } from 'react';
import { useIncidentsQuery } from '@/generated/graphql';
import Badge from '@/components/ui/badge';

const TableHeaderComponent = () => {
  return (
    <div className="mt-4 sm:flex sm:items-center">
      <div className="sm:flex-auto">
        <h1 className="text-base font-semibold text-gray-900">
          Incidents Monitoring
        </h1>
        <p className="mt-2 text-sm text-gray-700">
          A list of all reported incidents and their current status.
        </p>
      </div>
    </div>
  );
};

export const Route = createFileRoute('/incidents-monitoring/')({
  component: RouteComponent
});

const getPriorityBadgeColor = (priority: string) => {
  switch (priority.toLowerCase()) {
    case 'high':
      return 'bg-red-100 text-red-800';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800';
    case 'low':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

function RouteComponent() {
  const { data: incidents } = useIncidentsQuery();
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);
  const [selectedIncidentId, setSelectedIncidentId] = useState<string | null>(null);

  const handleReviewIncident = (incidentId: string) => {
    setSelectedIncidentId(incidentId);
    setIsReviewDialogOpen(true);
  };

  if (!incidents?.incidents) return null;

  const rowItems = incidents.incidents.map((incident) => {
    return [
      <div className="space-y-1">
        <div className="font-medium">{incident.location.name}</div>
        <div className="text-sm text-gray-500 line-clamp-2">
          {incident.description}
        </div>
      </div>,
      incident.reportedBy.fullname,
      format(new Date(incident.reportedAt), 'dd-MM-yyyy HH:mm'),
      <Badge className={getPriorityBadgeColor(incident.priorityLevel)}>
        {incident.priorityLevel}
      </Badge>,
      <DropdownMenu>
        <DropdownMenuTrigger className="text-blue-600">
          Action
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => handleReviewIncident(incident.id)}>
            <Eye className="mr-2 h-4 w-4" />
            Review Details
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ];
  });

  return (
    <div>
      <Table.StickyTable
        tableHeadComponent={<TableHeaderComponent />}
        headerItems={[
          [
            'Location & Description',
            'Reported By',
            'Date & Time',
            'Priority',
            'Actions'
          ]
        ]}
        rowItems={rowItems}
      />
      <Dialog
        open={isReviewDialogOpen}
        onOpenChange={setIsReviewDialogOpen}
      >
        <DialogContent className="max-w-3xl">
          <>
            <div className="flex flex-col gap-2">
              <DialogHeader>
                <DialogTitle className="text-left">Incident Details</DialogTitle>
                <DialogDescription className="text-left">
                  Review complete incident information
                </DialogDescription>
              </DialogHeader>
            </div>
            <Separator />
            {selectedIncidentId && (
              <div className="space-y-4">
                {incidents.incidents.find((i) => i.id === selectedIncidentId) && (
                  <ReviewIncidentDetails 
                    incident={incidents.incidents.find((i) => i.id === selectedIncidentId)!}
                  />
                )}
              </div>
            )}
          </>
        </DialogContent>
      </Dialog>
    </div>
  );
}

interface ReviewIncidentDetailsProps {
  incident: {
    id: string;
    reportedAt: Date;
    description: string;
    priorityLevel: string;
    location: { name: string };
    reportedBy: { fullname: string };
    evidence?: { type: string; url: string }[];
  };
}

function ReviewIncidentDetails({ incident }: ReviewIncidentDetailsProps) {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h3 className="font-medium text-gray-500">Location</h3>
          <p>{incident.location.name}</p>
        </div>
        <div>
          <h3 className="font-medium text-gray-500">Priority Level</h3>
          <Badge className={getPriorityBadgeColor(incident.priorityLevel)}>
            {incident.priorityLevel}
          </Badge>
        </div>
        <div>
          <h3 className="font-medium text-gray-500">Reported By</h3>
          <p>{incident.reportedBy.fullname}</p>
        </div>
        <div>
          <h3 className="font-medium text-gray-500">Reported At</h3>
          <p>{format(new Date(incident.reportedAt), 'dd MMM yyyy HH:mm')}</p>
        </div>
      </div>
      
      <div>
        <h3 className="font-medium text-gray-500">Description</h3>
        <p className="mt-1 whitespace-pre-wrap">{incident.description}</p>
      </div>

      {incident.evidence && incident.evidence.length > 0 && (
              <div>
                <h3 className="font-medium text-gray-500 mb-2">Evidence</h3>
                <div className="grid grid-cols-2 gap-4">
                  {incident.evidence.map((item, index) => (
                    <a 
                      key={index}
                      href={item.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      View {item.type}
                    </a>
                  ))}
                </div>
              </div>
            )}
    </div>
  );
}

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import CreateLocationForm from '@/forms/locations/CreateLocationForm';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu';
import { createFileRoute, Link } from '@tanstack/react-router';
import { Clock, Factory, Pencil, User } from 'lucide-react';
import Table from '@/components/ui/table/index';
import { useLocationsQuery } from '@/generated/graphql';

const TableHeaderComponent = () => {
  return (
    <div className="mt-4 sm:flex sm:items-center">
      <div className="sm:flex-auto">
        <h1 className="text-base font-semibold text-gray-900">
          Create Locations
        </h1>
        <p className="mt-2 text-sm text-gray-700">
          A list of all the locations in your account including their name,
          address, emergency contact and location.
        </p>
      </div>
      <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
        {/* create location dialog modal    */}
        <Dialog>
          <DialogTrigger asChild>
            <Button>Create Location</Button>
          </DialogTrigger>
          <DialogContent>
            <>
              <div className="flex flex-col gap-2">
                <div
                  className="flex size-11 shrink-0 items-center justify-center rounded-full border border-border"
                  aria-hidden="true"
                >
                  <Factory className="opacity-80" size={16} strokeWidth={2} />
                </div>
                <DialogHeader>
                  <DialogTitle className="text-left">
                    Create Location
                  </DialogTitle>
                  <DialogDescription className="text-left">
                    create a new location
                  </DialogDescription>
                </DialogHeader>
              </div>
              <Separator />
              <CreateLocationForm />
            </>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};
export const Route = createFileRoute('/locations/')({
  component: RouteComponent
});

function RouteComponent() {
  const { data: locations } = useLocationsQuery(undefined, {
    initialData: { locations: [] }
  });
  if (!locations?.locations) return null;

  const rowItems = locations.locations.map(loc => {
    return [
      loc.name,
      loc.address,
      loc.emergencyContact,
      <DropdownMenu>
        <DropdownMenuTrigger className="text-green-600">
          Action
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link
              to="/locations/$locationId"
              params={{ locationId: loc.id }}
            >
              <Pencil className="mr-2" />
              Edit
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem asChild>
            <Link
              to="/locations/$locationId/shift"
              params={{ locationId: loc.id }}
            >
              <Clock className="mr-2" />
              Shift
            </Link>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ];
  });

  return (
    <div className="">
      {/* Locations */}
      <Table.StickyTable
        tableHeadComponent={<TableHeaderComponent />}
        headerItems={[['Name', 'Address', 'Emergency Contact', 'Actions']]}
        rowItems={rowItems}
      />
    </div>
  );
}

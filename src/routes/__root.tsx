import { getToken } from '@/client'
import { AppSidebar } from '@/components/app-sidebar'
import {
  B<PERSON>crumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb'
import { Separator } from '@/components/ui/separator'
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger
} from '@/components/ui/sidebar'
import { Toaster } from '@/components/ui/sonner'
import { LoginForm } from '@/forms'
import useBreadCrumbs from '@/hooks/use-crumbs'
import { tokenAtom } from '@/hooks/useAuth'
import {
  Link,
  Outlet,
  createRootRouteWithContext,
  redirect
} from '@tanstack/react-router'
import { TanStackRouterDevtools } from '@tanstack/router-devtools'
import { useAtom } from 'jotai'

import React from 'react'

export const Route = createRootRouteWithContext<{}>()({
  component: () => {
    const [token] = useAtom(tokenAtom)

    if (!token) return <LoginForm />
    return <RootComponent />
  },

  head() {
    return {
      meta: [
        {
          charSet: 'utf-8'
        },
        {
          name: 'viewport',
          content: 'width=device-width, initial-scale=1'
        },
        {
          title: 'KKPM|HRMS'
        }
      ]
    }
  }
})

function RootComponent() {
  const breadCrumbs = useBreadCrumbs()

  return (
    <>
      <SidebarProvider className="text-foreground">
        <AppSidebar />
        <SidebarInset>
          <header className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator orientation="vertical" className="mr-2 h-4" />
              <Breadcrumb>
                <BreadcrumbList>
                  {breadCrumbs.map(({ href, label }, index) => {
                    const showSeperator = index !== breadCrumbs.length - 1
                    return (
                      <React.Fragment key={index}>
                        <BreadcrumbItem key={index} className="hidden md:block">
                          <BreadcrumbLink asChild>
                            <Link to={href} replace>
                              {label}
                            </Link>
                          </BreadcrumbLink>
                        </BreadcrumbItem>
                        {showSeperator && (
                          <BreadcrumbSeparator className="hidden md:block" />
                        )}
                      </React.Fragment>
                    )
                  })}
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>
          <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
            <Outlet />
          </div>
        </SidebarInset>
      </SidebarProvider>

      <TanStackRouterDevtools position="bottom-right" />
      <Toaster />
    </>
  )
}

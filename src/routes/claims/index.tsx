import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu';
import { useClaimsQuery, ClaimStatus, ClaimType } from "@/generated/graphql";
import { createFileRoute } from "@tanstack/react-router";
import { Eye } from "lucide-react";
import Table from '@/components/ui/table/index';
import { format } from "date-fns";
import { useState } from 'react';
import Badge from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { DateRange } from "react-day-picker";
import RangeCalendarWithPresets from '@/components/range-calendar-preset';
import { Combobox } from '@/components/ui/combo-box';
import { CalendarIcon } from "lucide-react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";

const TableHeaderComponent = () => {
  return (
    <div className="mt-4 sm:flex sm:items-center">
      <div className="sm:flex-auto">
        <h1 className="text-base font-semibold text-gray-900">Claims</h1>
        <p className="mt-2 text-sm text-gray-700">
          A list of all employee claims and their current status.
        </p>
      </div>
    </div>
  );
};

export const Route = createFileRoute('/claims/')({
  component: ClaimsComponent
});

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

interface ClaimFilters {
  user?: string;
  claimType?: ClaimType;
  status?: string;
  dateRange?: DateRange;
}

function ClaimsComponent() {
  const [filters, setFilters] = useState<ClaimFilters>({});
  const { data, isLoading } = useClaimsQuery({ 
    filter: {
      user: filters.user,
      claimType: filters.claimType as ClaimType,
      status: filters.status,
      from: filters.dateRange?.from?.toISOString(),
      to: filters.dateRange?.to?.toISOString()
    } 
  });
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [selectedClaimId, setSelectedClaimId] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<DateRange | undefined>();

  const handleViewClaim = (claimId: string) => {
    setSelectedClaimId(claimId);
    setIsViewDialogOpen(true);
  };

  const handleFilterChange = (key: keyof ClaimFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  if (isLoading || !data?.claims) return null;

  const rowItems = data.claims.map((claim) => {
    return [
      <div className="font-medium">{claim.user.fullname}</div>,
      <div className="text-gray-500">{claim.claimType}</div>,
      <div>RM {(claim.claimData.amount ?? 0).toFixed(2)}</div>,
      <div>{claim.claimData.purpose}</div>,
      <Badge className={getStatusColor(claim.status ?? 'pending')}>
        {claim.status ?? 'pending'}
      </Badge>,
      format(new Date(claim.createdAt), 'dd-MM-yyyy'),
      <DropdownMenu>
        <DropdownMenuTrigger className="text-blue-600">
          Action
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => handleViewClaim(claim.id)}>
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ];
  });

  const handleStatusChange = async (claimId: string, newStatus: ClaimStatus) => {
    try {
      // Add your mutation here using the generated GraphQL hooks
      // await updateClaimStatus({ variables: { claimId, status: newStatus } });
      await new Promise(resolve => setTimeout(resolve, 1000)); // Remove this when you add real mutation
      
      // Refetch claims or update cache as needed
      // await refetch();
    } catch (error) {
      throw error;
    }
  };

  const FilterSection = () => (
    <div className="flex items-center gap-4 mb-4 flex-wrap">
      <div className="w-[200px]">
        <Combobox
          options={(data?.claims || []).map(claim => ({
            label: claim.user.fullname,
            value: claim.user.id
          }))}
          placeholder="User"
          setValue={(value) => handleFilterChange('user', value)}
          value={filters.user}
        />
      </div>

      <div className="w-[150px]">
        <Select
          value={filters.claimType}
          onValueChange={(value) => handleFilterChange('claimType', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Claim Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALLOWANCE">ALLOWANCE</SelectItem>
            <SelectItem value="EXPENSE">EXPENSE</SelectItem>
            <SelectItem value="SITE">SITE</SelectItem>
            <SelectItem value="TRAVEL">TRAVEL</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="w-[150px]">
        <Select
          value={filters.status}
          onValueChange={(value) => handleFilterChange('status', value)}
        >
          <SelectTrigger>
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="PENDING">PENDING</SelectItem>
            <SelectItem value="APPROVED">APPROVED</SelectItem>
            <SelectItem value="REJECTED">REJECTED</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="w-[240px]">
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal",
                !dateRange && "text-muted-foreground"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {dateRange?.from ? (
                dateRange.to ? (
                  <>
                    {format(dateRange.from, "dd/MM/y")} -{" "}
                    {format(dateRange.to, "dd/MM/y")}
                  </>
                ) : (
                  format(dateRange.from, "dd/MM/y")
                )
              ) : (
                <span>Pick a date range</span>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="end">
            <RangeCalendarWithPresets
              range={dateRange}
              setDateRange={(range) => {
                setDateRange(range);
                handleFilterChange('dateRange', range);
              }}
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );

  return (
    <div className="space-y-4">
      <FilterSection />
      <Table.StickyTable
        tableHeadComponent={<TableHeaderComponent />}
        headerItems={[
          [
            'Employee',
            'Claim Type',
            'Amount',
            'Purpose',
            'Status',
            'Date',
            'Actions'
          ]
        ]}
        rowItems={rowItems}
      />
      <Dialog
        open={isViewDialogOpen}
        onOpenChange={setIsViewDialogOpen}
      >
        <DialogContent className="max-w-3xl">
          <>
            <div className="flex flex-col gap-2">
              <DialogHeader>
                <DialogTitle className="text-left">Claim Details</DialogTitle>
                <DialogDescription className="text-left">
                  View complete claim information
                </DialogDescription>
              </DialogHeader>
            </div>
            <Separator />
            {selectedClaimId && (
              <div className="space-y-4">
                {data.claims.find((c) => c.id === selectedClaimId) && (
                  <ViewClaimDetails 
                    claim={data.claims.find((c) => c.id === selectedClaimId)!}
                    onStatusChange={handleStatusChange}
                  />
                )}
              </div>
            )}
          </>
        </DialogContent>
      </Dialog>
    </div>
  );
}

interface ViewClaimDetailsProps {
  claim: any;
  onStatusChange?: (claimId: string, status: ClaimStatus) => Promise<void>;
}

// Add this helper function to get receipts based on claim type
const getClaimReceipts = (claimData: any, claimType: string) => {
  switch (claimType.toLowerCase()) {
    case 'allowance':
      return claimData.allowanceReceipts || [];
    case 'expense':
      return claimData.expenseReceipts || [];
    case 'site':
      return claimData.siteReceipts || [];
    case 'travel':
      return claimData.travelReceipts || [];
    default:
      return [];
  }
};

function ViewClaimDetails({ claim, onStatusChange }: ViewClaimDetailsProps) {
  const [selectedStatus, setSelectedStatus] = useState<ClaimStatus>(claim.status);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleStatusUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!onStatusChange) return;

    try {
      setIsUpdating(true);
      await onStatusChange(claim.id, selectedStatus);
      toast.success("Claim status updated successfully");
    } catch (error) {
      toast.error("Failed to update claim status");
    } finally {
      setIsUpdating(false);
    }
  };

  const receipts = getClaimReceipts(claim.claimData, claim.claimType);
  console.log(claim.claimData)
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <h3 className="font-medium text-gray-500">Employee</h3>
          <p>{claim.user.fullname}</p>
        </div>
        <div>
          <h3 className="font-medium text-gray-500">Status</h3>
          <Badge className={getStatusColor(claim.status)}>
            {claim.status}
          </Badge>
        </div>
        <div>
          <h3 className="font-medium text-gray-500">Claim Type</h3>
          <p className="capitalize">{claim.claimType}</p>
        </div>
        <div>
          <h3 className="font-medium text-gray-500">Amount</h3>
          <p>RM {claim.claimData.amount.toFixed(2)}</p>
        </div>
        <div>
          <h3 className="font-medium text-gray-500">Submitted On</h3>
          <p>{format(new Date(claim.createdAt), 'dd MMM yyyy HH:mm')}</p>
        </div>
        {claim.processedBy && (
          <div>
            <h3 className="font-medium text-gray-500">Processed By</h3>
            <p>{claim.processedBy.fullname}</p>
          </div>
        )}
      </div>
      
      <div>
        <h3 className="font-medium text-gray-500">Purpose</h3>
        <p className="mt-1 whitespace-pre-wrap">{claim.claimData.purpose}</p>
      </div>

      {claim.rejectedReason && (
        <div>
          <h3 className="font-medium text-gray-500">Rejection Reason</h3>
          <p className="mt-1 whitespace-pre-wrap text-red-600">{claim.rejectedReason}</p>
        </div>
      )}

      {receipts && receipts.length > 0 && (
        <div>
          <h3 className="font-medium text-gray-500 mb-2">Receipts</h3>
          <div className="grid grid-cols-2 gap-4">
            {receipts.map((receipt: string, index: number) => (
              <a 
                key={index}
                href={receipt}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                View Receipt {index + 1}
              </a>
            ))}
          </div>
        </div>
      )}

      <form onSubmit={handleStatusUpdate} className="col-span-2">
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-gray-500 mb-2">Update Status</h3>
            <Select
              value={selectedStatus}
              onValueChange={(value) => setSelectedStatus(value as ClaimStatus)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="APPROVED">Approved</SelectItem>
                <SelectItem value="REJECTED">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Button 
            type="submit"
            className="w-full"
            disabled={selectedStatus === claim.status || isUpdating}
          >
            {isUpdating ? "Updating..." : "Update Status"}
          </Button>
        </div>
      </form>
    </div>
  );
}

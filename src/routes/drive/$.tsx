import { CreateFolderDialog } from '@/components/drive/create-folder';
import DriveItem from '@/components/drive/drive-item';
import { FileState, UploadFileDialog } from '@/components/drive/upload-file';

import {
  StorageItemsQuery,
  useStorageFoldersQuery,
  useStorageItemsQuery
} from '@/generated/graphql';
import { s3url } from '@/lib/utils';
import { createFileRoute } from '@tanstack/react-router';

import { useMemo, useState } from 'react';

export const Route = createFileRoute('/drive/$')({
  component: RouteComponent,
  loader() {
    return { crumb: 'Drive' };
  }
});

function RouteComponent() {
  const [files, setFiles] = useState<FileState[]>([]);

  const { _splat = '' } = Route.useParams();

  const nav = Route.useNavigate();

  const folderId = useMemo(() => {
    const [folderId] = _splat.split('/').filter(Boolean).slice(-1);
    return folderId;
  }, [_splat]);

  console.log(folderId);

  const selectFolder = (
    storageItem: StorageItemsQuery['storageItems'][number]
  ) => {
    nav({ to: `/drive/${_splat}/${storageItem.id}` });
  };

  const onStorageItemClick = (
    storageItem: StorageItemsQuery['storageItems'][number]
  ) => {
    if (!storageItem.path) return;

    window.open(s3url(storageItem.path), '_blank');
  };

  const { data: folders } = useStorageFoldersQuery({ parentId: folderId });
  const { data: items } = useStorageItemsQuery({ parentId: folderId });

  return (
    <>
      <div className="flex-1 container z-10">
        {/* Upload btn dialog */}
        <UploadFileDialog
          files={files}
          setFiles={setFiles}
          parentFolderId={folderId}
        />
        <CreateFolderDialog parentFolderId={folderId} />

        <div className="flex justify-end"></div>

        {/* Items List */}
        <div className="grid grid-cols-12 gap-4 mt-4">
          {folders?.storageFolders.map(item => {
            return (
              <DriveItem
                key={item.id}
                storageItem={item}
                onClick={selectFolder}
              />
            );
          })}
          {items?.storageItems.map(item => {
            return (
              <DriveItem
                key={item.id}
                storageItem={item}
                onClick={onStorageItemClick}
              />
            );
          })}
        </div>
      </div>
    </>
  );
}

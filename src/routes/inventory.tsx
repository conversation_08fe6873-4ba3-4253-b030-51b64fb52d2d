import InventoryTabs, {
  InventoryLink
} from '@/components/inventory/inventory-tabs';
import { createFileRoute, Outlet } from '@tanstack/react-router';

export const Route = createFileRoute('/inventory')({
  component: RouteComponent,
  loader: ({ location }) => {
    const [, , activeTab] = location.pathname.split('/');

    return {
      activeTab: activeTab as InventoryLink,
      crumb: 'Inventory'
    };
  }
});

function RouteComponent() {
  const { activeTab } = Route.useLoaderData();

  return (
    <InventoryTabs activeTab={activeTab}>
      <Outlet />
    </InventoryTabs>
  );
}

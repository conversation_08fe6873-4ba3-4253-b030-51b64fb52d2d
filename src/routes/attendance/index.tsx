import { createFileRoute } from '@tanstack/react-router';
import { GetAttendanceByIdQuery, GetAttendanceByIdQueryVariables, useGetAllAttendanceQuery, useGetAttendanceByIdQuery } from '@/generated/graphql';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu';
import { Pencil } from 'lucide-react';
import Table from '@/components/ui/table/index';
import { format } from 'date-fns';
import { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { fetchData } from '@/client';
import UpdateAttendanceForm from '@/forms/attendance/UpdateAttendanceForm';
import { useUpdateAttendanceFormMethods } from '@/forms/attendance/UpdateAttendanceForm';
import _ from 'lodash';

const TableHeaderComponent = () => {
  return (
    <div className="mt-4 sm:flex sm:items-center">
      <div className="sm:flex-auto">
        <h1 className="text-base font-semibold text-gray-900">Attendance</h1>
        <p className="mt-2 text-sm text-gray-700">
          A list of all attendance records, including user details, shift
          timings, and locations.
        </p>
      </div>
    </div>
  );
};

export const Route = createFileRoute('/attendance/')({
  component: RouteComponent
});

function RouteComponent() {
  const { data: allAttendances } = useGetAllAttendanceQuery(undefined, {
    initialData: { allAttendances: [] }
  });
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [guards, setGuards] = useState<any[]>([]);
  const fm = useUpdateAttendanceFormMethods();

  const handleAttendanceEdit = async (attendanceId: string) => {
    setShowUpdateModal(true);
    fm.methods.setValue('attendanceId', attendanceId);
    const fetchAttendance = fetchData<GetAttendanceByIdQuery, GetAttendanceByIdQueryVariables>(
        useGetAttendanceByIdQuery.document,
        { attendanceId }
    );

    const { getAttendanceById: attendance } = await fetchAttendance();
    setGuards(_.get(attendance, 'shift.users') || []);
    fm.methods.setValue('locationId', _.get(attendance, 'location.id') || '');
    fm.methods.setValue('shiftId', _.get(attendance, 'shift.id') || '');
    fm.methods.setValue('userId', _.get(attendance, 'user.id') || '');
    fm.methods.setValue('date', new Date(_.get(attendance, 'date') || Date.now()));
    fm.methods.setValue('startTime', {
      date: new Date(_.get(attendance, 'startTime') || Date.now()),
      time: format(new Date(_.get(attendance, 'startTime') || Date.now()), 'HH:mm')
    });
    fm.methods.setValue('endTime', {
      date: new Date(_.get(attendance, 'endTime') || Date.now()),
      time: format(new Date(_.get(attendance, 'endTime') || Date.now()), 'HH:mm')
    });
    fm.methods.setValue('overtimeStartTime', {
      date: new Date(_.get(attendance, 'overtimeStartTime') || Date.now()),
      time: format(new Date(_.get(attendance, 'overtimeStartTime') || Date.now()), 'HH:mm')
    });
  };

  if (!allAttendances?.allAttendances) return null;

  const rowItems = allAttendances?.allAttendances.map(att => {
    return [
      att.user?.fullname,
      att.location?.name,
      format(att.date, 'dd-MM-yy'),
      att.timeSpentInMinutes,
      format(new Date(att.startTime), 'dd-MM-yy HH:mm'),
      att.endTime ? format(new Date(att.endTime), 'dd-MM-yy HH:mm') : '-',
      att.overTime ? format(new Date(att.overTime), 'dd-MM-yy HH:mm') : '-',
      <DropdownMenu>
        <DropdownMenuTrigger className="text-green-600">
          Action
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => handleAttendanceEdit(att.id)}>
            <Pencil />
            Edit
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ];
  });
  return (
    <div>
      <Dialog
        open={showUpdateModal}
        onOpenChange={(v: any) => {
          fm.methods.reset();
          setShowUpdateModal(v);
        }}
      >
        <DialogContent>
          <>
            <div className="flex flex-col gap-2">
              <DialogHeader>
                <DialogTitle className="text-left">
                  Update attendance details
                </DialogTitle>
                <DialogDescription className="text-left">
                  Update attendance details.
                </DialogDescription>
              </DialogHeader>
            </div>
            <Separator />
            <UpdateAttendanceForm {...fm} guards={guards} />
          </>
        </DialogContent>
      </Dialog>
      <Table.StickyTable
        tableHeadComponent={<TableHeaderComponent />}
        headerItems={[
          ['Full Name', 'Location', 'Date', 'Time spent', 'Start', 'End', 'OT', 'Actions']
        ]}
        rowItems={rowItems}
      />
    </div>
  );
}

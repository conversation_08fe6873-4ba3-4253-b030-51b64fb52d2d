import {
  InventoryType,
  useCreateInventoryMutation,
  UserRoles
} from '@/generated/graphql';
import { zodResolver } from '@hookform/resolvers/zod';
import { createFileRoute } from '@tanstack/react-router';
import { FormProvider, useFieldArray, useForm } from 'react-hook-form';
import FormField from '@/components/forms/FormField';

export const Route = createFileRoute('/inventory/create-inventory')({
  component: RouteComponent
});

import { z } from 'zod';
import FormSelect from '@/components/forms/FormSelect';
import { SelectItem } from '@/components/ui/select';
import { SelectGroup, SelectLabel } from '@/components/ui/select';
import { enumToOptions } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import FormChipInput, { tagsSchema } from '@/components/forms/FormChipInput';
import { useEffect } from 'react';
import FormTextArea from '@/components/forms/FormTextArea';
import { toast } from 'sonner';

// Define the enum for inventory types
const InventoryTypeEnum = z.nativeEnum(InventoryType);

// Define the attribute type schema
const AttributeTypeSchema = z.object({
  attibuteName: z.string(),
  attributeValues: tagsSchema
});

// Define the inventory item schema
const InventoryItemSchema = z.object({
  item: z.string().nonempty('Item is required'),
  quantity: z.coerce.number().nonnegative('Quantity must be a positive number'),
  sku: z.string().nonempty('SKU is required'),
  costPrice: z.coerce
    .number()
    .nonnegative('Cost price must be a positive number'),
  sellingPrice: z.coerce
    .number()
    .nonnegative('Selling price must be a positive number')
});

// Define the main inventory schema
export const InventorySchema = z.object({
  item: z.string().describe('Inventory item name').nonempty('Item is required'),
  description: z.string().optional(),
  attributes: z
    .array(AttributeTypeSchema)
    .min(1, 'At least one attribute is required'),
  items: z.array(InventoryItemSchema).min(1, 'At least one item is required'),
  type: InventoryTypeEnum
});

function combineArrays<T>(...arrays: T[][]): string[] {
  if (arrays.length === 0) return [];

  const combine = (arr1: string[], arr2: T[]): string[] => {
    const result: string[] = [];
    for (const item1 of arr1) {
      for (const item2 of arr2) {
        result.push(`${item1 ? `${item1}/` : ''}${item2}`);
      }
    }
    return result;
  };

  return arrays.reduce((acc, curr) => combine(acc, curr), ['']);
}

export type Inventory = z.infer<typeof InventorySchema>;

function RouteComponent() {
  const methods = useForm<Inventory>({
    resolver: zodResolver(InventorySchema),
    defaultValues: {
      item: '',
      description: '',
      attributes: [{ attibuteName: '', attributeValues: [] }],
      items: [],
      type: InventoryType.Guard
    }
  });

  const attributesFieldArray = useFieldArray({
    name: 'attributes',
    control: methods.control
  });

  const itemsFieldArray = useFieldArray({
    name: 'items',
    control: methods.control
  });

  useEffect(() => {
    const subscription = methods.watch(({ attributes, item }, { name }) => {
      if (name!.startsWith('attributes')) {
        const combineItems = combineArrays(
          ...attributes!.map(attr => attr!.attributeValues!.map(v => v!.text))
        );

        const items = combineItems.map(concat => ({
          item: `${item}-${concat}`,
          quantity: '' as unknown as number,
          sku: `${item} - ${concat}`.toUpperCase(),
          costPrice: '' as unknown as number,
          sellingPrice: '' as unknown as number
        }));

        methods.setValue('items', items);
      }
    });

    return () => subscription.unsubscribe();
  }, [methods]);

  const { mutateAsync: createInventory } = useCreateInventoryMutation();

  const onSubmit = methods.handleSubmit(data => {
    toast.promise(
      createInventory({
        input: {
          item: data.item,
          type: data.type,
          description: data.description,
          attributes: data.attributes.map(attr => ({
            attibuteName: attr.attibuteName,
            attributeValues: attr.attributeValues.map(v => v.text)
          })),
          items: data.items
        }
      }),
      {
        loading: 'Creating inventory...',
        success: () => {
          methods.reset();
          return 'Inventory created successfully';
        },
        error: 'Failed to create inventory'
      }
    );
  });

  return (
    <div className="flex flex-col gap-4">
      <FormProvider {...methods}>
        <form
          onSubmit={onSubmit}
          className="w-full space-y-4 bg-white border p-4 rounded-xl mt-4"
        >
          <div className="grid grid-cols-[auto,1fr] gap-5">
            <div className="self-start font-semibold text-2xl mb-3 text-accent-foreground">
              Create Inventory Form
            </div>
            <div></div>
            <div className="self-start font-semibold">Item Name</div>
            <div>
              <FormField name="item" placeholder="Item Name" />
            </div>

            <div className="self-start font-semibold">Description</div>
            <div>
              <FormTextArea name="description" placeholder="Description" />
            </div>

            <div className="self-start font-semibold">Type</div>
            <div>
              <FormSelect name="type" placeholder="Type">
                <SelectGroup>
                  <SelectLabel>Select Role</SelectLabel>
                  {enumToOptions(InventoryType).map(({ value, label }) => (
                    <SelectItem value={value} key={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </FormSelect>
            </div>

            <div className="self-start font-semibold">Attributes</div>
            <div className="space-y-2">
              {attributesFieldArray.fields.map(({ id }, idx) => {
                return (
                  <div key={id} className="flex gap-2">
                    <FormField
                      name={`attributes.${idx}.attibuteName`}
                      placeholder="Attributes"
                    />
                    <FormChipInput
                      label=""
                      name={`attributes.${idx}.attributeValues`}
                      placeholder="Attribute Values"
                    />
                  </div>
                );
              })}
            </div>

            <div className="self-start font-semibold">Items</div>
            <div className="space-y-2">
              {itemsFieldArray.fields.map(({ id }, idx) => {
                return (
                  <div key={id} className="flex items-center gap-4">
                    <div>
                      <FormField
                        name={`items.${idx}.item`}
                        placeholder="Item"
                      />
                    </div>
                    <div>
                      <FormField
                        name={`items.${idx}.quantity`}
                        placeholder="Quantity"
                      />
                    </div>
                    <div>
                      <FormField name={`items.${idx}.sku`} placeholder="SKU" />
                    </div>
                    <div>
                      <FormField
                        name={`items.${idx}.costPrice`}
                        placeholder="Cost Price"
                      />
                    </div>
                    <div>
                      <FormField
                        name={`items.${idx}.sellingPrice`}
                        placeholder="Selling Price"
                      />
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() =>
                attributesFieldArray.append({
                  attibuteName: '',
                  attributeValues: []
                })
              }
            >
              Add Attribute
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={() => attributesFieldArray.remove()}
            >
              Remove Attributes
            </Button>
            <Button
              onClick={() => methods.reset()}
              type="button"
              variant="destructive"
            >
              Reset Form
            </Button>
            <Button type="submit">Save</Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
}

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import Table from '@/components/ui/table/index';
import { InventoryType, useInventoryQuery } from '@/generated/graphql';
import { createFileRoute, Link } from '@tanstack/react-router';
import { Edit, MoreHorizontal, Plus } from 'lucide-react';
import { useMemo } from 'react';
import { z } from 'zod';

export const Route = createFileRoute('/inventory/')({
  component: RouteComponent,
  validateSearch: z.object({
    inventoryType: z.nativeEnum(InventoryType).default(InventoryType.Guard)
  }),
  loaderDeps: d => d
});

const HeaderComponent = () => {
  const nav = Route.useNavigate();
  const { inventoryType } = Route.useSearch();

  return (
    <div className="my-4 flex items-center justify-between">
      <div className="sm:flex-auto">
        <h1 className="text-base font-semibold text-gray-900">Inventory</h1>
        <p className="mt-2 text-sm text-gray-700">
          A list of all inventory items including their name and type.
        </p>
      </div>

      <div className="flex items-center gap-4">
        <Select
          value={inventoryType}
          onValueChange={value =>
            nav({ search: { inventoryType: value as InventoryType } })
          }
        >
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="Select Inventory Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={InventoryType.Guard}>Guard</SelectItem>
            <SelectItem value={InventoryType.Location}>Location</SelectItem>
          </SelectContent>
        </Select>

        <Button effect="shine" asChild>
          <Link to="/inventory/create-inventory">
            <Plus />
            Add Inventory
          </Link>
        </Button>
      </div>
    </div>
  );
};

function RouteComponent() {
  const { inventoryType } = Route.useSearch();
  const nav = Route.useNavigate();

  const { data } = useInventoryQuery(
    {
      input: {
        inventoryType
      }
    },
    { initialData: { inventory: [] } }
  );

  const rowItems = useMemo(() => {
    return data?.inventory.map(inventory => [
      inventory.item,
      inventory.type,
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            onClick={() => nav({ to: `/inventory/$inventoryId`, params: { inventoryId: inventory.id } })}
          >
            <Edit className="mr-2 h-4 w-4" />
            Edit Inventory
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ]);
  }, [data, nav]);

  return (
    <div>
      <Table.StickyTable
        headerItems={[['Item', 'Type', 'Actions']]}
        rowItems={rowItems}
        tableHeadComponent={<HeaderComponent />}
      />
    </div>
  );
}

import { createFileRoute } from '@tanstack/react-router';
import { useParams } from '@tanstack/react-router';
import { FormProvider, useFieldArray, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import FormField from '@/components/forms/FormField';
import FormSelect from '@/components/forms/FormSelect';
import FormTextArea from '@/components/forms/FormTextArea';
import FormChipInput from '@/components/forms/FormChipInput';
import { SelectGroup, SelectItem, SelectLabel } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { enumToOptions } from '@/lib/utils';
import {
  InventoryType,
  useInventoryByIdQuery,
  useUpdateInventoryMutation
} from '@/generated/graphql';
import { InventorySchema, type Inventory } from '../create-inventory';

export const Route = createFileRoute('/inventory/$inventoryId/')({
  component: RouteComponent,
});

function LoadingSkeleton() {
  return (
    <div className="space-y-4 p-4">
      <Skeleton className="h-8 w-1/4" />
      <div className="grid grid-cols-[auto,1fr] gap-5">
        <Skeleton className="h-6 w-24" />
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-6 w-24" />
        <Skeleton className="h-20 w-full" />
        <Skeleton className="h-6 w-24" />
        <Skeleton className="h-10 w-full" />
      </div>
    </div>
  );
}

function combineArrays<T>(...arrays: T[][]): string[] {
  if (arrays.length === 0) return [];

  const combine = (arr1: string[], arr2: T[]): string[] => {
    const result: string[] = [];
    for (const item1 of arr1) {
      for (const item2 of arr2) {
        result.push(`${item1 ? `${item1}/` : ''}${item2}`);
      }
    }
    return result;
  };

  return arrays.reduce((acc, curr) => combine(acc, curr), ['']);
}

function RouteComponent() {
  const { inventoryId } = useParams({ from: '/inventory/$inventoryId/' });
  
  const { data: inventoryData, isLoading } = useInventoryByIdQuery({
    id: inventoryId
  });

  const methods = useForm<Inventory>({
    resolver: zodResolver(InventorySchema),
    defaultValues: {
      item: '',
      description: '',
      attributes: [{ attibuteName: '', attributeValues: [] }],
      items: [{ item: '', costPrice: 0, quantity: 0, sellingPrice: 0, sku: '' }],
      type: InventoryType.Guard
    }
  });

  // Set form data when inventory is loaded
  useEffect(() => {
    if (inventoryData?.inventoryById) {
      const inventory = inventoryData.inventoryById;
      methods.reset({
        item: inventory.item,
        description: inventory.description || '',
        type: inventory.type as InventoryType,
        attributes: inventory.attributes?.map((attr: { attibuteName: any; attributeValues: any[]; }) => ({
          attibuteName: attr.attibuteName,
          attributeValues: attr.attributeValues.map(value => ({ text: value }))
        })) || [],
        items: inventory.items?.map((item: { item: any; quantity: any; sku: any; costPrice: any; sellingPrice: any; }) => ({
          item: item.item,
          quantity: item.quantity,
          sku: item.sku,
          costPrice: item.costPrice,
          sellingPrice: item.sellingPrice
        }))
      });
    }
  }, [inventoryData, methods]);

  const attributesFieldArray = useFieldArray({
    name: 'attributes',
    control: methods.control
  });

  const itemsFieldArray = useFieldArray({
    name: 'items',
    control: methods.control
  });

  useEffect(() => {
    const subscription = methods.watch(({ attributes, item }, { name = '' }) => {
      if (name.startsWith('attributes') && item) {
        // Get current items for value preservation
        const currentItems = methods.getValues('items') || [];
        const existingItemsMap = new Map(
          currentItems.map(item => [item.item, item])
        );

        // Filter out invalid attributes
        const validAttributes = attributes?.filter(
          attr => attr && Array.isArray(attr.attributeValues)
        );

        if (validAttributes?.length) {
          const combineItems = combineArrays(
            ...validAttributes.map(attr => 
              attr?.attributeValues?.map(v => v?.text) ?? []
            )
          );
          
          const items = combineItems.map(concat => {
            const newItemName = `${item}-${concat}`;
            const existingItem = existingItemsMap.get(newItemName);

            // If item exists, preserve its values
            if (existingItem) {
              return {
                ...existingItem,
                sku: `${item}-${concat}`.toUpperCase() // Update SKU in case item name changed
              };
            }

            // Otherwise create new item with default values
            return {
              item: newItemName,
              quantity: 0,
              sku: `${item}-${concat}`.toUpperCase(),
              costPrice: 0,
              sellingPrice: 0
            };
          });

          methods.setValue('items', items, { shouldValidate: true });
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [methods]);

  const { mutateAsync: updateInventory } = useUpdateInventoryMutation();

  const onSubmit = methods.handleSubmit(async (data) => {
    try {
      await updateInventory({
        id: inventoryId,
        updateInventoryInput: {
          item: data.item,
          type: data.type,
          description: data.description,
          attributes: data.attributes.map(attr => ({
            attibuteName: attr.attibuteName,
            attributeValues: attr.attributeValues.map(v => v.text)
          })),
          items: data.items
        }
      });
      
      toast.success('Inventory updated successfully');
    } catch (error) {
      console.error('Update error:', error);
      toast.error('Failed to update inventory');
    }
  });

  if (isLoading) {
    return <LoadingSkeleton />;
  }

  return (
    <div className="flex flex-col gap-4">
      <FormProvider {...methods}>
        <form
          onSubmit={onSubmit}
          className="w-full space-y-4 bg-white border p-4 rounded-xl mt-4"
        >
          <div className="grid grid-cols-[auto,1fr] gap-5">
            <div className="self-start font-semibold text-2xl mb-3 text-accent-foreground">
              Edit Inventory
            </div>
            <div></div>

            <div className="self-start font-semibold">Item Name</div>
            <div>
              <FormField name="item" placeholder="Item Name" />
            </div>

            <div className="self-start font-semibold">Description</div>
            <div>
              <FormTextArea name="description" placeholder="Description" />
            </div>

            <div className="self-start font-semibold">Type</div>
            <div>
              <FormSelect name="type" placeholder="Type">
                <SelectGroup>
                  <SelectLabel>Select Type</SelectLabel>
                  {enumToOptions(InventoryType).map(({ value, label }) => (
                    <SelectItem value={value} key={value}>
                      {label}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </FormSelect>
            </div>

            <div className="self-start font-semibold">Attributes</div>
            <div className="space-y-2">
              {attributesFieldArray?.fields?.map(({ id }, idx) => (
                <div key={id} className="flex gap-2">
                  <FormField
                    name={`attributes.${idx}.attibuteName`}
                    placeholder="Attributes"
                  />
                  <FormChipInput
                    label=""
                    name={`attributes.${idx}.attributeValues`}
                    placeholder="Attribute Values"
                  />
                </div>
              ))}
            </div>

            <div className="self-start font-semibold">Items</div>
            <div className="space-y-2">
              {itemsFieldArray?.fields?.map(({ id }, idx) => (
                <div key={id} className="flex items-center gap-4">
                  <FormField
                    name={`items.${idx}.item`}
                    placeholder="Item"
                  />
                  <FormField
                    name={`items.${idx}.quantity`}
                    placeholder="Quantity"
                  />
                  <FormField 
                    name={`items.${idx}.sku`} 
                    placeholder="SKU" 
                  />
                  <FormField
                    name={`items.${idx}.costPrice`}
                    placeholder="Cost Price"
                  />
                  <FormField
                    name={`items.${idx}.sellingPrice`}
                    placeholder="Selling Price"
                  />
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() =>
                attributesFieldArray.append({
                  attibuteName: '',
                  attributeValues: []
                })
              }
            >
              Add Attribute
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={() => attributesFieldArray.remove()}
            >
              Remove Attributes
            </Button>
            <Button type="submit">Update Inventory</Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
}

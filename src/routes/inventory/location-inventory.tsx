import { InventoryType, useInventoryRequestQuery, InventoryRequest, RequestStatus, useUpdateInventoryRequestMutation } from '@/generated/graphql';
import { createFileRoute } from '@tanstack/react-router';
import { useMemo, useState } from 'react';
import Table from '@/components/ui/table/index';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu';
import { FileText } from 'lucide-react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { InventoryRequestDetails } from '@/components/inventory/InventoryRequestDetails';
import { useCurrentUser } from '@/lib/auth';

export const Route = createFileRoute('/inventory/location-inventory')({
  component: RouteComponent
});

const HeaderComponent = () => {
  return (
    <div className="my-4 flex items-center justify-between">
      <div className="sm:flex-auto">
        <h1 className="text-base font-semibold text-gray-900">
          Location Inventory Request
        </h1>
        <p className="mt-2 text-sm text-gray-700">
          A list of all location inventory request items.
        </p>
      </div>
    </div>
  );
};

function RouteComponent() {
  const [selectedRequest, setSelectedRequest] = useState<InventoryRequest | null>(null);
    const { userId } = useCurrentUser();
    const { data: inventoryRequests, refetch } = useInventoryRequestQuery({
      input: { inventoryType: InventoryType.Location }
    });
  
    const { mutateAsync: updateInventoryRequest } = useUpdateInventoryRequestMutation();
  
    const handleStatusUpdate = async (status: RequestStatus) => {
      if (!selectedRequest) return;
      
      await updateInventoryRequest({
          id: selectedRequest.id,
          UpdateInventoryRequestInput: { status, ...(status === RequestStatus.Accepted && { acceptedBy: userId }) }
      });
      
      await refetch();
    };
  const rowItems = useMemo(() => {
    return (
      inventoryRequests?.inventoryRequests?.map(request => {
        return [
          request.inventory?.item ?? '—',
          request.requestedBy?.fullname ?? '—',
          request.inventoryType ?? '—',
          request.status ?? '—',
          new Date(request.createdAt).toLocaleString(),
          <DropdownMenu>
            <DropdownMenuTrigger className="text-green-600">
              Action
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => setSelectedRequest(request as InventoryRequest)}
              >
                <div className="flex items-center">
                  <FileText className="w-5 h-5 text-muted-foreground mr-2" />
                  Review
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        ];
      }) ?? []
    );
  }, [inventoryRequests]);
  return (
    <div>
      <Table.StickyTable
        headerItems={[
          [
            'Inventory',
            'Requested By',
            'Type',
            'Status',
            'Requested At',
            'Actions'
          ]
        ]}
        rowItems={rowItems}
        tableHeadComponent={<HeaderComponent />}
      />

      <Dialog
        open={!!selectedRequest}
        onOpenChange={open => !open && setSelectedRequest(null)}
      >
        <DialogContent className="max-w-3xl">
          {selectedRequest && (
            <InventoryRequestDetails
              request={selectedRequest}
              onStatusChange={handleStatusUpdate}
              currentUserId={userId}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}

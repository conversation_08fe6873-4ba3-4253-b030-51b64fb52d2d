import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu';
import { createFileRoute } from '@tanstack/react-router';
import { Calendar, Pencil, Trash2 } from 'lucide-react';
import Table from '@/components/ui/table/index';
import { format } from 'date-fns';
import { useState } from 'react';
import { useHolidaysQuery, useDeleteHolidayMutation } from '@/generated/graphql';
import CreateHolidayForm from '@/forms/holidays/CreateHolidayForm';
import { toast } from 'sonner';
import UpdateHolidayForm, { useUpdateHolidayForm } from '@/forms/holidays/UpdateHolidayForm';

const TableHeaderComponent = () => {
  return (
    <div className="mt-4 sm:flex sm:items-center">
      <div className="sm:flex-auto">
        <h1 className="text-base font-semibold text-gray-900">
          Holiday Management
        </h1>
        <p className="mt-2 text-sm text-gray-700">
          A list of all holidays including their type and dates.
        </p>
      </div>
      <Dialog>
        <DialogTrigger asChild>
          <Button>Create Holiday</Button>
        </DialogTrigger>
        <DialogContent>
          <>
            <div className="flex flex-col gap-2">
              <div
                className="flex size-11 shrink-0 items-center justify-center rounded-full border border-border"
                aria-hidden="true"
              >
                <Calendar className="opacity-80" size={16} strokeWidth={2} />
              </div>
              <DialogHeader>
                <DialogTitle className="text-left">Create Holiday</DialogTitle>
                <DialogDescription className="text-left">
                  Add a new holiday to the calendar
                </DialogDescription>
              </DialogHeader>
            </div>
            <Separator />
            <CreateHolidayForm />
          </>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export const Route = createFileRoute('/holidays/')({
  component: RouteComponent
});

function RouteComponent() {
  // Modify the query hook to get refetch function
  const { data: holidays, refetch } = useHolidaysQuery();
  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const { mutateAsync: deleteHoliday } = useDeleteHolidayMutation();
  const fm = useUpdateHolidayForm();

  const handleDelete = async (id: string) => {
    toast.promise(
      deleteHoliday({ holidayId: id }).then(() => refetch()), // Chain refetch after successful deletion
      {
        loading: 'Deleting holiday...',
        success: 'Holiday deleted successfully',
        error: 'Failed to delete holiday'
      }
    );
  };

  const handleHolidayEdit = async (holidayId: string) => {
    setIsUpdateDialogOpen(true);
    fm.methods.setValue('id', holidayId);

    const holiday = holidays?.holidays.find(h => h.id === holidayId);
    if (holiday) {
      fm.methods.setValue('name', holiday.name);
      fm.methods.setValue('date', new Date(holiday.date));
      fm.methods.setValue('description', holiday.description);
    }
  };

  if (!holidays?.holidays) return null;

  const rowItems = holidays.holidays.map(holiday => {
    return [
      holiday.name,
      holiday.date ? format(new Date(holiday.date), 'dd-MM-yyyy') : '-',
      holiday.description,
      <DropdownMenu>
        <DropdownMenuTrigger className="text-green-600">
          Action
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => handleHolidayEdit(holiday.id)}>
            <Pencil className="mr-2 h-4 w-4" />
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={() => handleDelete(holiday.id)}
            className="text-red-600"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Remove
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ];
  });

  return (
    <div>
      <Table.StickyTable
        tableHeadComponent={<TableHeaderComponent />}
        headerItems={[
          [
            'Name',
            'Date',
            'Description',
            'Actions'
          ]
        ]}
        rowItems={rowItems}
      />
      <Dialog
        open={isUpdateDialogOpen}
        onOpenChange={v => {
          fm.methods.reset();
          setIsUpdateDialogOpen(v);
        }}
      >
        <DialogContent>
          <>
            <div className="flex flex-col gap-2">
              <div
                className="flex size-11 shrink-0 items-center justify-center rounded-full border border-border"
                aria-hidden="true"
              >
                <Calendar className="opacity-80" size={16} strokeWidth={2} />
              </div>
              <DialogHeader>
                <DialogTitle className="text-left">Update Holiday</DialogTitle>
                <DialogDescription className="text-left">
                  Update existing holiday details
                </DialogDescription>
              </DialogHeader>
            </div>
            <Separator />
            <UpdateHolidayForm methods={fm.methods} />
          </>
        </DialogContent>
      </Dialog>
    </div>
  );
}

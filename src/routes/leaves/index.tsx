import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu';
import { createFileRoute } from '@tanstack/react-router';
import { Calendar, Pencil } from 'lucide-react';
import Table from '@/components/ui/table/index';
import { LeaveStatus, useLeavesQuery, useMeQuery, UserRoles, UserStatus, useUsersQuery } from '@/generated/graphql';
import { format } from 'date-fns';
import CreateLeaveForm from '@/forms/leaves/CreateLeaveForm';
import { useState } from 'react';
import UpdateLeaveForm from '@/forms/leaves/UpdateLeaveForm';
import { useUpdateLeaveFormMethods } from '@/forms/leaves/UpdateLeaveForm';
import {
  GetLeaveByIdQuery,
  GetLeaveByIdQueryVariables,
  useGetLeaveByIdQuery
} from '@/generated/graphql';
import { fetchData } from '@/client';
import _ from 'lodash';

interface TableHeaderComponentProps {
  users: {
    id: string;
    fullname: string;
    phone: string;
    userStatus: UserStatus;
    role: UserRoles;
  }[];
}

const TableHeaderComponent: React.FC<TableHeaderComponentProps> = ({ users }) => {
  return (
    <div className="mt-4 sm:flex sm:items-center">
      <div className="sm:flex-auto">
        <h1 className="text-base font-semibold text-gray-900">
          Leave Management
        </h1>
        <p className="mt-2 text-sm text-gray-700">
          A list of all leaves including their type, status, dates and
          description.
        </p>
      </div>
      {/* Create Leave Dialog */}
      <Dialog>
        <DialogTrigger asChild>
          <Button>Create Leave</Button>
        </DialogTrigger>
        <DialogContent>
          <>
            <div className="flex flex-col gap-2">
              <div
                className="flex size-11 shrink-0 items-center justify-center rounded-full border border-border"
                aria-hidden="true"
              >
                <Calendar className="opacity-80" size={16} strokeWidth={2} />
              </div>
              <DialogHeader>
                <DialogTitle className="text-left">Create Leave</DialogTitle>
                <DialogDescription className="text-left">
                  Submit a new leave request
                </DialogDescription>
              </DialogHeader>
            </div>
            <Separator />
            <CreateLeaveForm users={users || []} />
          </>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export const Route = createFileRoute('/leaves/')({
  component: RouteComponent
});

function RouteComponent() {
  const { data: leaves } = useLeavesQuery();
  const { data: users } = useUsersQuery({ input: {} });
  const { data: me } = useMeQuery();

  const [isUpdateDialogOpen, setIsUpdateDialogOpen] = useState(false);
  const fm = useUpdateLeaveFormMethods();

  const handleLeaveEdit = async (leaveId: string) => {
    setIsUpdateDialogOpen(true);
    fm.methods.setValue('id', leaveId);

    const fetchLeave = fetchData<GetLeaveByIdQuery, GetLeaveByIdQueryVariables>(
      useGetLeaveByIdQuery.document,
      { leaveId }
    );

    const { leave } = await fetchLeave();

    fm.methods.setValue('leaveType', _.get(leave, 'leaveType'));
    fm.methods.setValue('user', _.get(leave, 'user.id') || '');
    fm.methods.setValue(
      'startDateTime',
      new Date(_.get(leave, 'startDateTime'))
    );
    fm.methods.setValue('endDateTime', new Date(_.get(leave, 'endDateTime')));
    fm.methods.setValue('reason', _.get(leave, 'reason') || '');
    fm.methods.setValue('leaveStatus', _.get(leave, 'leaveStatus') || LeaveStatus.Pending);
    fm.methods.setValue('rejectedReason', _.get(leave, 'rejectedReason') || '');
  };

  if (!leaves?.leaves) return null;

  const rowItems = leaves.leaves.map(leave => {
    return [
      leave.user?.fullname,
      leave.leaveType,
      leave.leaveStatus,
      leave.startDateTime
        ? format(new Date(leave.startDateTime), 'dd-MM-yy')
        : '-',
      leave.endDateTime
        ? format(new Date(leave.endDateTime), 'dd-MM-yy')
        : '-',
      leave.reason,
      <DropdownMenu>
        <DropdownMenuTrigger className="text-green-600">
          Action
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => handleLeaveEdit(leave.id)}>
            <Pencil className="mr-2 h-4 w-4" />
            Edit
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ];
  });

  return (
    <div>
      <Table.StickyTable
        tableHeadComponent={<TableHeaderComponent users={users?.users || []} />}
        headerItems={[
          [
            'Full Name',
            'Type',
            'Status',
            'Start Date',
            'End Date',
            'Reason',
            'Actions'
          ]
        ]}
        rowItems={rowItems}
      />

      {/* Update Leave Dialog */}
      <Dialog
        open={isUpdateDialogOpen}
        onOpenChange={v => {
          fm.methods.reset();
          setIsUpdateDialogOpen(v);
        }}
      >
        <DialogContent>
          <>
            <div className="flex flex-col gap-2">
              <div
                className="flex size-11 shrink-0 items-center justify-center rounded-full border border-border"
                aria-hidden="true"
              >
                <Calendar className="opacity-80" size={16} strokeWidth={2} />
              </div>
              <DialogHeader>
                <DialogTitle className="text-left">Update Leave</DialogTitle>
                <DialogDescription className="text-left">
                  Update existing leave request
                </DialogDescription>
              </DialogHeader>
            </div>
            <Separator />
            <UpdateLeaveForm methods={fm.methods} users={users?.users || []} currentUserId={me?.me?.id || ''} />
          </>
        </DialogContent>
      </Dialog>
    </div>
  );
}

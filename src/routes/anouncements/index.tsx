import { createFileRoute } from '@tanstack/react-router';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Pencil } from 'lucide-react';
import Table from '@/components/ui/table/index';
import { format } from 'date-fns';
import { Separator } from '@/components/ui/separator';
import { useAnnouncementsQuery, useUsersQuery } from '@/generated/graphql';
import CreateAnnouncementForm from '@/forms/announcements/CreateAnnouncementForm';
import UpdateAnnouncementForm, {
  useUpdateAnnouncementFormMethods
} from '@/forms/announcements/UpdateAnnouncementForm';
import { useState } from 'react';

const TableHeaderComponent = ({ users }: { users: any }) => {
  return (
    <div className="mt-4 sm:flex sm:items-center">
      <div className="sm:flex-auto">
        <h1 className="text-base font-semibold text-gray-900">Announcements</h1>
        <p className="mt-2 text-sm text-gray-700">
          A list of all anouncements including their details and target
          audience.
        </p>
      </div>
      <Dialog>
        <DialogTrigger asChild>
          <Button>Create Announcement</Button>
        </DialogTrigger>
        <DialogContent>
          <>
            <DialogHeader>
              <DialogTitle className="text-left">
                Create Announcement
              </DialogTitle>
              <DialogDescription className="text-left">
                Create a new announcement for users or roles.
              </DialogDescription>
            </DialogHeader>
            <Separator />
            <CreateAnnouncementForm users={users} />
          </>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export const Route = createFileRoute('/anouncements/')({
  component: RouteComponent
});

function RouteComponent() {
  const { data: anouncements } = useAnnouncementsQuery();
  const { data: users } = useUsersQuery({ input: {} });
  const [editingAnnouncement, setEditingAnnouncement] = useState<any>(null);
  const updateMethods = useUpdateAnnouncementFormMethods();

  if (!anouncements?.anouncements || !users?.users) return null;

  const handleEdit = (announcement: any, e?: Event) => {

    setEditingAnnouncement(announcement);
    updateMethods.methods.reset({
      announcementId: announcement.id,
      title: announcement.title,
      description: announcement.description,
      date: new Date(announcement.date),
      document: announcement.document || '',
      users:
        announcement.users?.map((user: any) => ({
          label: user.fullname,
          value: user.id
        })) || [],
      userRoles:
        announcement.userRoles?.map((role: string) => ({
          label: role,
          value: role
        })) || []
    });
  };

  const rowItems = anouncements.anouncements.map((announcement: any) => {
    const getRoleDisplay = () => {
      if (!announcement.userRoles?.length && !announcement.users?.length) {
        return '-';
      }
      if (announcement.userRoles?.length) {
        return announcement.userRoles.join(', ');
      }
      return 'Specific Users';
    };

    return [
      announcement.title,
      announcement.description,
      format(new Date(announcement.date), 'dd-MM-yyyy'),
      getRoleDisplay(),
      announcement.document ? 'Yes' : 'No',
      <DropdownMenu>
        <DropdownMenuTrigger className="text-green-600">
          Action
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <Dialog>
            <DialogTrigger asChild>
              <DropdownMenuItem
                onSelect={e => {
                  e.preventDefault();
                  handleEdit(announcement, e);
                }}
                onClick={e => e.stopPropagation()}
              >
                <Pencil className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
            </DialogTrigger>
            <DialogContent>
              <>
                <DialogHeader>
                  <DialogTitle className="text-left">
                    Update Announcement
                  </DialogTitle>
                  <DialogDescription className="text-left">
                    Update the announcement details.
                  </DialogDescription>
                </DialogHeader>
                <Separator />
                {editingAnnouncement && (
                  <UpdateAnnouncementForm
                    methods={updateMethods.methods}
                    onSubmit={updateMethods.onSubmit}
                    users={users?.users}
                  />
                )}
              </>
            </DialogContent>
          </Dialog>
        </DropdownMenuContent>
      </DropdownMenu>
    ];
  });

  return (
    <div>
      <Table.StickyTable
        tableHeadComponent={<TableHeaderComponent users={users?.users} />}
        headerItems={[
          [
            'Title',
            'Description',
            'Date',
            'Target Roles',
            'Has Document',
            'Actions'
          ]
        ]}
        rowItems={rowItems}
      />
    </div>
  );
}

import { createFileRoute } from '@tanstack/react-router';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, FormProvider } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useState, ChangeEvent, useRef } from 'react';
import { toast } from 'sonner';
import {
  useIndexFaceMutation,
  useClearFaceMutation,
  useUserProfileQuery
} from '@/generated/graphql';
import { X } from 'lucide-react';
import UserProfileForm, {
  useUpdateUserProfileFormMethods
} from '@/forms/auth/UpdateUserProfileForm';
import { UserDocumentForm } from '@/forms/auth/UserDocumentForm';

const faceIndexSchema = z.object({
  userId: z.string(),
  base64Img: z.string().optional()
});

type FaceIndexFormValues = z.infer<typeof faceIndexSchema>;

function RouteComponent() {
  const { userId } = Route.useParams();
  const { data: userProfile, isLoading } = useUserProfileQuery({
    userId
  });
  const [preview, setPreview] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { mutateAsync: indexFace } = useIndexFaceMutation();
  const { mutateAsync: clearFaceIndex } = useClearFaceMutation();

  const methods = useForm<FaceIndexFormValues>({
    resolver: zodResolver(faceIndexSchema),
    defaultValues: {
      userId
    }
  });

  const handleImageSelect = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      const reader = new FileReader();

      reader.onloadend = () => {
        const base64String = reader.result as string;
        methods.setValue('base64Img', base64String);
        setPreview(base64String);
      };

      reader.readAsDataURL(file);
    }
  };

  const handleFaceIndex = methods.handleSubmit(async data => {
    try {
      if (!data.base64Img) {
        toast.error('Please select an image');
        return;
      }

      await toast.promise(
        indexFace({
          indexFaceInput: {
            base64Img: data.base64Img.split(',')[1], // Remove data:image prefix
            userId: data.userId
          }
        }),
        {
          loading: 'Registering face...',
          success: 'Face registered successfully',
          error: 'Failed to register face'
        }
      );

      // Reset the file input and preview after successful upload
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      setPreview(null);
      methods.setValue('base64Img', undefined);
    } catch (error) {
      console.error(error);
    }
  });

  const handleClearFaceIndex = async () => {
    try {
      await toast.promise(
        clearFaceIndex({
          input: {
            userId
          }
        }),
        {
          loading: 'Clearing face index...',
          success: 'Face index cleared successfully',
          error: 'Failed to clear face index'
        }
      );
      setPreview(null);
      methods.reset();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="container py-10">
      <div className="space-y-6">
        {isLoading ? (
          <Card>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* User Profile Form */}
            <UserProfileForm
              userId={userId}
              initialData={{
                ic: userProfile?.userProfile?.ic ?? '',
                ID: userProfile?.userProfile?.ID ?? '',
                passport: userProfile?.userProfile?.passport ?? '',
                passportExpiresAt: userProfile?.userProfile?.passportExpiresAt
                  ? new Date(userProfile.userProfile.passportExpiresAt)
                  : undefined,
                permitNumber: userProfile?.userProfile?.permitNumber ?? '',
                permitExpiresAt: userProfile?.userProfile?.permitExpiresAt
                  ? new Date(userProfile.userProfile.permitExpiresAt)
                  : undefined,
                gender:
                  (userProfile?.userProfile?.gender as
                    | 'male'
                    | 'female'
                    | 'other'
                    | undefined) ?? undefined,
                dob: userProfile?.userProfile?.dob
                  ? new Date(userProfile.userProfile.dob)
                  : undefined,
                placeOfBirth: userProfile?.userProfile?.placeOfBirth ?? '',
                currentAddress: userProfile?.userProfile?.currentAddress ?? '',
                joinedAt: userProfile?.userProfile?.joinedAt
                  ? new Date(userProfile.userProfile.joinedAt)
                  : undefined,
                maritalStatus:
                  (userProfile?.userProfile?.maritalStatus as
                    | 'single'
                    | 'married'
                    | 'divorced'
                    | 'widowed'
                    | undefined) ?? undefined,
                bankAccNumber: userProfile?.userProfile?.bankAccNumber ?? '',
                bankName: userProfile?.userProfile?.bankName ?? '',
                emergencyContact:
                  userProfile?.userProfile?.emergencyContact ?? []
              }}
            />

            {/* User Documents Form */}
            <UserDocumentForm userId={userId} />

            {/* Face Registration Card */}
            <Card>
              <CardHeader>
                <CardTitle>Face Registration</CardTitle>
              </CardHeader>
              <CardContent>
                <FormProvider {...methods}>
                  <form onSubmit={handleFaceIndex} className="space-y-6">
                    <div className="flex flex-col gap-4">
                      <div>
                        <label
                          htmlFor="face"
                          className="block text-sm font-medium text-gray-700 mb-2"
                        >
                          Register Guard Face Index
                        </label>
                        <input
                          ref={fileInputRef}
                          type="file"
                          id="face"
                          className="block w-full text-sm text-gray-500
                            file:mr-4 file:py-2 file:px-4
                            file:rounded-md file:border-0
                            file:text-sm file:font-semibold
                            file:bg-primary file:text-white
                            hover:file:bg-primary/90"
                          accept="image/*"
                          onChange={handleImageSelect}
                        />
                      </div>

                      {preview && (
                        <div className="mt-4 relative">
                          <img
                            src={preview}
                            alt="Preview"
                            className="w-32 h-32 object-cover rounded-md"
                          />
                          <button
                            type="button"
                            onClick={() => {
                              setPreview(null);
                              if (fileInputRef.current) {
                                fileInputRef.current.value = '';
                              }
                              methods.setValue('base64Img', undefined);
                            }}
                            className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full p-1 hover:bg-destructive/90"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      )}

                      <div className="flex gap-4">
                        <Button type="submit" className="flex-1">
                          Upload Image
                        </Button>
                        <Button
                          type="button"
                          variant="destructive"
                          onClick={handleClearFaceIndex}
                        >
                          Clear Face Index
                        </Button>
                      </div>
                    </div>
                  </form>
                </FormProvider>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </div>
  );
}

export const Route = createFileRoute('/users/$userId/')({
  component: RouteComponent
});

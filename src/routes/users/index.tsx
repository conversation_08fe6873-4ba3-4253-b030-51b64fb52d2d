import { fetchData } from '@/client';
import Badge from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Separator } from '@/components/ui/separator';
import Table from '@/components/ui/table/index';
import {
  CreateUserForm,
  UpdateUserForm,
  useCreateUserFormMethods
} from '@/forms';
import { useUpdateUserFormMethods } from '@/forms/auth/UpdateUserForm';
import {
  UserQuery,
  UserQueryVariables,
  UserStatus,
  useUserQuery,
  useUsersQuery
} from '@/generated/graphql';
import { cn } from '@/lib/utils';

import { createFileRoute, Link } from '@tanstack/react-router';
import _ from 'lodash';
import { Pencil, User, UserRoundPen } from 'lucide-react';
import { useState } from 'react';

export const Route = createFileRoute('/users/')({
  component: RouteComponent,
  loader: async () => ({})
});

const TableHeaderComponent = () => {
  const fm = useCreateUserFormMethods();

  return (
    <div className="mt-4 sm:flex sm:items-center">
      <div className="sm:flex-auto">
        <h1 className="text-base font-semibold text-gray-900">Users</h1>
        <p className="mt-2 text-sm text-gray-700">
          A list of all the users in your account including their name, title,
          email and role.
        </p>
      </div>
      <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
        {/* create user dialog modal*/}
        <Dialog>
          <DialogTrigger asChild>
            <Button>Create User</Button>
          </DialogTrigger>
          <DialogContent>
            <>
              <div className="flex flex-col gap-2">
                <div
                  className="flex size-11 shrink-0 items-center justify-center rounded-full border border-border"
                  aria-hidden="true"
                >
                  <UserRoundPen
                    className="opacity-80"
                    size={16}
                    strokeWidth={2}
                  />
                </div>
                <DialogHeader>
                  <DialogTitle className="text-left">Create user</DialogTitle>
                  <DialogDescription className="text-left">
                    create a new user
                  </DialogDescription>
                </DialogHeader>
              </div>
              <Separator />
              <CreateUserForm {...fm} />
            </>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

function RouteComponent() {
  const { data: users, isLoading } = useUsersQuery({ input: {} });
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const fm = useUpdateUserFormMethods(() => setShowUpdateModal(false));

  const handleSelectedUserProfile = async (userId: string) => {
    setShowUpdateModal(true);
    fm.methods.setValue('userId', userId);

    const fetchUser = fetchData<UserQuery, UserQueryVariables>(
      useUserQuery.document,
      { userId }
    );
    const { user } = await fetchUser();

    Object.entries(
      _.pick(user, ['fullname', 'phone', 'role', 'userStatus', 'employeeId'])
    ).forEach(([key, value]) => {
      // @ts-expect-error
      fm.methods.setValue(key, value);
    });
  };

  if (!users?.users) return null;

  const rowItems = users.users.map(user => {
    return [
      user.fullname,
      user.phone,
      user.role,
      <Badge
        className={cn(
          user.userStatus === UserStatus.Inactive &&
            'bg-red-50 text-red-700 ring-red-600/20'
        )}
      >
        {user.userStatus ?? 'active'}
      </Badge>,
      <DropdownMenu>
        <DropdownMenuTrigger className="text-green-600">
          Edit
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem asChild>
            <Link to="/users/$userId" params={{ userId: user.id }}>
              <User />
              Profile
            </Link>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => handleSelectedUserProfile(user.id)}>
            <Pencil />
            Edit
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    ];
  });

  return (
    <div>
      {/* update user dialog modal*/}
      <Dialog
        open={showUpdateModal}
        onOpenChange={v => {
          fm.methods.reset();
          setShowUpdateModal(v);
        }}
      >
        <DialogContent>
          <>
            <div className="flex flex-col gap-2">
              <div
                className="flex size-11 shrink-0 items-center justify-center rounded-full border border-border"
                aria-hidden="true"
              >
                <UserRoundPen
                  className="opacity-80"
                  size={16}
                  strokeWidth={2}
                />
              </div>
              <DialogHeader>
                <DialogTitle className="text-left">
                  Update user details
                </DialogTitle>
                <DialogDescription className="text-left">
                  Update your user details and active status.
                </DialogDescription>
              </DialogHeader>
            </div>
            <Separator />
            <UpdateUserForm {...fm} />
          </>
        </DialogContent>
      </Dialog>
      <Table.StickyTable
        tableHeadComponent={<TableHeaderComponent />}
        headerItems={[['Full Name', 'Phone', 'Role', 'Status', 'Actions']]}
        rowItems={rowItems}
      />
    </div>
  );
}

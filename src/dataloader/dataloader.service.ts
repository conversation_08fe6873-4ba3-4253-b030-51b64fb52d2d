/* eslint-disable @typescript-eslint/ban-ts-comment */
import { Injectable } from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import DataLoader from 'dataloader';
import { Connection, Document } from 'mongoose';
import { Location } from 'src/location/entities/location.entity';
import { LocationService } from 'src/location/location.service';
import { Shift } from 'src/shifts/entities/shift.entity';
import { Storage } from 'src/storage/entities/storage.entity';
import { User } from 'src/users/entities/user.entity';
import { UsersService } from 'src/users/users.service';
import { Inventory } from 'src/inventory/entities/inventory.entity';

@Injectable()
export class DataloaderService {
  constructor(
    @InjectConnection() private readonly connection: Connection,
    private readonly userService: UsersService,
    private readonly locationService: LocationService,
  ) {}

  private createDataLoader<T extends Document>(modelName: string) {
    const model = this.connection.model<T>(modelName);

    // @ts-expect-error
    return new DataLoader<string, T>(async (keys) => {
      const results = await model.find({
        _id: { $in: [...new Set(keys.map(String))] },
      });

      return keys.map(
        (key) =>
          results.find((doc) => String(doc._id) === key.toString()) || null,
      );
    });
  }

  getLoaders() {
    const usersLoader = this.createDataLoader(User.name);
    const storageLoader = this.createDataLoader(Storage.name);
    const locationLoader = this.createDataLoader(Location.name);
    const shiftLoader = this.createDataLoader(Shift.name);
    const inventoryLoader = this.createDataLoader(Inventory.name);

    return {
      usersLoader,
      storageLoader,
      locationLoader,
      shiftLoader,
      inventoryLoader,
    };
  }
}

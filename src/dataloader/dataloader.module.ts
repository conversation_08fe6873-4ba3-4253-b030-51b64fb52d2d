import { Global, Module } from '@nestjs/common';
import { LocationModule } from 'src/location/location.module';
import { DataloaderService } from './dataloader.service';
import { StorageModule } from 'src/storage/storage.module';
import { UsersModule } from 'src/users/users.module';
import { ShiftsModule } from 'src/shifts/shifts.module';

@Global()
@Module({
  imports: [LocationModule, StorageModule, UsersModule, ShiftsModule],
  providers: [DataloaderService],
  exports: [DataloaderService],
})
export class DataloaderModule {}

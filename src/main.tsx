import { RouterProvider, createRouter } from '@tanstack/react-router';
import ReactDOM from 'react-dom/client';
import './index.css';
import { routeTree } from './routeTree.gen';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { errorHandler } from './lib/utils';

export const queryClient = new QueryClient({
  defaultOptions: {
    mutations: {
      onError(err) {
        console.log('err');
        errorHandler(err);
      }
    }
  }
});

// Set up a Router instance
const router = createRouter({
  routeTree,
  defaultPreload: 'intent',
  Wrap(props) {
    return (
      <QueryClientProvider client={queryClient}>
        {props.children}
      </QueryClientProvider>
    );
  }
});

// Register things for typesafety
declare module '@tanstack/react-router' {
  interface Register {
    router: typeof router;
  }
}

const rootElement = document.getElementById('app')!;

if (!rootElement.innerHTML) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(<RouterProvider router={router} />);
}

import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { PaymentConfigService } from './payment-config.service';
import { PaymentConfig } from './entities/payment-config.entity';
import { CreatePaymentConfigInput } from './dto/create-payment-config.input';
import { UpdatePaymentConfigInput } from './dto/update-payment-config.input';

@Resolver(() => PaymentConfig)
export class PaymentConfigResolver {
  constructor(private readonly paymentConfigService: PaymentConfigService) {}

  @Mutation(() => PaymentConfig)
  createPaymentConfig(@Args('createPaymentConfigInput') createPaymentConfigInput: CreatePaymentConfigInput) {
    return this.paymentConfigService.create(createPaymentConfigInput);
  }

  @Query(() => [PaymentConfig], { name: 'paymentConfig' })
  findAll() {
    return this.paymentConfigService.findAll();
  }

  @Query(() => PaymentConfig, { name: 'paymentConfig' })
  findOne(@Args('id', { type: () => Int }) id: number) {
    return this.paymentConfigService.findOne(id);
  }

  @Mutation(() => PaymentConfig)
  updatePaymentConfig(@Args('updatePaymentConfigInput') updatePaymentConfigInput: UpdatePaymentConfigInput) {
    return this.paymentConfigService.update(updatePaymentConfigInput.id, updatePaymentConfigInput);
  }

  @Mutation(() => PaymentConfig)
  removePaymentConfig(@Args('id', { type: () => Int }) id: number) {
    return this.paymentConfigService.remove(id);
  }
}

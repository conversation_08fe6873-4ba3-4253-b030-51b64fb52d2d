import { ObjectType, Field, Int, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema } from '@nestjs/mongoose';
import { Location } from 'src/location/entities/location.entity';
import { UserRoles } from 'src/users/entities/user.entity';

export enum PaymentType {
  HOURLY = 'HOURLY',
  DAILY = 'DAILY',
  MONTHLY = 'MONTHLY',
}

registerEnumType(PaymentType, { name: 'PaymentType' });

@ObjectType()
@Schema()
class PaymentConfigOption {
  @Field(() => PaymentType)
  @Prop({ required: true, type: PaymentType })
  paymentType: PaymentType;

  @Field()
  @Prop()
  fullTimeAmount: number;

  @Field()
  @Prop()
  overTimeAmount: number;
}

@ObjectType()
@Schema()
export class PaymentConfig {
  @Field(() => Location)
  @Prop({ required: true, type: Location })
  location: Location;

  @Field(() => [UserRoles])
  @Prop({ required: true, type: [UserRoles] })
  roles: UserRoles[];

  @Field(() => PaymentConfigOption)
  @Prop({ required: true, type: PaymentConfigOption })
  weekDay: PaymentConfigOption;

  @Field(() => PaymentConfigOption)
  @Prop({ required: true, type: PaymentConfigOption })
  weekOff: PaymentConfigOption;

  @Field(() => PaymentConfigOption)
  @Prop({ required: true, type: PaymentConfigOption })
  holiday: PaymentConfigOption;
}

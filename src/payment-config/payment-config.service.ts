import { Injectable } from '@nestjs/common';
import { CreatePaymentConfigInput } from './dto/create-payment-config.input';
import { UpdatePaymentConfigInput } from './dto/update-payment-config.input';

@Injectable()
export class PaymentConfigService {
  create(createPaymentConfigInput: CreatePaymentConfigInput) {
    return 'This action adds a new paymentConfig';
  }

  findAll() {
    return `This action returns all paymentConfig`;
  }

  findOne(id: number) {
    return `This action returns a #${id} paymentConfig`;
  }

  update(id: number, updatePaymentConfigInput: UpdatePaymentConfigInput) {
    return `This action updates a #${id} paymentConfig`;
  }

  remove(id: number) {
    return `This action removes a #${id} paymentConfig`;
  }
}

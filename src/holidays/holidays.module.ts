import { Module } from '@nestjs/common';
import { HolidaysService } from './holidays.service';
import { HolidaysResolver } from './holidays.resolver';
import { MongooseModule } from '@nestjs/mongoose';
import { Holiday, HolidaySchema } from './entities/holiday.entity';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Holiday.name, schema: HolidaySchema }]), // Register the model here
  ],
  providers: [HolidaysResolver, HolidaysService],
})
export class HolidaysModule {}

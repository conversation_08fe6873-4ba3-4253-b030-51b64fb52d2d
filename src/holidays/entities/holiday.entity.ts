import { ObjectType, Field } from '@nestjs/graphql';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { MongooseSchema } from 'src/common/common.entity';

@ObjectType()
@Schema()
export class Holiday extends MongooseSchema {
  @Field(() => String)
  @Prop({ required: true })
  name: string;

  @Field(() => String)
  @Prop({ required: true })
  description: string;

  @Field(() => Date)
  @Prop({ required: true })
  date: Date;
}

export const HolidaySchema = SchemaFactory.createForClass(Holiday);

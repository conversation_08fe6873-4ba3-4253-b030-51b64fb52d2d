import { Resolver, Query, Mutation, Args } from '@nestjs/graphql';
import { HolidaysService } from './holidays.service';
import { Holiday } from './entities/holiday.entity';
import { CreateHolidayInput } from './dto/create-holiday.input';
import { UpdateHolidayInput } from './dto/update-holiday.input';

@Resolver(() => Holiday)
export class HolidaysResolver {
  constructor(private readonly holidaysService: HolidaysService) {}

  @Mutation(() => Holiday)
  createHoliday(
    @Args('createHolidayInput') createHolidayInput: CreateHolidayInput,
  ) {
    return this.holidaysService.create(createHolidayInput);
  }

  @Query(() => [Holiday], { name: 'holidays' })
  findAll() {
    return this.holidaysService.findAll();
  }

  @Query(() => Holiday, { name: 'holiday' })
  findOne(@Args('id', { type: () => String }) id: number) {
    return this.holidaysService.findOne(id);
  }

  @Mutation(() => Holiday)
  updateHoliday(
    @Args('updateHolidayInput') updateHolidayInput: UpdateHolidayInput,
    @Args('id', { type: () => String }) id: string,
  ) {
    return this.holidaysService.update(id, updateHolidayInput);
  }

  @Mutation(() => Holiday)
  removeHoliday(@Args('id', { type: () => String }) id: string) {
    return this.holidaysService.remove(id);
  }
}

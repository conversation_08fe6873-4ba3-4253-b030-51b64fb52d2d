import { Injectable } from '@nestjs/common';
import { CreateHolidayInput } from './dto/create-holiday.input';
import { UpdateHolidayInput } from './dto/update-holiday.input';
import { Holiday } from './entities/holiday.entity';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';

@Injectable()
export class HolidaysService {
  constructor(@InjectModel(Holiday.name) private holiday: Model<Holiday>) {}

  create(createHolidayInput: CreateHolidayInput) {
    return this.holiday.create(createHolidayInput);
  }

  findAll() {
    return this.holiday.find();
  }

  findOne(id: number) {
    return this.holiday.findById(id);
  }

  update(id: string, updateHolidayInput: UpdateHolidayInput) {
    return this.holiday.findByIdAndUpdate(id, updateHolidayInput, {
      new: true,
    });
  }

  remove(id: string): Promise<unknown> {
    return this.holiday.findByIdAndDelete(id);
  }
}

import { Injectable } from '@nestjs/common';
import { CreateAnouncementInput } from './dto/create-anouncement.input';
import { UpdateAnouncementInput } from './dto/update-anouncement.input';
import { InjectModel } from '@nestjs/mongoose';
import { Anouncement } from './entities/anouncement.entity';
import { Model } from 'mongoose';

@Injectable()
export class AnouncementsService {
  constructor(
    @InjectModel(Anouncement.name) private announcement: Model<Anouncement>,
  ) {}
  create(createAnouncementInput: CreateAnouncementInput, createdBy: string) {
    return this.announcement.create({
      ...createAnouncementInput,
      createdBy,
    });
  }

  findAll() {
    return this.announcement.find();
  }

  findOne(id: string) {
    return this.announcement.findById(id);
  }

  update(id: string, updateAnouncementInput: UpdateAnouncementInput) {
    return this.announcement
      .findByIdAndUpdate(id, updateAnouncementInput)
      .populate('users')
      .exec();
  }

  remove(id: string) {
    return this.announcement.findByIdAndDelete(id);
  }
}

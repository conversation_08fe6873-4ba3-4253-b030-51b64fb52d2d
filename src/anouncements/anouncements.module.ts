import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AnouncementsService } from './anouncements.service';
import { Anouncement, AnouncementSchema } from './entities/anouncement.entity';
import { AnouncementsResolver } from './anouncements.resolver';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Anouncement.name, schema: AnouncementSchema },
    ]),
  ],
  providers: [AnouncementsService, AnouncementsResolver],
  exports: [AnouncementsService],
})
export class AnouncementsModule {}

import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { CheckpointAttendance } from './entities/checkpoint-attendance.entity';
import { CreateCheckpointAttendanceInput } from './dto/create-checkpoint-attendance.input';
import { CheckpointsService } from 'src/checkpoints/checkpoints.service';

@Injectable()
export class CheckpointAttendanceService {
  constructor(
    @InjectModel(CheckpointAttendance.name)
    private checkpointAttendance: Model<CheckpointAttendance>,
    private checkpointsService: CheckpointsService,
  ) {}

  async create(
    guardId: string,
    createCheckpointAttendanceInput: CreateCheckpointAttendanceInput,
  ) {
    const { checkpointId, locationId, scannedLocation } =
      createCheckpointAttendanceInput;

    // Verify checkpoint exists
    const checkpoint = await this.checkpointsService.findOne(checkpointId);
    if (!checkpoint) {
      throw new NotFoundException('Checkpoint not found');
    }

    const checkpointData = {
      name: checkpoint.name,
      locationCoordinates: checkpoint.locationCoordinates,
    };

    const attendance = new this.checkpointAttendance({
      location: locationId,
      checkpoint: checkpoint._id,
      checkpointData: checkpointData,
      scannedAt: new Date(),
      scannedLocation,
      guard: guardId,
    });

    return attendance.save();
  }

  findAll() {
    return this.checkpointAttendance.find();
  }

  findOne(id: string) {
    return this.checkpointAttendance.findById(id);
  }

  getCheckpointAttendances(checkpointId: string) {
    return this.checkpointAttendance.find({ checkpoint: checkpointId });
  }

  getGuardAttendances(guardId: string) {
    return this.checkpointAttendance.find({ guard: guardId });
  }

  getLocationAttendances(locationId: string) {
    return this.checkpointAttendance.find({ location: locationId });
  }
}

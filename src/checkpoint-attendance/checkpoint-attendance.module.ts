import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  CheckpointAttendance,
  CheckpointAttendanceSchema,
} from './entities/checkpoint-attendance.entity';
import { CheckpointAttendanceService } from './checkpoint-attendance.service';
import { CheckpointAttendanceResolver } from './checkpoint-attendance.resolver';
import { CheckpointsModule } from 'src/checkpoints/checkpoints.module';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: CheckpointAttendance.name, schema: CheckpointAttendanceSchema },
    ]),
    CheckpointsModule,
  ],
  providers: [CheckpointAttendanceResolver, CheckpointAttendanceService],
  exports: [CheckpointAttendanceService],
})
export class CheckpointAttendanceModule {}

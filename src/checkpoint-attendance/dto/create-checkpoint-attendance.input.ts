import { Field, Float, InputType } from '@nestjs/graphql';
import { IsMongoId, IsNotEmpty, IsOptional } from 'class-validator';

@InputType()
export class CreateCheckpointAttendanceInput {
  @Field(() => String)
  @IsMongoId()
  @IsNotEmpty()
  checkpointId: string;

  @Field(() => String)
  @IsMongoId()
  @IsNotEmpty()
  locationId: string;

  @Field(() => [Float], { nullable: true })
  @IsOptional()
  scannedLocation?: [number, number];
}

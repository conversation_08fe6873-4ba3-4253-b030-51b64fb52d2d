import { Field, ObjectType, Float } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { User } from 'src/users/entities/user.entity';
import { Location } from 'src/location/entities/location.entity';
import { Checkpoint } from 'src/checkpoints/entities/checkpoint.entity';

@ObjectType()
class CheckpointData {
  @Field(() => String)
  @Prop({ required: true })
  name: string;

  @Field(() => [Float], { nullable: true })
  @Prop({ type: [Number] })
  locationCoordinates?: [number, number];
}

@ObjectType()
@Schema({ collection: 'checkpoint-attendance' })
export class CheckpointAttendance extends MongooseSchema {
  @Field(() => Checkpoint)
  @Prop({ type: Types.ObjectId, ref: 'Checkpoint', required: true })
  checkpoint: string;

  @Field(() => CheckpointData)
  @Prop({ type: CheckpointData, required: true })
  checkpointData: CheckpointData;

  @Field(() => Location)
  @Prop({ type: Types.ObjectId, ref: 'Location', required: true })
  location: string;

  @Field(() => User)
  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  guard: string;

  @Field(() => Date)
  @Prop({ default: Date.now })
  scannedAt: Date;

  @Field(() => [Float], { nullable: true })
  @Prop({ type: [Number], index: '2dsphere' })
  scannedLocation?: [number, number];
}

export const CheckpointAttendanceSchema =
  SchemaFactory.createForClass(CheckpointAttendance);

import {
  Resolver,
  Query,
  Mutation,
  Args,
  Context,
  ResolveField,
  Parent,
} from '@nestjs/graphql';
import { CheckpointAttendanceService } from './checkpoint-attendance.service';
import { CheckpointAttendance } from './entities/checkpoint-attendance.entity';
import { CreateCheckpointAttendanceInput } from './dto/create-checkpoint-attendance.input';
import { GqlContext } from 'src/app.module';
import { Location } from 'src/location/entities/location.entity';
import { User } from 'src/users/entities/user.entity';
import { CurrentUser } from 'src/auth/decorators/current-user.decorator';

@Resolver(() => CheckpointAttendance)
export class CheckpointAttendanceResolver {
  constructor(
    private readonly checkpointAttendanceService: CheckpointAttendanceService,
  ) {}

  @ResolveField(() => Location)
  location(
    @Parent() attendance: CheckpointAttendance,
    @Context() context: GqlContext,
  ) {
    return context.loaders.locationLoader.load(attendance.location);
  }

  @ResolveField(() => User)
  guard(
    @Parent() attendance: CheckpointAttendance,
    @Context() context: GqlContext,
  ) {
    return context.loaders.usersLoader.load(attendance.guard);
  }

  @Mutation(() => CheckpointAttendance)
  createCheckpointAttendance(
    @Args('createCheckpointAttendanceInput')
    createCheckpointAttendanceInput: CreateCheckpointAttendanceInput,
    @CurrentUser() user: User,
  ) {
    const guardId = user.id;
    return this.checkpointAttendanceService.create(
      guardId,
      createCheckpointAttendanceInput,
    );
  }

  @Query(() => [CheckpointAttendance])
  checkpointAttendances() {
    return this.checkpointAttendanceService.findAll();
  }

  @Query(() => CheckpointAttendance)
  checkpointAttendance(@Args('id', { type: () => String }) id: string) {
    return this.checkpointAttendanceService.findOne(id);
  }

  @Query(() => [CheckpointAttendance])
  getCheckpointAttendances(
    @Args('checkpointId', { type: () => String }) checkpointId: string,
  ) {
    return this.checkpointAttendanceService.getCheckpointAttendances(
      checkpointId,
    );
  }

  @Query(() => [CheckpointAttendance])
  getGuardAttendances(
    @Args('guardId', { type: () => String }) guardId: string,
  ) {
    return this.checkpointAttendanceService.getGuardAttendances(guardId);
  }

  @Query(() => [CheckpointAttendance])
  getLocationAttendances(
    @Args('locationId', { type: () => String }) locationId: string,
  ) {
    return this.checkpointAttendanceService.getLocationAttendances(locationId);
  }
}

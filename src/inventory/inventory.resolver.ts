import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import { CreateInventoryInput } from './dto/create-inventory.input';
import { UpdateInventoryInput } from './dto/update-inventory.input';
import { InventoryInput } from './dto/inventory.input';
import { Inventory } from './entities/inventory.entity';
import { InventoryService } from './inventory.service';

@Resolver(() => Inventory)
export class InventoryResolver {
  constructor(private readonly inventoryService: InventoryService) {}

  @Mutation(() => Inventory)
  createInventory(
    @Args('createInventoryInput') createInventoryInput: CreateInventoryInput,
  ) {
    return this.inventoryService.create(createInventoryInput);
  }

  @Query(() => [Inventory], { name: 'inventory' })
  findAll(
    @Args('inventoryInput', { nullable: true }) inventoryInput?: InventoryInput,
  ) {
    return this.inventoryService.findAll({
      ...(inventoryInput?.inventoryType && {
        type: inventoryInput?.inventoryType,
      }),
    });
  }

  @Query(() => Inventory, { name: 'inventoryById', nullable: true })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.inventoryService.findOne({ _id: id });
  }

  @Mutation(() => Inventory)
  updateInventory(
    @Args('id') id: string,
    @Args('updateInventoryInput') updateInventoryInput: UpdateInventoryInput,
  ) {
    return this.inventoryService.update(id, updateInventoryInput);
  }
}

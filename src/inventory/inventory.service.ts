import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import { Inventory } from './entities/inventory.entity';
import {
  InventoryRequest,
  RequestStatus,
} from './entities/inventory-request.entity';
import { CreateInventoryInput } from './dto/create-inventory.input';
import { CreateInventoryRequestInput } from './dto/create-inventory-request.input';
import { UpdateInventoryRequestInput } from './dto/update-inventory-request.input';
import { UpdateInventoryInput } from './dto/update-inventory.input';

@Injectable()
export class InventoryService {
  constructor(
    @InjectModel(Inventory.name)
    private inventoryModel: Model<Inventory>,
    @InjectModel(InventoryRequest.name)
    private inventoryRequestModel: Model<InventoryRequest>,
  ) {}

  create(createInventoryInput: CreateInventoryInput) {
    return this.inventoryModel.create(createInventoryInput);
  }

  update(id: string, updateInventoryInput: UpdateInventoryInput) {
    return this.inventoryModel.findByIdAndUpdate(id, updateInventoryInput, {
      new: true,
    });
  }

  findAll(query: FilterQuery<Inventory> = {}) {
    return this.inventoryModel.find(query);
  }

  findOne(query: FilterQuery<Inventory> = {}) {
    return this.inventoryModel.findOne(query);
  }

  async createRequest(createInput: CreateInventoryRequestInput) {
    // Fetch the inventory item first
    const inventory = await this.inventoryModel.findById(createInput.inventory);
    if (!inventory) {
      throw new NotFoundException(
        `Inventory with ID ${createInput.inventory} not found`,
      );
    }

    // Get all pending requests for this inventory
    const pendingRequests = await this.inventoryRequestModel.find({
      inventory: createInput.inventory,
      status: RequestStatus.PENDING,
    });

    // Calculate pending quantities per SKU
    const pendingQuantities = pendingRequests.reduce(
      (acc, request) => {
        request.items.forEach((item) => {
          acc[item.sku] = (acc[item.sku] || 0) + item.quantity;
        });
        return acc;
      },
      {} as Record<string, number>,
    );

    // Map the items with prices from inventory
    const items = await Promise.all(
      createInput.items.map((item) => {
        const inventoryItem = (
          (Array.isArray(inventory.items) ? inventory.items : []) as Array<{
            sku: string;
            costPrice: number;
            sellingPrice: number;
            quantity: number;
          }>
        )?.find((invItem) => invItem.sku === item.sku);

        if (!inventoryItem) {
          throw new NotFoundException(
            `Item with SKU ${item.sku} not found in inventory`,
          );
        }

        const pendingQuantity = pendingQuantities[item.sku] || 0;
        const availableQuantity = inventoryItem.quantity - pendingQuantity;

        if (item.quantity > availableQuantity) {
          throw new BadRequestException(
            `Requested quantity (${item.quantity}) exceeds available stock (${availableQuantity}) for item ${item.sku}`,
          );
        }

        return {
          ...item,
          costPrice: inventoryItem.costPrice,
          sellingPrice: inventoryItem.sellingPrice,
        };
      }),
    );

    // Create the request with updated items
    return this.inventoryRequestModel.create({
      ...createInput,
      items,
      inventoryType: inventory.type,
    });
  }

  async findAllRequests(
    filter: Partial<{
      inventoryType: string;
      status: RequestStatus;
      requestedBy: string;
    }> = {},
  ) {
    const query: Partial<{
      inventoryType: string;
      status: RequestStatus;
      requestedBy: string;
    }> = {};

    if (filter.inventoryType) {
      query.inventoryType = filter.inventoryType;
    }

    if (filter.status) {
      query.status = filter.status;
    }

    if (filter.requestedBy) {
      query.requestedBy = filter.requestedBy;
    }

    return this.inventoryRequestModel.find(query);
  }

  async findOneRequest(id: string) {
    const request = await this.inventoryRequestModel.findById(id);
    if (!request) {
      throw new NotFoundException(`Inventory request ${id} not found`);
    }
    return request;
  }

  async updateRequest(id: string, updateInput: UpdateInventoryRequestInput) {
    const request = await this.inventoryRequestModel.findById(id);
    if (!request) {
      throw new NotFoundException(`Inventory request ${id} not found`);
    }

    // If status is being updated to ACCEPTED
    if (updateInput.status === RequestStatus.ACCEPTED) {
      const inventory = await this.inventoryModel.findById(request.inventory);
      if (!inventory) {
        throw new NotFoundException(`Inventory ${request.inventory} not found`);
      }

      // Transform inventory items to plain objects
      const inventoryItems = (
        Array.isArray(inventory.items) ? inventory.items : []
      ).map(
        (item: {
          sku: string;
          item: string;
          quantity: number;
          costPrice: number;
          sellingPrice: number;
          _id: string;
          createdAt: Date;
          updatedAt: Date;
        }) => ({
          sku: item.sku,
          item: item.item,
          quantity: item.quantity,
          costPrice: item.costPrice,
          sellingPrice: item.sellingPrice,
          _id: item._id,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
        }),
      );

      // Update quantities
      const updatedItems = inventoryItems.map((invItem) => {
        const requestedItem = request.items.find(
          (item) => item.sku === invItem.sku,
        );

        if (requestedItem) {
          // Verify stock availability
          if (invItem.quantity < requestedItem.quantity) {
            throw new BadRequestException(
              `Insufficient stock for item ${invItem.sku}. Available: ${invItem.quantity}, Requested: ${requestedItem.quantity}`,
            );
          }

          return {
            ...invItem,
            quantity: invItem.quantity - requestedItem.quantity,
          };
        }
        return invItem;
      });

      // Update inventory with plain objects
      await this.inventoryModel.findByIdAndUpdate(
        request.inventory,
        { $set: { items: updatedItems } },
        { new: true },
      );

      // Update request status
      return this.inventoryRequestModel.findByIdAndUpdate(
        id,
        {
          status: RequestStatus.ACCEPTED,
          acceptedAt: new Date(),
        },
        { new: true },
      );
    }

    // If status is being updated to REJECTED, just update the status
    if (updateInput.status === RequestStatus.REJECTED) {
      return this.inventoryRequestModel.findByIdAndUpdate(
        id,
        {
          status: RequestStatus.REJECTED,
        },
        { new: true },
      );
    }

    // For any other updates
    return this.inventoryRequestModel.findByIdAndUpdate(id, updateInput, {
      new: true,
    });
  }
}

import { Field, InputType } from '@nestjs/graphql';
import { IsEnum, IsOptional } from 'class-validator';
import { InventoryType } from '../entities/inventory.entity';
import { RequestStatus } from '../entities/inventory-request.entity';

@InputType()
export class FindInventoryRequestsInput {
  @Field(() => InventoryType, { nullable: true })
  @IsEnum(InventoryType)
  @IsOptional()
  inventoryType?: InventoryType;

  @Field(() => RequestStatus, { nullable: true })
  @IsEnum(RequestStatus)
  @IsOptional()
  status?: RequestStatus;

  @Field(() => String, { nullable: true })
  @IsOptional()
  requestedBy?: string;
}

import { Field, InputType, Int } from '@nestjs/graphql';
import { Type } from 'class-transformer';
import {
  <PERSON>Array,
  IsN<PERSON>ber,
  IsString,
  ValidateNested,
  Min,
} from 'class-validator';

@InputType()
class SelectedAttributeInput {
  @Field(() => String)
  @IsString()
  attributeName: string;

  @Field(() => String)
  @IsString()
  value: string;
}

@InputType()
class RequestedItemInput {
  @Field(() => String)
  @IsString()
  item: string;

  @Field(() => String)
  @IsString()
  sku: string;

  @Field(() => Int)
  @IsNumber()
  @Min(1)
  quantity: number;

  @Field(() => [SelectedAttributeInput])
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SelectedAttributeInput)
  selectedAttributes: SelectedAttributeInput[];
}

@InputType()
export class CreateInventoryRequestInput {
  @Field(() => String)
  @IsString()
  inventory: string;

  @Field(() => [RequestedItemInput])
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RequestedItemInput)
  items: RequestedItemInput[];

  @Field(() => String)
  @IsString()
  requestedBy: string;
}

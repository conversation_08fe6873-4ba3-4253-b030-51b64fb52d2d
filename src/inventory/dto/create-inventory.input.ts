import { Field, InputType, Int } from '@nestjs/graphql';
import { InventoryType } from '../entities/inventory.entity';
import {
  IsString,
  IsArray,
  IsNumber,
  IsInt,
  IsOptional,
  IsEnum,
  MinLength,
  Min,
} from 'class-validator';

@InputType()
class AttributeTypeInput {
  @Field(() => String)
  @IsString()
  @MinLength(1)
  attibuteName: string;

  @Field(() => [String])
  @IsArray()
  @IsString({ each: true })
  attributeValues: string[];
}

@InputType()
class InventoryItemInputType {
  @Field(() => String)
  @IsString()
  @MinLength(1)
  item: string;

  @Field(() => Int)
  @IsInt()
  @Min(0)
  quantity: number;

  @Field(() => String)
  @IsString()
  @MinLength(1)
  sku: string;

  @Field(() => Number)
  @IsNumber()
  @Min(0)
  costPrice: number;

  @Field(() => Number)
  @IsNumber()
  @Min(0)
  sellingPrice: number;
}

@InputType()
export class CreateInventoryInput {
  @Field(() => String, { description: 'Inventory item name' })
  @IsString()
  @MinLength(1)
  item: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  @IsString()
  description?: string;

  @Field(() => [AttributeTypeInput], { nullable: true })
  @IsOptional()
  @IsArray()
  attributes?: AttributeTypeInput;

  @Field(() => [InventoryItemInputType], { nullable: true })
  @IsOptional()
  @IsArray()
  items?: InventoryItemInputType;

  @Field(() => InventoryType)
  @IsEnum(InventoryType)
  type: InventoryType;
}

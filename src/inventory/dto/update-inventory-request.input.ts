import { Field, InputType } from '@nestjs/graphql';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { RequestStatus } from '../entities/inventory-request.entity';

@InputType()
export class UpdateInventoryRequestInput {
  @Field(() => RequestStatus)
  @IsEnum(RequestStatus)
  status: RequestStatus;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  acceptedBy?: string;
}

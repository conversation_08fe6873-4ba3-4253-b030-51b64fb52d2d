import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { InventoryService } from './inventory.service';
import { InventoryResolver } from './inventory.resolver';
import { InventoryRequestResolver } from './inventory-request.resolver';
import { Inventory, InventorySchema } from './entities/inventory.entity';
import {
  InventoryRequest,
  InventoryRequestSchema,
} from './entities/inventory-request.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Inventory.name, schema: InventorySchema },
      { name: InventoryRequest.name, schema: InventoryRequestSchema },
    ]),
  ],
  providers: [InventoryService, InventoryResolver, InventoryRequestResolver],
})
export class InventoryModule {}

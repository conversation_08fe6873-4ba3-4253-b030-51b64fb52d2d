import {
  <PERSON><PERSON>,
  ResolveField,
  <PERSON>solver,
  Context,
  Query,
  Args,
  Mutation,
} from '@nestjs/graphql';
import { InventoryRequest } from './entities/inventory-request.entity';
import { Inventory } from './entities/inventory.entity';
import { User } from '../users/entities/user.entity';
import { GqlContext } from '../app.module';
import { InventoryService } from './inventory.service';
import { CreateInventoryRequestInput } from './dto/create-inventory-request.input';
import { UpdateInventoryRequestInput } from './dto/update-inventory-request.input';
import { FindInventoryRequestsInput } from './dto/find-inventory-requests.input';

@Resolver(() => InventoryRequest)
export class InventoryRequestResolver {
  constructor(private readonly inventoryService: InventoryService) {}

  @ResolveField(() => Inventory)
  inventory(
    @Parent() request: InventoryRequest,
    @Context() context: GqlContext,
  ) {
    return context.loaders.inventoryLoader.load(request.inventory);
  }

  @ResolveField(() => User)
  requestedBy(
    @Parent() request: InventoryRequest,
    @Context() context: GqlContext,
  ) {
    return context.loaders.usersLoader.load(request.requestedBy);
  }

  @ResolveField(() => User, { nullable: true })
  acceptedBy(
    @Parent() request: InventoryRequest,
    @Context() context: GqlContext,
  ) {
    return request.acceptedBy
      ? context.loaders.usersLoader.load(request.acceptedBy)
      : null;
  }

  @Query(() => [InventoryRequest])
  inventoryRequests(
    @Args('filter', { type: () => FindInventoryRequestsInput, nullable: true })
    filter?: FindInventoryRequestsInput,
  ) {
    return this.inventoryService.findAllRequests(filter || {});
  }

  @Query(() => InventoryRequest)
  inventoryRequest(@Args('id') id: string) {
    return this.inventoryService.findOneRequest(id);
  }

  @Mutation(() => InventoryRequest)
  createInventoryRequest(@Args('input') input: CreateInventoryRequestInput) {
    return this.inventoryService.createRequest(input);
  }

  @Mutation(() => InventoryRequest)
  updateInventoryRequest(
    @Args('id') id: string,
    @Args('input') input: UpdateInventoryRequestInput,
  ) {
    return this.inventoryService.updateRequest(id, input);
  }
}

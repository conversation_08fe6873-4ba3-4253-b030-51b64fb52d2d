import {
  ObjectType,
  Field,
  Int,
  Float,
  registerEnumType,
} from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import mongoose from 'mongoose';
import { Inventory, InventoryType } from './inventory.entity';
import { User } from 'src/users/entities/user.entity';

export enum RequestStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
}

registerEnumType(RequestStatus, {
  name: 'RequestStatus',
});

@ObjectType()
@Schema()
class SelectedAttribute {
  @Field(() => String)
  @Prop({ required: true })
  attributeName: string;

  @Field(() => String)
  @Prop({ required: true })
  value: string;
}

@ObjectType()
@Schema()
class RequestedItem {
  @Field(() => String)
  @Prop({ required: true })
  item: string;

  @Field(() => String)
  @Prop({ required: true })
  sku: string;

  @Field(() => Int)
  @Prop({ required: true })
  quantity: number;

  @Field(() => [SelectedAttribute])
  @Prop({ type: [SelectedAttribute], default: [] })
  selectedAttributes: SelectedAttribute[];

  @Field(() => Float)
  @Prop({ required: true })
  costPrice: number;

  @Field(() => Float)
  @Prop({ required: true })
  sellingPrice: number;
}

@ObjectType()
@Schema({ collection: 'inventory-requests' })
export class InventoryRequest extends MongooseSchema {
  @Field(() => Inventory)
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Inventory',
    required: true,
  })
  inventory: string;

  @Field(() => [RequestedItem])
  @Prop({ type: [RequestedItem], required: true })
  items: RequestedItem[];

  @Field(() => User)
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  })
  requestedBy: string;

  @Field(() => User, { nullable: true })
  @Prop({
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: false,
  })
  acceptedBy?: string;

  @Field(() => RequestStatus)
  @Prop({ required: true, enum: RequestStatus, default: RequestStatus.PENDING })
  status: RequestStatus;

  @Field(() => Date, { nullable: true })
  @Prop({ type: Date })
  acceptedAt?: Date;

  @Field(() => InventoryType)
  @Prop({ required: true, enum: InventoryType })
  inventoryType: InventoryType;
}

export const InventoryRequestSchema =
  SchemaFactory.createForClass(InventoryRequest);

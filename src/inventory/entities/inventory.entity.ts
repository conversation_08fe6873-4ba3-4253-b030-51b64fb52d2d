import { ObjectType, Field, Int, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { MongooseSchema } from 'src/common/common.entity';

export enum InventoryType {
  LOCATION = 'LOCATION',
  GUARD = 'GUARD',
}

registerEnumType(InventoryType, {
  name: 'InventoryType',
});

@ObjectType()
@Schema()
class AttributeType {
  @Field(() => String)
  @Prop({ required: true, type: String })
  attibuteName: string;

  @Field(() => [String])
  @Prop({ required: true, type: [String] })
  attributeValues: string[];
}

@ObjectType()
@Schema()
class InventoryItem {
  @Field(() => String)
  @Prop({ required: true, type: String })
  item: string;

  @Field(() => Int)
  @Prop({ required: true, type: Number })
  quantity: number;

  @Field(() => String)
  @Prop({ required: true, type: String })
  sku: string;

  @Field(() => Number)
  @Prop({ required: true, type: Number })
  costPrice: number;

  @Field(() => Number)
  @Prop({ required: true, type: Number })
  sellingPrice: number;
}

@ObjectType()
@Schema()
export class Inventory extends MongooseSchema {
  @Field(() => String, { description: 'Inventory item name' })
  @Prop({ required: true, type: String })
  item: string;

  @Field(() => String, { nullable: true })
  @Prop({ type: String })
  description?: string;

  @Field(() => [AttributeType], { nullable: true })
  @Prop({
    type: [AttributeType],
  })
  attributes?: AttributeType;

  @Field(() => [InventoryItem], { nullable: true })
  @Prop({
    type: [InventoryItem],
  })
  items?: InventoryItem;

  @Field(() => InventoryType)
  @Prop({ required: true, type: String, enum: InventoryType })
  type: InventoryType;
}

export const InventorySchema = SchemaFactory.createForClass(Inventory);

mutation CreateInventory($input: CreateInventoryInput!) {
  createInventory(createInventoryInput: $input) {
    id
  }
}

query Inventory($input: InventoryInput!) {
  inventory(inventoryInput: $input) {
    id
    item
    type
  }
}

query InventoryById($id: String!) {
    inventoryById(id: $id) {
        id
        createdAt
        item
        description
        attributes {
            attibuteName
            attributeValues
        }
        items {
            item
            quantity
            sku
            costPrice
            sellingPrice
        }
        type
    }
}

mutation UpdateInventory($id: String!, $updateInventoryInput: UpdateInventoryInput!) {
    updateInventory(id: $id, updateInventoryInput: $updateInventoryInput) {
        id
    }
}

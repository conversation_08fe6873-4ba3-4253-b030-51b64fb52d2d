query StorageFolders($parentId: String) {
  storageFolders(parentId: $parentId) {
    id
    name
    type
    size
    path
    metadata
  }
}
query StorageItems($parentId: String) {
  storageItems(parentId: $parentId) {
    id
    name
    type
    size
    path
    metadata
  }
}

mutation CreateFolder($createFolderInput: CreateFolderDto!) {
  createFolder(createFolderInput: $createFolderInput) {
    _id
  }
}

query StorageSystemFolders {
  systemFiles {
    id
    name
    path
    parent {
      id
    }
  }
}

query Locations {
  locations {
    id
    name
    description
    address
    emergencyContact
    geofence {
        center
        coords
    }
  }
}

mutation CreateLocation($createLocationInput: CreateLocationInput!) {
  createLocation(createLocationInput: $createLocationInput) {
    id
  }
}

query Location($locationId: String!) {
    location(id: $locationId) {
        _id
        id
        createdAt
        updatedAt
        name
        description
        address
        emergencyContact
        geofence {
            center
            coords
        }
    }
}

mutation UpdateLocation($id: ID!, $updateLocationInput: UpdateLocationInput!) {
    updateLocation(id: $id, updateLocationInput: $updateLocationInput) {
        id
    }
}


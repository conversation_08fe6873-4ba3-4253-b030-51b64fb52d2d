query Shift($input: ShiftsInput!) {
  shifts(shiftsInput: $input) {
    id
    recurringId
    startDateTime
    endDateTime
    isRecurring
    location {
      name
      id
    }
    users {
      fullname
      id
    }
  }
}

mutation CreateShift($input: CreateShiftInput!) {
  createShift(createShiftInput: $input)
}

mutation UpdateShift($id: String!, $input: UpdateShiftInput!) {
  updateShift(shiftId: $id, updateShiftInput: $input) {
    id
  }
}

mutation DeleteShift($id: String!) {
  removeShift(shiftId: $id)
}

mutation DeleteRecurringShift($id: String!, $recurringId: String!) {
  removeRecurringShifts(shiftId: $id, recurringId: $recurringId)
}

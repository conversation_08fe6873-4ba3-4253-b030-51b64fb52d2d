query Announcements {
  anouncements {
    id
    title
    description
    date
    userRoles
    document
    users {
      id
      fullname
    }
  }
}

mutation CreateAnnouncement($createAnnouncementInput: CreateAnouncementInput!) {
  createAnouncement(createAnouncementInput: $createAnnouncementInput) {
    title
    description
    date
    userRoles
    document
  }
}

mutation UpdateAnnoucement($id: String!, $updateAnouncementInput: UpdateAnouncementInput!) {
    updateAnouncement(id: $id, updateAnouncementInput: $updateAnouncementInput) {
        id
    }
}
query InventoryRequest($input : FindInventoryRequestsInput!) {
    inventoryRequests(filter: $input) {
        _id
        id
        createdAt
        updatedAt
        requestedBy {
            fullname
        }
        items {
            item
            sku
            quantity
            costPrice
            sellingPrice
            selectedAttributes {
                attributeName
                value
            }
        }
        acceptedAt
        status
        inventoryType
        inventory {
            item
        }
    }
}

mutation UpdateInventoryRequest($id: String!, $UpdateInventoryRequestInput: UpdateInventoryRequestInput!) {
    updateInventoryRequest(id: $id, input: $UpdateInventoryRequestInput) {
        id
    }
}
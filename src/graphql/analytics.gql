query GuardAttendanceTrends($filter: AnalyticsFilterInput!) {
  guardAttendanceTrends(filter: $filter) {
    date
    count
  }
}

query GuardActivityStats($filter: AnalyticsFilterInput!) {
  guardActivityStats(filter: $filter) {
    totalAttendance
    averageTimeSpent
    totalCheckpoints
    totalIncidents
  }
}

query LeaveStats($filter: AnalyticsFilterInput!) {
  leaveStats(filter: $filter) {
    pending
    approved
    rejected
  }
}

query InventoryStats {
  inventoryStats {
    type
    totalItems
    requestsPending
  }
}
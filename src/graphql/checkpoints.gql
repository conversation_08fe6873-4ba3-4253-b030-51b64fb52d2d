query Checkpoints($filter: FindCheckpointsInput!) {
  checkpoints(filter: $filter) {
    _id
    id
    createdAt
    updatedAt
    name
    location {
      id
      name
    }
    locationCoordinates
  }
}

mutation CreateCheckpoint($createCheckpointInput: CreateCheckpointInput!) {
  createCheckpoint(createCheckpointInput: $createCheckpointInput) {
    _id
    id
    name
  }
}

mutation RemoveCheckpoint($checkpointId: String!) {
  removeCheckpoint(id: $checkpointId) {
    id
  }
}

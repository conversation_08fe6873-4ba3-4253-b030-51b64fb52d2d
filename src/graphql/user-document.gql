mutation CreateUserDocument($input: CreateUserDocumentInput!) {
  createUserDocument(createUserDocumentInput: $input) {
    id
  }
}

mutation DeleteUserDocument($documentId: String!) {
  removeUserDocument(id: $documentId) {
    id
    documentName
  }
}

query UserDocuments($userDocumentFilterInput: UserDocumentFilterInput!) {
    userDocuments(filter: $userDocumentFilterInput) {
        id
        documentName
        url
    }
}

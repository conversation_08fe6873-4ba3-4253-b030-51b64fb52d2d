query Leaves {
    leaves {
        _id
        id
        createdAt
        updatedAt
        reason
        leaveType
        startDateTime
        endDateTime
        leaveStatus
        rejectedReason
        user {
            _id
            id
            fullname
        }
    }
}

mutation CreateLeave($createLeaveInput: CreateLeaveInput!) {
    createLeave(createLeaveInput: $createLeaveInput) {
        id
    }
}

mutation UpdateLeave($id: String!, $updateLeaveInput: UpdateLeaveInput!) {
    updateLeave(id: $id, updateLeaveInput: $updateLeaveInput) {
        id
    }
}

query GetLeaveById($leaveId: String!) {
  leave(id: $leaveId) {
    _id
    id
    leaveType
    reason
    startDateTime
    endDateTime
    leaveStatus
    user {
      id
      fullname
    }
    approvedBy {
      id
      fullname
    }
    rejectedReason
  }
}


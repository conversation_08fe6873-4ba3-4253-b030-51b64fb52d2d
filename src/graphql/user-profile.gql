mutation UpdateUserProfile($userId: String!, $updateUserProfileInput: UpdateUserProfileInput!) {
    updateUserProfile(id: $userId, updateUserProfileInput: $updateUserProfileInput) {
        id
    }
}

query UserProfile($userId: String!) {
    userProfile(id: $userId) {
        _id
        permitExpiresAt
        gender
        dob
        placeOfBirth
        currentAddress
        joinedAt
        maritalStatus
        bankAccNumber
        bankName
        emergencyContact{
            name
            relation
            contact {
                countryCode
                phone
            }
        }
        id
        ID
        ic
        passport
        passportExpiresAt
        permitNumber
    }
}
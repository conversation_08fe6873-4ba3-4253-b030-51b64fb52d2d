query Attendance($input: AttendanceInput!) {
  attendance(attendanceInput: $input) {
    id
    date
    timeSpentInMinutes
    startTime
    endTime
    overTime
    shift {
      id
    }
    location {
      id
      name
    }
    user {
      id
      fullname
    }
  }
}

query GetAllAttendance($filter: AttendanceFilterInput) {
  allAttendances(filter: $filter) {
    id
    date
    timeSpentInMinutes
    overTimeSpentInMinutes
    startTime
    endTime
    overTime
    shift {
      id
    }
    location {
      id
      name
    }
    user {
      id
      fullname
    }
  }
}

mutation ClockIn($input: ClockInInput!) {
  clockIn(clockInInput: $input) {
    id
  }
}

mutation ClockOut($input: ClockOutInput!) {
  clockOut(clockOutInput: $input) {
    id
  }
}

mutation CreateAttendance($input: CreateAttendanceInput!) {
  createAttendance(createAttendanceInput: $input) {
    id
  }
}

query GetAttendanceById($attendanceId: String!) {
  getAttendanceById(id: $attendanceId) {
    id
    date
    startTime
    endTime
    overTime
    location {
      id
    }
    user {
      id
      fullname
    }
    shift {
      id
      users {
        id
        fullname
      }
    }
  }
}

mutation UpdateAttendance($input: UpdateAttendanceInput!) {
  updateAttendance(updateAttendanceInput: $input) {
    id
  }
}

query Claims($filter: FindClaimsInput!) {
  claims(filter: $filter) {
    id
    createdAt
    status
    claimType
    claimData {
      ... on AllowanceClaim {
        amount
        purpose
        from
        to
        workingHours
        allowanceReceipts: receipts
      }
      ... on ExpenseClaim {
        amount
        purpose
        items
        date
        expenseReceipts: receipts
      }
      ... on SiteClaim {
        amount
        purpose
        site {
          id
          name
        }
        items
        siteReceipts: receipts
      }
      ... on TravelClaim {
        amount
        purpose
        from
        to
        client
        toll
        distance
        travelReceipts: receipts
      }
    }
    user {
      id
      fullname
    }
    processedBy {
      id
      fullname
    }
    rejectedReason
  }
}

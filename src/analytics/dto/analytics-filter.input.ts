import { Field, InputType } from '@nestjs/graphql';
import { IsOptional, IsDate } from 'class-validator';
import { UserRoles } from '../../users/entities/user.entity';

@InputType()
export class DateRangeInput {
  @Field(() => Date, { nullable: true })
  @IsOptional()
  @IsDate()
  startDate?: Date;

  @Field(() => Date, { nullable: true })
  @IsOptional()
  @IsDate()
  endDate?: Date;
}

@InputType()
export class AnalyticsFilterInput {
  @Field(() => DateRangeInput, { nullable: true })
  dateRange?: DateRangeInput;

  @Field(() => UserRoles, { nullable: true })
  userRole?: UserRoles;

  @Field(() => String, { nullable: true })
  locationId?: string;
}

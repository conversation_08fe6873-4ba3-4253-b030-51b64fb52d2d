import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { AnalyticsResolver } from './analytics.resolver';
import { AnalyticsService } from './analytics.service';
import {
  Attendance,
  AttendanceSchema,
} from '../attendance/entities/attendance.entity';
import { Leave, LeaveSchema } from '../leaves/entities/leave.entity';
import {
  CheckpointAttendance,
  CheckpointAttendanceSchema,
} from '../checkpoint-attendance/entities/checkpoint-attendance.entity';
import {
  Incident,
  IncidentSchema,
} from '../incident-monitoring/entities/incident-monitoring.entity';
import {
  Inventory,
  InventorySchema,
} from '../inventory/entities/inventory.entity';
import {
  InventoryRequest,
  InventoryRequestSchema,
} from 'src/inventory/entities/inventory-request.entity';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Attendance.name, schema: AttendanceSchema },
      { name: Leave.name, schema: LeaveSchema },
      { name: CheckpointAttendance.name, schema: CheckpointAttendanceSchema },
      { name: Incident.name, schema: IncidentSchema },
      { name: Inventory.name, schema: InventorySchema },
      { name: InventoryRequest.name, schema: InventoryRequestSchema },
    ]),
  ],
  providers: [AnalyticsResolver, AnalyticsService],
})
export class AnalyticsModule {}

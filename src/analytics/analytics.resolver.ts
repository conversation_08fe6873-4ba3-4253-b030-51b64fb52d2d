import { Args, Query, Resolver } from '@nestjs/graphql';
import { AnalyticsService } from './analytics.service';
import { AnalyticsFilterInput } from './dto/analytics-filter.input';
import {
  GuardActivityStats,
  InventoryStats,
  LeaveStats,
  TimeSeriesData,
} from './entities/analytics.entity';

@Resolver()
export class AnalyticsResolver {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Query(() => [TimeSeriesData])
  async guardAttendanceTrends(
    @Args('filter', { nullable: true }) filter?: AnalyticsFilterInput,
  ) {
    return this.analyticsService.getGuardAttendanceTrends(filter);
  }

  @Query(() => GuardActivityStats)
  async guardActivityStats(
    @Args('filter', { nullable: true }) filter?: AnalyticsFilterInput,
  ) {
    return this.analyticsService.getGuardActivityStats(filter);
  }

  @Query(() => LeaveStats)
  async leaveStats(
    @Args('filter', { nullable: true }) filter?: AnalyticsFilterInput,
  ) {
    return this.analyticsService.getLeaveStats(filter);
  }

  @Query(() => [InventoryStats])
  async inventoryStats() {
    return this.analyticsService.getInventoryStats();
  }
}

import { Field, ObjectType, Int, Float } from '@nestjs/graphql';

@ObjectType()
export class TimeSeriesData {
  @Field()
  date: string;

  @Field(() => Int)
  count: number;
}

@ObjectType()
export class GuardActivityStats {
  @Field(() => Int)
  totalAttendance: number;

  @Field(() => Float)
  averageTimeSpent: number;

  @Field(() => Int)
  totalCheckpoints: number;

  @Field(() => Int)
  totalIncidents: number;
}

@ObjectType()
export class LeaveStats {
  @Field(() => Int)
  pending: number;

  @Field(() => Int)
  approved: number;

  @Field(() => Int)
  rejected: number;
}

@ObjectType()
export class InventoryStats {
  @Field(() => String)
  type: string;

  @Field(() => Int)
  totalItems: number;

  @Field(() => Int)
  requestsPending: number;
}

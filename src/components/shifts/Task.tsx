import { errorHand<PERSON> } from '@/lib/utils';
import React, { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '../ui/table';
import UpdateTaskForm, {
  useUpdateTaskFormMethods
} from '@/forms/shifts/UpdateTaskForm';
import { CalendarSync, Pencil, Trash2 } from 'lucide-react';
import { Button } from '../ui/button';

const UpdateTask = ({ taskId }: { taskId: Id<'tasks'> }) => {
  const utf = useUpdateTaskFormMethods();
  const task = useQuery(api.tasks.getTask, { taskId });

  useEffect(() => {
    if (task) {
      utf.methods.setValue('taskId', taskId);
      utf.methods.setValue('title', task?.title);
      utf.methods.setValue(
        'userIds',
        // @ts-expect-error
        task?.users.map(u => ({ value: u._id, label: u.fullName }))
      );
      utf.methods.setValue('description', task?.description);
    }
  }, [task, utf.methods]);

  return <UpdateTaskForm {...utf} />;
};

export const Tasks = ({ shiftId }: { shiftId: Id<'shifts'> }) => {
  const tasks = useQuery(api.tasks.getTasks, {
    shiftId: shiftId as Id<'shifts'>
  });
  const [open, setOPen] = useState(false);
  const [taskId, setTaskId] = useState<Id<'tasks'>>();

  const deleteTask = useMutation(api.tasks.deleteTask);
  const handleDeleteTask = async (taskId: Id<'tasks'>) => {
    try {
      await deleteTask({ taskId });
      toast.success('Task deleted successfully');
    } catch (ex) {
      errorHandler(ex);
    }
  };

  return (
    <div>
      <Dialog open={open} onOpenChange={setOPen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Task</DialogTitle>
          </DialogHeader>
          {taskId && <UpdateTask taskId={taskId} />}
        </DialogContent>
      </Dialog>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>S.No</TableHead>
            <TableHead>Task</TableHead>
            <TableHead>Users</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Recurring</TableHead>
            <TableHead>Update</TableHead>
            <TableHead>Delete</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tasks?.map((task, idx) => {
            return (
              <TableRow key={task._id}>
                <TableCell>{idx + 1}</TableCell>
                <TableCell>{task.title}</TableCell>
                <TableCell>
                  {task.users.map(p => p?.fullName).join(', ')}
                </TableCell>
                <TableCell>{task.taskStatus}</TableCell>
                <TableCell className="">
                  {task.isRecurring ? (
                    <CalendarSync className="text-green-800" />
                  ) : null}
                </TableCell>
                <TableCell>
                  <Button
                    size={'icon'}
                    onClick={() => {
                      setOPen(true);
                      setTaskId(task._id);
                    }}
                  >
                    <Pencil />
                  </Button>
                </TableCell>
                <TableCell>
                  <Button
                    variant={'destructive'}
                    size={'icon'}
                    onClick={() => handleDeleteTask(task._id)}
                  >
                    <Trash2 />
                  </Button>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};

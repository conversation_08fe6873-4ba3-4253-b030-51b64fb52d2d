import CreateTaskForm, {
  useCreateTaskFormMethods
} from '@/forms/shifts/CreateTaskForm';
import UpdateShiftForm, {
  useUpdateShiftFormMethods
} from '@/forms/shifts/UpdateShiftForm';
import {
  Shift as ShiftType,
  useDeleteRecurringShiftMutation,
  useDeleteShiftMutation,
  useHolidaysQuery
} from '@/generated/graphql';
import { CalendarArrowDown, ListCheck } from 'lucide-react';
import { useMemo, useState } from 'react';
import CreateAttendance from '../elements/CreateAttendance';
import { Button } from '../ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle
} from '../ui/dialog';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup
} from '../ui/resizable';
import VerticalTabs, { VerticalTabProps } from '../vertical-tabs';
import Shift from './Shift';
import { Tasks } from './Task';
import { toast } from 'sonner';

let selectedShiftId: string;

export default function ShiftsList({
  shiftId,
  shifts,
  locationId,
  setShiftId,
  date
}: {
  shiftId?: string;
  shifts: ShiftType[];
  locationId: string;
  setShiftId: (shiftId: string) => void;
  date?: Date;
}) {
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [updateModalOpen, setUpdateModalOpen] = useState(false);
  const [taskModalOpen, setTaskModalOpen] = useState(false);

  const selectedShift = useMemo(() => {
    if (!shiftId) return undefined;
    return shifts?.find(shift => shift.id === shiftId);
  }, [shiftId, shifts]);

  const guardsBySelectedShift = useMemo(() => {
    if (!selectedShift) return [];
    return selectedShift.users;
  }, [selectedShift]);

  const onDeleteClick = (shiftId: string) => {
    setDeleteModalOpen(true);
    selectedShiftId = shiftId;
  };

  const onTaskClick = (shiftId: string) => {
    setTaskModalOpen(true);
    selectedShiftId = shiftId;
    createTaskFormMethods.methods.setValue('shiftId', shiftId);
    createTaskFormMethods.methods.setValue('locationId', locationId);
    createTaskFormMethods.methods.setValue(
      'isRecurring',
      !!selectedShift?.recurringId
    );

    if (!guardsBySelectedShift) return;
    createTaskFormMethods.methods.setValue(
      'userIds',
      guardsBySelectedShift.map(g => ({ value: g._id, label: g.fullname }))
    );
  };

  const fm = useUpdateShiftFormMethods();
  const { methods } = fm;

  const onUpdateClick = (shiftId: string) => {
    setUpdateModalOpen(true);
    selectedShiftId = shiftId;
    const shift = shifts?.find(s => s.id === shiftId);
    if (!shift) return;
    const { users } = shift;
    methods.setValue('shiftId', shiftId);

    methods.setValue(
      'userIds',
      users?.map(u => ({ value: u?.id, label: u?.fullname })) ?? []
    );
    methods.setValue('updateRecurring', shift.isRecurring ?? false)
  };

  const onShiftClick = (shiftId: string) => {
    setShiftId(shiftId);
  };

  const createTaskFormMethods = useCreateTaskFormMethods(s =>
    setTaskModalOpen(s)
  );

  const tabs: VerticalTabProps['tabs'] = useMemo(() => {
    return [
      {
        icon: (
          <CalendarArrowDown size={16} strokeWidth={2} aria-hidden="true" />
        ),
        key: 'attendance',
        content: shiftId && (
          <CreateAttendance
            locationId={locationId}
            shiftId={shiftId}
            guards={guardsBySelectedShift}
            date={
              selectedShift?.startDateTime
                ? new Date(selectedShift?.startDateTime)
                : new Date()
            }
          />
        ),
        tooltipContent: 'Attendance'
      },
      {
        icon: <ListCheck size={16} strokeWidth={2} aria-hidden="true" />,
        key: 'tasks',
        content: shiftId && <Tasks shiftId={shiftId as string} />,
        tooltipContent: 'Tasks'
      }
    ];
  }, [shiftId, selectedShift]);

  const { mutateAsync: deleteShift } = useDeleteShiftMutation();
  const { mutateAsync: deleteRecurringShift } =
    useDeleteRecurringShiftMutation();

  return (
    <div className="mr-4 flex-1 border-r">
      {/* Delete */}
      <Dialog open={deleteModalOpen} onOpenChange={setDeleteModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Shift</DialogTitle>
            <DialogDescription>
              By selecting recurring option will make changes to all the
              recurring shifts
            </DialogDescription>
          </DialogHeader>

          {/* Delete */}
          <div className="flex items-center gap-4">
            <Button
              variant="destructive"
              onClick={() => {
                if (!selectedShiftId) return;

                toast.promise(deleteShift({ id: selectedShiftId }), {
                  loading: 'Deleting shift...',
                  success: 'Shift deleted successfully',
                  error: 'Failed to delete shift'
                });
              }}
            >
              Delete
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                if (!selectedShiftId) return;
                if (!selectedShift?.recurringId)
                  return toast.error('No recurring shifts found');

                toast.promise(
                  deleteRecurringShift({
                    id: selectedShiftId,
                    recurringId: selectedShift.recurringId
                  }),
                  {
                    loading: 'Deleting recurring shifts...',
                    success: 'Recurring shifts deleted successfully',
                    error: 'Failed to delete recurring shifts'
                  }
                );
              }}
            >
              Delete Recurring Shifts
            </Button>
            <Button
              variant="secondary"
              onClick={() => setDeleteModalOpen(false)}
            >
              Cancel
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Update Modal */}
      <Dialog open={updateModalOpen} onOpenChange={setUpdateModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Shift</DialogTitle>
            <DialogDescription>
              By selecting recurring option will make changes to all the
              recurring shifts
            </DialogDescription>
          </DialogHeader>

          {/* Update FOrm */}
          <UpdateShiftForm {...fm} />
        </DialogContent>
      </Dialog>

      {/* Create Task Modal */}
      <Dialog open={taskModalOpen} onOpenChange={setTaskModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create Task</DialogTitle>
            <DialogDescription>
              By selecting recurring option will make changes to all the
              recurring shifts
            </DialogDescription>
          </DialogHeader>

          {/* Update Form */}
          <CreateTaskForm
            // @ts-expect-error
            guards={guardsBySelectedShift}
            {...createTaskFormMethods}
          />
        </DialogContent>
      </Dialog>

      <ResizablePanelGroup direction="horizontal">
        <ResizablePanel minSize={30} maxSize={50} defaultSize={40}>
          <div className="h-full divide-y overflow-y-auto">
            {shifts?.map(s => {
              return (
                // @ts-expect-error
                <Shift
                  key={s.id}
                  onDeleteClick={onDeleteClick}
                  onUpdateClick={onUpdateClick}
                  onTaskClick={onTaskClick}
                  onShiftClick={onShiftClick}
                  isActive={s.id === shiftId}
                  {...s}
                />
              );
            })}
          </div>
        </ResizablePanel>
        <ResizableHandle />
        <ResizablePanel>
          {selectedShift ? (
            <VerticalTabs tabs={tabs} />
          ) : (
            <div className="p-4">Select a shift for details</div>
          )}
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  );
}

import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import {
  CalendarIcon,
  EllipsisVertical,
  ListCheck,
  LucideEdit,
  MapPinIcon,
  Trash2
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from '../ui/dropdown-menu';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { Location, Shift as ShiftType, User } from '@/generated/graphql';

export default function Shift(
  shift: ShiftType & {
    users: User[];
    location: Location;
    onDeleteClick: (shiftId: string) => void;
    onUpdateClick: (shiftId: string) => void;
    onTaskClick: (shiftId: string) => void;
    onShiftClick: (shiftId: string) => void;
    isActive?: boolean;
  }
) {
  const {
    startDateTime,
    users,
    location,
    onDeleteClick,
    id,
    onUpdateClick,
    onTaskClick,
    onShiftClick,
    isActive
  } = shift;

  return (
    <div
      className={cn(
        'flex items-center gap-4 py-4 cursor-pointer hover:bg-secondary px-4 duration-500',
        isActive && 'bg-secondary'
      )}
      onClick={() => onShiftClick(id)}
    >
      <Avatar className="size-12">
        {/* <AvatarImage src="https://github.com/shadcn.png" /> */}
        <AvatarFallback className="size-10">CN</AvatarFallback>
      </Avatar>

      <div className="mr-4 flex-1">
        <h3 className="pr-10 font-semibold text-gray-900">
          {users.map(u => u.fullname).join(', ')}
        </h3>
        <div className="flex flex-col text-gray-500 xl:flex-row">
          <div className="flex items-start gap-x-3">
            <div className="mt-0.5">
              <span className="sr-only">Date</span>
              <CalendarIcon
                className="size-5 text-gray-400"
                aria-hidden="true"
              />
            </div>
            <time dateTime={String(new Date(startDateTime))}>
              {format(startDateTime, 'MMM do, yyyy  h:mm a')}
            </time>
          </div>
          <div className="mt-2 flex items-start gap-x-3 xl:ml-3.5 xl:mt-0 xl:border-l xl:border-gray-400/50 xl:pl-3.5">
            <div className="mt-0.5">
              <MapPinIcon className="size-5 text-gray-400" aria-hidden="true" />
            </div>
            <div>{location.name}</div>
          </div>
        </div>
      </div>

      <DropdownMenu>
        <DropdownMenuTrigger>
          <EllipsisVertical className="size-4 text-neutral-600" />
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => onUpdateClick(id)}>
            <LucideEdit />
            Update
          </DropdownMenuItem>

          <DropdownMenuItem onClick={() => onDeleteClick(id)}>
            <Trash2 /> Delete
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onTaskClick(id)}>
            <ListCheck /> Tasks
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}

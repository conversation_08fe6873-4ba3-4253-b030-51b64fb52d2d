import CreateShiftForm from '@/forms/shifts/CreateShiftForm';
import { Link } from '@tanstack/react-router';
import { addYears, eachYearOfInterval, format, subYears } from 'date-fns';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '../ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../ui/select';

type MonthPickerProps = {
  date?: Date;
  onChange: (date: Date) => void;
  startYear?: Date;
  endYear?: Date;
};

const MonthPicker = ({
  date = new Date(),
  onChange,
  startYear = subYears(new Date(), 5),
  endYear = addYears(new Date(), 10)
}: MonthPickerProps) => {
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December'
  ];

  const selectedMonth = format(date, 'MMMM');
  const selectedYear = format(date, 'yyyy');

  const handleMonthSelect = (month: string) => {
    const newDate = new Date(date);
    newDate.setMonth(months.indexOf(month));
    onChange(newDate);
  };

  const onPrevMonthClick = () => {
    const newDate = new Date(date);
    newDate.setMonth(date.getMonth() - 1);
    onChange(newDate);
  };

  const onNextMonthClick = () => {
    const newDate = new Date(date);
    newDate.setMonth(date.getMonth() + 1);
    onChange(newDate);
  };

  const onYearSelect = (yearStr: string) => {
    const newDate = new Date(date);
    newDate.setFullYear(Number(yearStr));
    onChange(newDate);
  };

  const years = eachYearOfInterval({ start: startYear, end: endYear });

  return (
    <div className="flex items-center gap-4">
      <div className="inline-flex -space-x-px rounded-lg shadow-sm shadow-black/5 rtl:space-x-reverse">
        <Button
          className="rounded-none shadow-none first:rounded-s-lg last:rounded-e-lg focus-visible:z-10"
          variant="outline"
          size="sm"
          onClick={onPrevMonthClick}
        >
          <ChevronLeft size={16} strokeWidth={2} aria-hidden="true" />
        </Button>
        <Select value={selectedMonth} onValueChange={handleMonthSelect}>
          <SelectTrigger
            className="rounded-none bg-white"
            value={selectedMonth}
          >
            <SelectValue placeholder="Select Month" />
          </SelectTrigger>
          <SelectContent>
            {months.map(m => {
              return (
                <SelectItem key={m} value={m}>
                  {m}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>

        <Button
          onClick={onNextMonthClick}
          className="rounded-none shadow-none first:rounded-s-lg last:rounded-e-lg focus-visible:z-10"
          variant="outline"
          size="sm"
        >
          <ChevronRight size={16} strokeWidth={2} aria-hidden="true" />
        </Button>
      </div>

      <Select value={selectedYear} onValueChange={onYearSelect}>
        <SelectTrigger className="bg-white" value={selectedYear}>
          <SelectValue placeholder="Select Year" />
        </SelectTrigger>
        <SelectContent>
          {years.map(y => {
            const yearStr = format(y, 'yyyy');
            return (
              <SelectItem key={yearStr} value={yearStr}>
                {yearStr}
              </SelectItem>
            );
          })}
        </SelectContent>
      </Select>
    </div>
  );
};

export default function ShiftHeader({
  date,
  locationId,
  setDate
}: {
  locationId: string;
  date: Date;
  setDate: (date: Date) => void;
}) {
  return (
    <div className="flex items-center justify-between">
      <Button asChild effect="underline" variant="link">
        <Link to="/locations/$locationId" params={{ locationId }}>
          {/* {name} */}
          TODO: Show location name
        </Link>
      </Button>

      <div className="flex items-center gap-4">
        <MonthPicker onChange={setDate} date={date} />

        <CreateShiftForm locationId={locationId} date={date} />
      </div>
    </div>
  );
}

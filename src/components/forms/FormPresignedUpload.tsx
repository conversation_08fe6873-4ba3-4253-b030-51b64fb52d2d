import { Form } from '@/@types';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { useCreateSignedUploadUrlMutation } from '@/generated/graphql';
import { useId, useState, useEffect } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import Dropzone from 'shadcn-dropzone';
import { X } from 'lucide-react';

export type FileState = {
  file: File;
  progress: number;
  uploadedUrl?: string;
};

interface FormPresignedUploadProps {
  name: string;
  label?: string;
  accept?: string;
  multiple?: boolean;
}

// Add a helper function to extract filename from URL
const getFileNameFromUrl = (url: string) => {
  try {
    const urlPath = new URL(url).pathname;
    return decodeURIComponent(urlPath.split('/').pop() || '');
  } catch {
    return 'Unknown file';
  }
};

export function FormPresignedUpload({
  name,
  label,
  accept,
  multiple = false
}: Form<FormPresignedUploadProps>) {
  const id = useId();
  const [files, setFiles] = useState<FileState[]>([]);
  const [error, setError] = useState<string | null>(null);
  const { mutateAsync: getSignedUrl } = useCreateSignedUploadUrlMutation();
  
  // Add form context
  const methods = useFormContext();
  const formValue = methods.watch(name);

  // Effect to handle existing file URL
  useEffect(() => {
    if (formValue && typeof formValue === 'string' && files.length === 0) {
      // Create a mock file state for existing URL
      setFiles([{
        file: new File([], getFileNameFromUrl(formValue)), // Empty file with name from URL
        progress: 100,
        uploadedUrl: formValue
      }]);
    } else if (!formValue) {
      setFiles([]);
    }
  }, [formValue]);

  const uploadFile = async (file: File) => {
    try {
      // Get signed URL with fields
      const data = await getSignedUrl({
        input: {
          key: `uploads/${Date.now()}-${file.name}`,
          contentType: file.type, // This should match exactly what S3 expects
          expiresIn: 300,
        }
      });

      if (!data?.createSignedUploadUrl?.url || !data?.createSignedUploadUrl?.fields) {
        throw new Error('Failed to get upload URL');
      }

      // Create FormData with fields in specific order
      const formData = new FormData();
      const fields = data.createSignedUploadUrl.fields;
      
      // Add fields in correct order for S3
      formData.append('key', fields.key);
      formData.append('bucket', fields.bucket);
      formData.append('acl', fields.acl);
      formData.append('X-Amz-Algorithm', fields.algorithm);
      formData.append('X-Amz-Credential', fields.credential);
      formData.append('X-Amz-Date', fields.date);
      formData.append('Policy', fields.Policy);
      formData.append('X-Amz-Signature', fields.signature);
      
      // Important: Use the exact Content-Type from the policy
      formData.append('Content-Type', file.type); // Use file's content type
      
      // Add the file last
      formData.append('file', file);

      // Upload using XMLHttpRequest
      const xhr = new XMLHttpRequest();
      xhr.open('POST', data.createSignedUploadUrl.url);

      // Don't set any content type header, let the browser set it with the form data
      return new Promise((resolve, reject) => {
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            setFiles(current =>
              current.map(f =>
                f.file === file ? { ...f, progress } : f
              )
            );
          }
        });

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            // Construct the final S3 URL
            const fileUrl = `${data.createSignedUploadUrl.url}/${fields.key}`;
            resolve(fileUrl);
          } else {
            reject(new Error('Upload failed'));
          }
        };

        xhr.onerror = () => reject(new Error('Upload failed'));
        xhr.send(formData);
      });
    } catch (error) {
      console.error('Upload failed:', error);
      throw error;
    }
  };

  // Modify the render part to show existing files
  return (
    <Controller
      name={name}
      render={({ field: { onChange }, fieldState: { error: fieldError } }) => (
        <div className="space-y-2">
          {label && <Label htmlFor={id}>{label}</Label>}
          {(!multiple && files.length > 0) ? (
            <div className="grid gap-2">
              {files.map((fileState, index) => (
                <div key={index} className="flex items-center gap-2 p-2 border rounded">
                  <div className="flex-1">
                    <div className="text-sm">
                      {fileState.uploadedUrl ? (
                        <a 
                          href={fileState.uploadedUrl} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          {fileState.file.name}
                        </a>
                      ) : (
                        fileState.file.name
                      )}
                    </div>
                    {fileState.progress < 100 && !fileState.uploadedUrl && (
                      <Progress value={fileState.progress} className="h-1 mt-1" />
                    )}
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      setFiles([]);
                      setError(null);
                      onChange(null);
                      methods.setValue(name, null);
                    }}
                    className="text-muted-foreground hover:text-destructive transition-colors"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          ) : (
            <Dropzone
              onDrop={async (droppedFiles) => {
                if (!multiple && files.length > 0) {
                  setError("You can only upload one file at a time");
                  return;
                }
                
                setError(null);
                setFiles([]); // Clear any previous files before setting new ones
                
                const newFiles = multiple
                  ? droppedFiles
                  : [droppedFiles[0]];

                const fileStates = newFiles.map(file => ({
                  file,
                  progress: 0
                }));

                setFiles(fileStates);

                try {
                  const uploadResults = await Promise.all(
                    fileStates.map(async ({ file }) => {
                      const uploadedUrl = await uploadFile(file);
                      return { file, uploadedUrl };
                    })
                  );
                  
                  setFiles(current =>
                    current.map(fileState => {
                      const result = uploadResults.find(r => r.file === fileState.file);
                      return result ? { ...fileState, uploadedUrl: result.uploadedUrl as string } : fileState;
                    })
                  );
                  
                  const newValue = multiple ? uploadResults.map(r => r.uploadedUrl) : uploadResults[0].uploadedUrl;
                  onChange(newValue);
                  methods.setValue(name, newValue); // Update using form context
                } catch (error) {
                  console.error('Upload failed:', error);
                  setError("Upload failed. Please try again.");
                  setFiles([]);
                  methods.setValue(name, null); // Clear value on error
                }
              }}
              maxFiles={multiple ? undefined : 1}
              accept={
                accept
                  ? accept.split(',').reduce<Record<string, string[]>>((acc, type) => {
                      const trimmed = type.trim();
                      if (trimmed) acc[trimmed] = [];
                      return acc;
                    }, {})
                  : undefined
              }
              containerClassName="border rounded-2xl border-dashed"
            />
          )}

          {(error || fieldError) && (
            <p className="text-sm text-destructive">
              {error || fieldError?.message}
            </p>
          )}
        </div>
      )}
    />
  );
}
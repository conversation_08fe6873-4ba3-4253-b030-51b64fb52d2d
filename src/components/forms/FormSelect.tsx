import React, { useId } from 'react';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { useFormContext } from 'react-hook-form';
import _ from 'lodash';
import { Form } from '@/@types';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { cn } from '@/lib/utils';

export default function FormSelect({
  name,
  label,
  placeholder,
  children,
  disabled,
  ...divProps
}: Form<React.ComponentPropsWithoutRef<'div'>> & { disabled?: boolean }) {
  const methods = useFormContext();
  const value = methods.watch(name);

  const error = _.get(methods.formState.errors, name);

  if (!name) throw new Error('FormSelect must have a name');

  return (
    <div className="space-y-2" {...divProps}>
      <Label>{label}</Label>
      <Select
        value={value}
        onValueChange={v => methods.setValue(name, v)}
        disabled={disabled}
        {...methods.register(name)}
      >
        <SelectTrigger className={cn(disabled && "opacity-50 cursor-not-allowed")}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent
          className={cn(
            error?.message &&
              'border-destructive/80 focus-visible:border-destructive/80 focus-visible:ring-destructive/20 border-red-400 ring-red-400',
            disabled && "opacity-50"
          )}
        >
          {children}
        </SelectContent>
      </Select>

      {error?.message && (
        <p
          className="mt-2 text-xs text-destructive"
          role="alert"
          aria-live="polite"
        >
          {typeof error.message === 'string'
            ? error.message
            : 'something went wrong'}
        </p>
      )}
    </div>
  );
}

import React from 'react';
import { Label } from '../ui/label';
import <PERSON>Field from './FormField';
import { cn } from '@/lib/utils';
import { Form } from '@/@types';

export default function FormPhoneInput({
  className,
  name,
  label,
  placeholder,
  type,
  ...rest
}: Form<React.ComponentPropsWithoutRef<'div'>>) {
  return (
    <div className={cn('space-y-2', className)} {...rest}>
      <Label>{label}</Label>
      <div className="flex items-end rounded-lg shadow-sm shadow-black/5">
        <span className="relative z-10 inline-flex h-9 items-center rounded-s-lg border border-input bg-background px-3 text-sm text-muted-foreground">
          +60
        </span>
        <FormField
          name={name}
          inputClassName="-ms-px rounded-s-none shadow-none"
          placeholder={placeholder}
          type={type || 'tel'}
        />
      </div>
    </div>
  );
}

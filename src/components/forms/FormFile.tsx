import { Form } from '@/@types';
import { Label } from '@/components/ui/label';
import React, { useId, useState } from 'react';
import { Controller, UseFormReturn } from 'react-hook-form';
import Dropzone from 'shadcn-dropzone';
import {
  FileUploadProgress,
  uploadFiles as _uploadFiles
} from '../drive/upload-file';

export type FileState = { file: File; progress: number };

export const useFormFile = () => {
  const [files, setFiles] = useState<FileState[]>([]);

  const uploadFiles = async ({
    parentFolderId
  }: {
    parentFolderId?: string;
  }) => {
    const uploads = await _uploadFiles(files, setFiles, parentFolderId);
    return (uploads ?? []).map(u => u.fileId);
  };

  return { files, setFiles, uploadFiles };
};

function FormFile({
  name,
  label,
  files,
  setFiles,
  multiple = false
}: Form<{
  name: string;
  label: string;
  accept?: string;
  files: FileState[];
  setFiles: React.Dispatch<React.SetStateAction<FileState[]>>;
  multiple?: boolean;
}>) {
  const id = useId();

  return (
    <Controller
      name={name}
      render={({ field: { onChange } }) => {
        return (
          <div className="space-y-2">
            {label && <Label htmlFor={id}>{label}</Label>}
            <Dropzone
              onDrop={files => {
                if (multiple) {
                  setFiles(files.map(file => ({ file: file, progress: 0 })));
                } else {
                  setFiles([{ file: files[0], progress: 0 }]);
                }
              }}
              containerClassName="border rounded-2xl border-dashed"
            />

            <div className="grid grid-cols-6">
              {files.map((item, fileIdx) => (
                <FileUploadProgress
                  file={item}
                  onDelete={() => {
                    setFiles(files.filter((_, idx) => idx !== fileIdx));
                  }}
                />
              ))}
            </div>
          </div>
        );
      }}
    />
  );
}

export default FormFile;

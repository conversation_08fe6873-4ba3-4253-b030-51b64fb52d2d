import { Form } from '@/@types';
import { useController } from 'react-hook-form';
import ChipInput from '../ui/chip-input';
import { Label } from '../ui/label';
import FormError from './FormError';
import { z } from 'zod';

export const tagsSchema = z.array(
  z.object({
    id: z.string(),
    text: z.string()
  })
);

export default function FormChipInput({
  name,
  label,
  placeholder
}: Form<{
  name: string;
  label: string;
  placeholder: string;
}>) {
  const { field } = useController({ name });
  return (
    <div>
      {label && <Label htmlFor={name}>{label}</Label>}
      {/* @ts-expect-error */}
      <ChipInput
        tags={field.value}
        setTags={field.onChange}
        placeholder={placeholder}
      />
      <FormError name={name} />
    </div>
  );
}

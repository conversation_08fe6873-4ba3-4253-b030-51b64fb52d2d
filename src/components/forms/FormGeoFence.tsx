import { Form } from '@/@types';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Libraries, GoogleMap, Polygon, Marker, Autocomplete } from '@react-google-maps/api';
import { useJs<PERSON>pi<PERSON>oader } from '@react-google-maps/api';
import { useState, useCallback, useRef, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import FormError from './FormError';
import { VITE_GOOGLE_MAPS_API_KEY } from '@/env';
import { Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';

const libraries: Libraries = ["places", "geometry", "drawing"];

const calculatePolygonCenter = (path: google.maps.LatLng[]): google.maps.LatLngLiteral => {
  if (path.length === 0) return { lat: 0, lng: 0 };
  
  const bounds = new google.maps.LatLngBounds();
  path.forEach(point => bounds.extend(point));
  
  return {
    lat: bounds.getCenter().lat(),
    lng: bounds.getCenter().lng()
  };
};

export default function FormGeoFence({
  name,
  label,
  placeholder
}: Form<{
  name: string;
  label: string;
  placeholder?: string;
}>) {
  const { setValue, watch } = useFormContext();
  const value = watch(name);
  console.log('value', value)
  const [searchBox, setSearchBox] = useState<google.maps.places.Autocomplete | null>(null);
  const [path, setPath] = useState<google.maps.LatLngLiteral[]>([]);
  const polygonRef = useRef<google.maps.Polygon | null>(null) as React.MutableRefObject<google.maps.Polygon | null>;
  const listenersRef = useRef<google.maps.MapsEventListener[]>([]);

  const { isLoaded } = useJsApiLoader({
    id: 'google-map-script',
    googleMapsApiKey: VITE_GOOGLE_MAPS_API_KEY,
    libraries
  });

  const [center, setCenter] = useState<google.maps.LatLngLiteral>({
    lat: 3.140853,
    lng: 101.693207
  });

  const onMapLoad = useCallback((map: google.maps.Map) => {
    // Map loaded callback
  }, []);

  const onPlaceChanged = () => {
    if (searchBox) {
      const place = searchBox.getPlace();
      const location = place.geometry?.location;
      if (location) {
        setCenter({ lat: location.lat(), lng: location.lng() });
      }
    }
  };

  const onLoad = useCallback((polygon: google.maps.Polygon) => {
    polygonRef.current = polygon;
    const path = polygon.getPath();
    listenersRef.current.push(
      path.addListener('set_at', updatePathState),
      path.addListener('insert_at', updatePathState),
      path.addListener('remove_at', updatePathState)
    );
  }, []);

  const onUnmount = useCallback(() => {
    listenersRef.current.forEach(lis => lis.remove());
    polygonRef.current = null;
  }, []);

  const updatePathState = () => {
    const polygon = polygonRef.current;
    if (polygon) {
      const pathArray = polygon.getPath().getArray();
      const coords = pathArray.map(latLng => [latLng.lat(), latLng.lng()]);
      const centerPoint = calculatePolygonCenter(pathArray);
      setValue(name, {
        center: [centerPoint.lat, centerPoint.lng],
        coords
      });
      setPath(pathArray.map(latLng => ({ lat: latLng.lat(), lng: latLng.lng() })));
    }
  };

  const handleMarkerPosChange = (e: google.maps.MapMouseEvent, idx: number) => {
    if (e.latLng) {
      const newPath = [...path];
      newPath[idx] = { lat: e.latLng.lat(), lng: e.latLng.lng() };
      setPath(newPath);
      updatePathState();
    }
  };

  const onMapClick = (e: google.maps.MapMouseEvent) => {
    if (e.latLng) {
      const newPath = [...path, { lat: e.latLng.lat(), lng: e.latLng.lng() }];
      setPath(newPath);
      
      // Update form value when adding new points
      const coords = newPath.map(point => [point.lat, point.lng]);
      const centerPoint = calculatePolygonCenter(
        newPath.map(point => new google.maps.LatLng(point.lat, point.lng))
      );
      
      setValue(name, {
        center: [centerPoint.lat, centerPoint.lng],
        coords
      });
    }
  };

  const clearPolygon = () => {
    setPath([]);
    setValue(name, {
      center: [center.lat, center.lng],
      coords: []
    });
  };

  useEffect(() => {
    if (value) {
      // Convert the stored coords array to LatLngLiteral array
      const existingPath = value.coords?.map(([lat, lng]: [number, number]) => ({
        lat,
        lng
      }));
      setPath(existingPath);
      
      // Set the center if it exists
      if (value.center) {
        setCenter({
          lat: value.center[0],
          lng: value.center[1]
        });
      }
    }
  }, []); // Only run on mount

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <Label>{label}</Label>
        {path?.length > 0 && (
          <Button
            onClick={clearPolygon}
            variant="destructive"
            size="sm"
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Clear Polygon
          </Button>
        )}
      </div>
      {isLoaded && (
        <div className="relative">
          <Autocomplete
            onLoad={setSearchBox}
            onPlaceChanged={onPlaceChanged}
          >
            <div className="relative mb-2">
              <Search className="absolute left-3 top-1/2 -translate-y-1/3 text-gray-500 h-4 w-4" />
              <Input 
                placeholder={placeholder || "Search location"} 
                className="pl-10"
              />
            </div>
          </Autocomplete>
        </div>
      )}
      <div className="h-[70vh] relative">
        {isLoaded ? (
          <GoogleMap
            onLoad={onMapLoad}
            mapContainerClassName="h-[70vh] w-full flex flex-col"
            center={center}
            zoom={12}
            onClick={onMapClick}
          >
            {path?.map((marker, idx) => (
              <Marker
                key={idx}
                position={marker}
                draggable
                onDragEnd={(e) => handleMarkerPosChange(e, idx)}
              />
            ))}

            <Polygon
              path={path}
              onLoad={onLoad}
              onUnmount={onUnmount}
              editable
              draggable
              options={{
                fillColor: "#2196F3",
                fillOpacity: 0.35,
                strokeColor: "#2196F3",
                strokeWeight: 2
              }}
            />
          </GoogleMap>
        ) : (
          <div className="h-full flex items-center justify-center">Loading...</div>
        )}
      </div>
      <FormError name={name} />
    </div>
  );
}
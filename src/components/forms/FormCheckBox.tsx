import { Controller } from 'react-hook-form';
import { Checkbox } from '../ui/checkbox';
import { Label } from '../ui/label';
import { useId } from 'react';

export default function FormCheckBox({
  name,
  label
}: {
  name: string;
  label: string;
}) {
  const id = useId();
  return (
    <Controller
      name={name}
      render={({ field: { onChange, value } }) => {
        return (
          <div className="flex items-center gap-2">
            <Checkbox id={id} checked={value} onCheckedChange={onChange} />
            <Label htmlFor={id}>{label}</Label>
          </div>
        );
      }}
    />
  );
}

import { Form } from '@/@types';
import { cn } from '@/lib/utils';
import _ from 'lodash';
import React, { useId } from 'react';
import { useFormContext } from 'react-hook-form';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';

export default function FormTextArea({
  name,
  label,
  placeholder,
  type,
  inputClassName,
  disabled,
  ...divProps
}: Form<React.ComponentPropsWithoutRef<'div'>> & { 
  inputClassName?: string;
  disabled?: boolean;
}) {
  const id = useId();
  const methods = useFormContext();

  const error = _.get(methods.formState.errors, name);

  if (!name) throw new Error('Form<PERSON>ield must have a name');

  return (
    <div className="space-y-2" {...divProps}>
      {label && <Label htmlFor={id}>{label}</Label>}
      <Textarea
        id={id}
        className={cn(
          error?.message &&
            'border-destructive/80 focus-visible:border-destructive/80 focus-visible:ring-destructive/20 border-red-400 ring-red-400',
          disabled && "opacity-50 cursor-not-allowed",
          inputClassName
        )}
        placeholder={placeholder}
        disabled={disabled}
        {...methods.register(name)}
      />
      {error?.message && (
        <p
          className="mt-2 text-xs text-destructive"
          role="alert"
          aria-live="polite"
        >
          {typeof error.message === 'string'
            ? error.message
            : 'something went wrong'}
        </p>
      )}
    </div>
  );
}

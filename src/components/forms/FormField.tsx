import React, { useId } from 'react';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { useFormContext } from 'react-hook-form';
import _ from 'lodash';
import { Form } from '@/@types';
import { cn } from '@/lib/utils';

export default function FormField({
  name,
  label,
  placeholder,
  type,
  inputClassName,
  ...divProps
}: Form<React.ComponentPropsWithoutRef<'div'>> & { inputClassName?: string }) {
  const id = useId();
  const methods = useFormContext();

  const error = _.get(methods.formState.errors, name);

  if (!name) throw new Error('Form<PERSON>ield must have a name');

  return (
    <div className="space-y-2" {...divProps}>
      {label && <Label htmlFor={id}>{label}</Label>}
      <Input
        id={id}
        className={cn(
          error?.message &&
            'border-destructive/80 focus-visible:border-destructive/80 focus-visible:ring-destructive/20 border-red-400 ring-red-400',
          inputClassName
        )}
        placeholder={placeholder}
        type={type}
        {...methods.register(name)}
      />
      {error?.message && (
        <p
          className="mt-2 text-xs text-destructive"
          role="alert"
          aria-live="polite"
        >
          {typeof error.message === 'string'
            ? error.message
            : 'something went wrong'}
        </p>
      )}
    </div>
  );
}

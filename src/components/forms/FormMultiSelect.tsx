import { Label } from '@/components/ui/label';
import MultipleSelector, { Option } from '@/components/ui/multiselect';
import { useFormContext } from 'react-hook-form';
import { z } from 'zod';

export const multiSelectSchema = z.array(
  z.object({
    value: z.string(),
    label: z.string()
  })
);

export default function FormMultiSelect({
  name,
  label,
  placeholder,
  options
}: {
  name: string;
  label?: string;
  placeholder?: string;
  options: Option[];
}) {
  const methods = useFormContext();
  const v = methods.watch(name);

  // TODO: Error handling
  return (
    <div className="space-y-2">
      {label && <Label>{label}</Label>}
      <MultipleSelector
        commandProps={{
          label: placeholder ?? 'Select items'
        }}
        value={v}
        onChange={data => methods.setValue(name, data)}
        defaultOptions={options}
        placeholder={placeholder}
        hideClearAllButton
        hidePlaceholderWhenSelected
        emptyIndicator={<p className="text-center text-sm">No results found</p>}
      />
    </div>
  );
}

import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Link } from '@tanstack/react-router';
import { BoxIcon, PackageCheck, PackagePlus } from 'lucide-react';
import React from 'react';

export enum InventoryLink {
  CREATE_INVENTORY = 'create-inventory',
  LOCATION_INVENTORY = 'location-inventory',
  GUARD_INVENTORY = 'guard-inventory'
}

type InventoryTabsProps = {
  activeTab: InventoryLink;
};

export default function InventoryTabs({
  children,
  activeTab
}: React.PropsWithChildren<InventoryTabsProps>) {
  return (
    <Tabs value={activeTab}>
      <TabsList className="mb-3 bg-secondary">
        <TabsTrigger value={InventoryLink.CREATE_INVENTORY} asChild>
          <Link to="/inventory/create-inventory">
            <PackagePlus
              className="-ms-0.5 me-1.5 opacity-60"
              size={16}
              aria-hidden="true"
            />
            Inventory
          </Link>
        </TabsTrigger>
        <TabsTrigger value={InventoryLink.LOCATION_INVENTORY} asChild>
          <Link to="/inventory/location-inventory">
            <PackageCheck
              className="-ms-0.5 me-1.5 opacity-60"
              size={16}
              aria-hidden="true"
            />
            Location Inventory
          </Link>
        </TabsTrigger>
        <TabsTrigger
          value={InventoryLink.GUARD_INVENTORY}
          className="group"
          asChild
        >
          <Link to="/inventory/guard-inventory">
            <BoxIcon
              className="-ms-0.5 me-1.5 opacity-60"
              size={16}
              aria-hidden="true"
            />
            Guard Inventory
          </Link>
        </TabsTrigger>
      </TabsList>
      {children}
      {/* <TabsContent value={InventoryLink.CREATE_INVENTORY}>
        
      </TabsContent>
      <TabsContent value={InventoryLink.LOCATION_INVENTORY}>
        {children}
      </TabsContent>
      <TabsContent value={InventoryLink.GUARD_INVENTORY}>
        {children}
      </TabsContent> */}
    </Tabs>
  );
}

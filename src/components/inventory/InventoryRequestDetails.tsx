import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON>er,
  CardTitle
} from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { formatDate } from "date-fns";
import { RequestStatus } from "@/generated/graphql";
import { toast } from "sonner";
import { useState } from "react";

interface InventoryRequestDetailsProps {
  request: {
    _id: string;
    id: string;
    createdAt: Date;
    updatedAt: Date;
    requestedBy: {
      fullname: string;
    };
    items: Array<{
      item: string;
      sku: string;
      quantity: number;
      costPrice: number;
      sellingPrice: number;
      selectedAttributes: Array<{
        attributeName: string;
        value: string;
      }>;
    }>;
    acceptedAt?: Date | null;
    status: RequestStatus;
    inventoryType: string;
    inventory: {
      item: string;
    };
  };
  onStatusChange: (status: RequestStatus) => Promise<void>;
  currentUserId: string;
}

export function InventoryRequestDetails({ request, onStatusChange }: InventoryRequestDetailsProps) {
  const [selectedStatus, setSelectedStatus] = useState<RequestStatus>(request.status);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await onStatusChange(selectedStatus);
      toast.success("Status updated successfully");
    } catch (error) {
      toast.error("Failed to update status");
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Request Details</CardTitle>
          <CardDescription>
            Created on {formatDate(request.createdAt, 'PPP')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Requested By
              </p>
              <p className="text-sm">{request.requestedBy.fullname}</p>
            </div>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Status</p>
                <Select
                  defaultValue={request.status}
                  onValueChange={(value) => setSelectedStatus(value as RequestStatus)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value={RequestStatus.Pending}>
                      Pending
                    </SelectItem>
                    <SelectItem value={RequestStatus.Accepted}>
                      Accepted
                    </SelectItem>
                    <SelectItem value={RequestStatus.Rejected}>
                      Rejected
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button 
                type="submit" 
                className="w-full"
                disabled={selectedStatus === request.status}
              >
                Update Status
              </Button>
            </form>
          </div>

          {/* Items List */}
          <div>
            <h3 className="text-sm font-medium text-muted-foreground mb-3">
              Requested Items
            </h3>
            <div className="space-y-4">
              {request.items.map((item, index) => (
                <Card key={index}>
                  <CardContent className="pt-6">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Item Name
                        </p>
                        <p className="text-sm">{item.item}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          SKU
                        </p>
                        <p className="text-sm">{item.sku}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Quantity
                        </p>
                        <p className="text-sm">{item.quantity}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Cost Price
                        </p>
                        <p className="text-sm">${item.costPrice}</p>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          Selling Price
                        </p>
                        <p className="text-sm">${item.sellingPrice}</p>
                      </div>
                    </div>
                    {item.selectedAttributes.length > 0 && (
                      <div className="mt-4">
                        <p className="text-sm font-medium text-muted-foreground mb-2">
                          Attributes
                        </p>
                        <div className="grid grid-cols-2 gap-2">
                          {item.selectedAttributes.map((attr, idx) => (
                            <div key={idx} className="text-sm">
                              <span className="font-medium">
                                {attr.attributeName}:
                              </span>{" "}
                              {attr.value}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
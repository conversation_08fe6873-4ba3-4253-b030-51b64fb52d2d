import { Chart<PERSON>ontainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Axi<PERSON>, YAxis } from 'recharts';

interface InventoryStats {
  type: string;
  totalItems: number;
  requestsPending: number;
}

interface InventoryChartProps {
  data: InventoryStats[];
}

export function InventoryStatusChart({ data }: InventoryChartProps) {
  const config = {
    items: {
      label: 'Total Items',
      theme: { light: '#6366f1', dark: '#818cf8' }
    },
    pending: {
      label: 'Pending Requests',
      theme: { light: '#f97316', dark: '#fb923c' }
    }
  };

  return (
    <ChartContainer config={config}>
      <BarChart data={data}>
        <XAxis dataKey="type" />
        <YAxis />
        <ChartTooltip content={<ChartTooltipContent />} />
        <Bar 
          dataKey="totalItems"
          name="items"
          fill={config.items.theme.light}
          stackId="a"
        />
        <Bar 
          dataKey="requestsPending"
          name="pending"
          fill={config.pending.theme.light}
          stackId="a"
        />
      </BarChart>
    </ChartContainer>
  );
}

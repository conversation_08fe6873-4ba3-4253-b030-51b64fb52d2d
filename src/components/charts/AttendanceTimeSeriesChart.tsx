import { Chart<PERSON>ontainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { <PERSON>, <PERSON>C<PERSON>, <PERSON><PERSON><PERSON><PERSON>, YAxis } from 'recharts';

interface TimeSeriesData {
  date: string;
  count: number;
}

interface AttendanceChartProps {
  data: TimeSeriesData[];
}

export function AttendanceTimeSeriesChart({ data }: AttendanceChartProps) {
  const config = {
    attendance: {
      label: 'Daily Attendance',
      theme: {
        light: '#2563eb',
        dark: '#3b82f6'
      }
    }
  };

  return (
    <ChartContainer config={config}>
      <LineChart data={data}>
        <XAxis dataKey="date" />
        <YAxis />
        <ChartTooltip content={<ChartTooltipContent />} />
        <Line 
          type="monotone"
          dataKey="count"
          name="attendance"
          stroke={config.attendance.theme.light}
          strokeWidth={2}
        />
      </LineChart>
    </ChartContainer>
  );
}

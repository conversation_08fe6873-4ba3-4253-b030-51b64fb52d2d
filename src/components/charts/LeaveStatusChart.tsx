import { Chart<PERSON>ontainer, ChartTooltip, ChartTooltipContent, ChartLegend } from '@/components/ui/chart';
import { <PERSON>, <PERSON><PERSON><PERSON>, Cell } from 'recharts';

interface LeaveStats {
  pending: number;
  approved: number;
  rejected: number;
}

interface LeaveStatusProps {
  data: LeaveStats;
}

export function LeaveStatusChart({ data }: LeaveStatusProps) {
  const chartData = [
    { name: 'Pending', value: data.pending },
    { name: 'Approved', value: data.approved },
    { name: 'Rejected', value: data.rejected },
  ];

  const config = {
    pending: {
      label: 'Pending',
      theme: { light: '#fbbf24', dark: '#fcd34d' }
    },
    approved: {
      label: 'Approved',
      theme: { light: '#22c55e', dark: '#4ade80' }
    },
    rejected: {
      label: 'Rejected',
      theme: { light: '#ef4444', dark: '#f87171' }
    }
  };

  return (
    <ChartContainer config={config}>
      <PieChart>
        <Pie
          data={chartData}
          dataKey="value"
          nameKey="name"
          cx="50%"
          cy="50%"
          innerRadius={60}
          outerRadius={80}
        >
          {chartData.map((entry, index) => (
            <Cell 
              key={`cell-${index}`}
              fill={config[entry.name.toLowerCase() as keyof typeof config].theme.light}
            />
          ))}
        </Pie>
        <ChartTooltip content={<ChartTooltipContent />} />
        <ChartLegend />
      </PieChart>
    </ChartContainer>
  );
}

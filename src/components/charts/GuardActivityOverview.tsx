import { Chart<PERSON>ontainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { <PERSON>, <PERSON><PERSON><PERSON>, XAxis, YAxis } from 'recharts';

interface GuardActivityStats {
  totalAttendance: number;
  totalCheckpoints: number;
  totalIncidents: number;
}

interface GuardActivityProps {
  data: GuardActivityStats;
}

export function GuardActivityOverview({ data }: GuardActivityProps) {
  // Transform the data for the chart
  const chartData = [
    { name: 'Attendance', value: data.totalAttendance },
    { name: 'Checkpoints', value: data.totalCheckpoints },
    { name: 'Incidents', value: data.totalIncidents },
  ];

  const config = {
    stats: {
      label: 'Guard Activity',
      theme: {
        light: '#0ea5e9',
        dark: '#38bdf8'
      }
    }
  };

  return (
    <ChartContainer config={config}>
      <BarChart data={chartData}>
        <XAxis dataKey="name" />
        <YAxis />
        <ChartTooltip content={<ChartTooltipContent />} />
        <Bar 
          dataKey="value"
          name="stats"
          fill="currentColor"
          className="fill-primary"
        />
      </BarChart>
    </ChartContainer>
  );
}

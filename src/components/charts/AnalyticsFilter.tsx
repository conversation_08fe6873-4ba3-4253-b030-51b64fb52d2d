import * as React from 'react';
import { Calendar as CalendarIcon } from 'lucide-react';
import { DateRange } from 'react-day-picker';
import { Button } from '../ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { Calendar } from '../ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '../ui/popover';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { UserRoles } from '@/generated/graphql';

interface AnalyticsFilterProps {
  onFilterChange: (filters: {
    dateRange?: DateRange;
    userRole?: UserRoles;
    locationId?: string;
  }) => void;
  locations?: { id: string; name: string }[];
}

export function AnalyticsFilter({ onFilterChange, locations }: AnalyticsFilterProps) {
  const [date, setDate] = React.useState<DateRange | undefined>();
  const [role, setRole] = React.useState<UserRoles>();
  const [location, setLocation] = React.useState<string>();

  React.useEffect(() => {
    onFilterChange({
      dateRange: date,
      userRole: role,
      locationId: location
    });
  }, [date, role, location, onFilterChange]);

  return (
    <div className="flex flex-wrap gap-2 mb-4">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              'justify-start text-left font-normal',
              !date && 'text-muted-foreground'
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, 'LLL dd, y')} -{' '}
                  {format(date.to, 'LLL dd, y')}
                </>
              ) : (
                format(date.from, 'LLL dd, y')
              )
            ) : (
              <span>Date range</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            selected={date}
            onSelect={setDate}
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>

      <Select
        value={role}
        onValueChange={(value) => setRole(value as UserRoles)}
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select role" />
        </SelectTrigger>
        <SelectContent>
          {Object.values(UserRoles).map((role) => (
            <SelectItem key={role} value={role}>
              {role}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {locations && (
        <Select
          value={location}
          onValueChange={setLocation}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select location" />
          </SelectTrigger>
          <SelectContent>
            {locations.map((loc) => (
              <SelectItem key={loc.id} value={loc.id}>
                {loc.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}
    </div>
  );
}
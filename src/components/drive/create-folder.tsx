import { useCreateFolderMutation } from '@/generated/graphql';
import { DialogTrigger } from '@radix-ui/react-dialog';
import { FolderPlus } from 'lucide-react';
import { useEffect } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { z } from 'zod';
import <PERSON>Field from '../forms/FormField';
import { Button } from '../ui/button';
import { Dialog, DialogContent } from '../ui/dialog';
import { toast } from 'sonner';

const schema = z.object({
  folder: z.string().nonempty(),
  path: z.string().nonempty(),
  parentFolderId: z.string().optional()
});

type Form = z.infer<typeof schema>;

export function CreateFolderDialog({
  parentFolderId,
  path = '/'
}: {
  parentFolderId?: string;
  path?: string;
}) {
  const methods = useForm<Form>({
    defaultValues: { folder: '', path, parentFolderId }
  });

  useEffect(() => {
    methods.setValue('path', path);
    methods.setValue('parentFolderId', parentFolderId);
  }, [path, parentFolderId]);

  const { mutateAsync: creatFolderAsync } = useCreateFolderMutation();

  const onSubmit = methods.handleSubmit(data => {
    toast.promise(creatFolderAsync({ createFolderInput: data }), {
      success(data) {
        methods.setValue('folder', '');
        return 'Folder created successfully';
      }
    });
  });
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>Create Folder</Button>
      </DialogTrigger>

      <DialogContent>
        <FormProvider {...methods}>
          <form className="space-y-4" onSubmit={onSubmit}>
            <FormField
              name="folder"
              label="Folder Name"
              placeholder='e.g. "New Folder"'
            />

            <Button
              className="w-full"
              effect="expandIcon"
              iconPlacement="right"
              icon={FolderPlus}
            >
              Create Folder
            </Button>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
}

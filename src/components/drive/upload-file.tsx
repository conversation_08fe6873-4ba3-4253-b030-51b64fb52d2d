import { FileIcon, Trash } from 'lucide-react';
import { Progress } from '../ui/progress';
import { errorHandler, formatFileSize } from '@/lib/utils';
import { client, uploadFile, type UploadProgress } from '@/client';
import { Upload, UploadIcon } from 'lucide-react';
import Dropzone from 'shadcn-dropzone';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { queryClient } from '@/main';
import {
  useStorageFoldersQuery,
  useStorageItemsQuery
} from '@/generated/graphql';

export type FileState = { file: File; progress: number };

export const uploadFiles = async (
  files: FileState[],
  setFiles: React.Dispatch<React.SetStateAction<FileState[]>>,
  parentFolderId?: string
) => {
  try {
    return await Promise.all(
      files.map(async (f, idx) =>
        uploadFile(
          {
            file: f.file,
            fileName: f.file.name,
            parentFolderId
          },
          (progress: UploadProgress) => {
            setFiles(files =>
              files.map((item, itemIdx) => {
                if (itemIdx === idx)
                  return { file: item.file, progress: progress.percentage };
                return item;
              })
            );
          }
        )
      )
    );
  } catch (ex) {
    errorHandler(ex);
  }
  setFiles([]);

  // invalidate queries
  queryClient.invalidateQueries({
    queryKey: useStorageFoldersQuery.getKey()
  });

  queryClient.invalidateQueries({
    queryKey: useStorageItemsQuery.getKey()
  });
};

export function FileUploadProgress({
  file: { file, progress },
  onDelete
}: {
  file: FileState;
  onDelete: () => void;
}) {
  // upload file with upload progress

  return (
    <div className="relative">
      <div className="">
        <FileIcon className="size-6 text-primary mr-2" />
        <div className="text-xs truncate">{file.name}</div>
        <Progress value={progress} className="h-1 border" />
      </div>

      <button
        onClick={onDelete}
        className="rounded-full border size-7 flex justify-center items-center absolute top-0 right-0 "
      >
        <Trash className="size-4 hover:text-red-400" />
      </button>
    </div>
  );
}

export function UploadFileDialog({
  setFiles,
  files,
  parentFolderId
}: {
  setFiles: React.Dispatch<React.SetStateAction<FileState[]>>;
  files: FileState[];
  parentFolderId: string;
}) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button>
          <Upload />
          Create File
        </Button>
      </DialogTrigger>

      <DialogContent>
        <>
          <div className="flex flex-col gap-2">
            <div
              className="flex size-11 shrink-0 items-center justify-center rounded-full border border-border"
              aria-hidden="true"
            >
              <UploadIcon className="opacity-80" size={16} strokeWidth={2} />
            </div>
            <DialogHeader>
              <DialogTitle className="text-left">Upload files</DialogTitle>
              <DialogDescription className="text-left">
                Select multiple files and upload
              </DialogDescription>
            </DialogHeader>
          </div>
          <Separator />
          <div>
            <Dropzone
              onDrop={files =>
                setFiles(files.map(file => ({ file: file, progress: 0 })))
              }
              containerClassName="border rounded-2xl border-dashed"
            />

            <Button
              className="w-full mt-6"
              effect="shine"
              disabled={!files.length}
              onClick={() => uploadFiles(files, setFiles, parentFolderId)}
            >
              <Upload />
              Upload
            </Button>
          </div>
        </>
      </DialogContent>
    </Dialog>
  );
}

import { StorageItemsQuery, StorageItemType } from '@/generated/graphql';
import { formatFileSize } from '@/lib/utils';
import { FileText, Folder } from 'lucide-react';

export default function DriveItem({
  storageItem,
  onClick
}: {
  storageItem: StorageItemsQuery['storageItems'][number];
  onClick: (storageItem: StorageItemsQuery['storageItems'][number]) => void;
}) {
  const { name, size, type } = storageItem;
  return (
    <button
      className="hover:bg-lime-100 transition-all duration-300 p-3 rounded-xl"
      onClick={() => onClick(storageItem)}
    >
      {type === StorageItemType.File ? (
        <FileText className="size-10 text-lime-600" />
      ) : (
        <Folder className="size-10 text-lime-600" />
      )}
      <div className="font-medium truncate text-start text-sm">{name}</div>
    </button>
  );
}

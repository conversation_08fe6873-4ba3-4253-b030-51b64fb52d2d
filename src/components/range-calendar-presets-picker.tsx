import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import React, { useId } from 'react';
import { useFormContext } from 'react-hook-form';
import { z } from 'zod';
import { Button } from './ui/button';
import { Calendar, CalendarProps } from './ui/calendar';
import { Label } from './ui/label';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { DateRange } from 'react-day-picker';
import RangeCalendarWithPresets from './range-calendar-preset';

export default function RangeCalendarPresetsPicker({
  className,
  range,
  setDateRange,
  ...props
}: React.ComponentPropsWithoutRef<'div'> & {
  range: DateRange | undefined;
  setDateRange: (date: DateRange | undefined) => void;
}) {
  const id = useId();

  return (
    <div className={cn('space-y-2', className)} {...props}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id={id}
            variant={'outline'}
            className={cn(
              'focus-visible:outline-ring/20 group w-full justify-between bg-background px-3 font-normal outline-offset-0 hover:bg-background focus-visible:border-ring focus-visible:outline-[3px]',
              !range && 'text-muted-foreground'
            )}
          >
            <span className={cn('truncate', !range && 'text-muted-foreground')}>
              {range?.from && range.to
                ? `${format(range.from, 'MMM dd, yyyy')} - ${format(range.to, 'MMM dd, yyyy')}`
                : 'Pick a date'}
            </span>
            <CalendarIcon
              size={16}
              strokeWidth={2}
              className="text-muted-foreground/80 shrink-0 transition-colors group-hover:text-foreground"
              aria-hidden="true"
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-2" align="start">
          <RangeCalendarWithPresets range={range} setDateRange={setDateRange} />
        </PopoverContent>
      </Popover>
    </div>
  );
}

export const dateSchema = z.object({
  date: z.date()
});

export function FormRangeCalendarPickerPreset({
  name,
  label
}: {
  name: string;
  label?: string;
}) {
  const methods = useFormContext();
  const date = methods.watch(name);
  const errorMessage = methods.formState.errors[name];

  return (
    <div>
      {label && <Label>{label}</Label>}
      <RangeCalendarPresetsPicker
        range={date}
        setDateRange={date => methods.setValue(name, date)}
      />
      {!!errorMessage && (
        <p
          className="mt-2 text-xs text-destructive"
          role="alert"
          aria-live="polite"
        >
          Required
        </p>
      )}
    </div>
  );
}

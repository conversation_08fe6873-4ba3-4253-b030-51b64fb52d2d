import { Form } from '@/@types';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  endOfMonth,
  endOfYear,
  startOfMonth,
  startOfYear,
  subDays,
  subMonths,
  subYears
} from 'date-fns';
import _ from 'lodash';
import { useState } from 'react';
import { DateRange } from 'react-day-picker';
import { useController } from 'react-hook-form';

const today = new Date();
const yesterday = {
  from: subDays(today, 1),
  to: subDays(today, 1)
};
const last7Days = {
  from: subDays(today, 6),
  to: today
};
const last30Days = {
  from: subDays(today, 29),
  to: today
};
const monthToDate = {
  from: startOfMonth(today),
  to: today
};
const lastMonth = {
  from: startOfMonth(subMonths(today, 1)),
  to: endOfMonth(subMonths(today, 1))
};
const yearToDate = {
  from: startOfYear(today),
  to: today
};
const lastYear = {
  from: startOfYear(subYears(today, 1)),
  to: endOfYear(subYears(today, 1))
};

export default function RangeCalendarWithPresets({
  range = last7Days,
  setDateRange
}: {
  range: DateRange | undefined;
  setDateRange: (range: DateRange | undefined) => void;
}) {
  const [month, setMonth] = useState(today);

  return (
    <div>
      <div className="rounded-md border">
        <div className="flex max-sm:flex-col">
          <div className="relative py-4 max-sm:order-1 max-sm:border-t sm:w-32">
            <div className="h-full sm:border-e">
              <div className="flex flex-col px-2">
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setDateRange({
                      from: today,
                      to: today
                    });
                    setMonth(today);
                  }}
                >
                  Today
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setDateRange(yesterday);
                    setMonth(yesterday.to);
                  }}
                >
                  Yesterday
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setDateRange(last7Days);
                    setMonth(last7Days.to);
                  }}
                >
                  Last 7 days
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setDateRange(last30Days);
                    setMonth(last30Days.to);
                  }}
                >
                  Last 30 days
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setDateRange(monthToDate);
                    setMonth(monthToDate.to);
                  }}
                >
                  Month to date
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setDateRange(lastMonth);
                    setMonth(lastMonth.to);
                  }}
                >
                  Last month
                </Button>
                {/* <Button
                  disabled
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setDateRange(yearToDate)
                    setMonth(yearToDate.to)
                  }}
                >
                  Year to date
                </Button> */}
                {/* <Button
                  variant="ghost"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    setDateRange(lastYear)
                    setMonth(lastYear.to)
                  }}
                >
                  Last year
                </Button> */}
              </div>
            </div>
          </div>
          <Calendar
            mode="range"
            selected={range}
            onSelect={newDate => {
              if (newDate) {
                setDateRange(newDate);
              }
            }}
            month={month}
            onMonthChange={setMonth}
            className="p-2"
            disabled={[
              { after: today } // Dates before today
            ]}
          />
        </div>
      </div>
    </div>
  );
}

export function FormRangeCalendarWithPresets({
  name
}: Form<{
  range: DateRange | undefined;
  setDateRange: (range: DateRange | undefined) => void;
}>) {
  const {
    formState: { errors },
    field: { value, onChange }
  } = useController({ name });

  const errorMessage = _.get(errors, name)?.message as string;

  return (
    <div className="w-full">
      <RangeCalendarWithPresets range={value} setDateRange={onChange} />
      {errorMessage && (
        <p className="text-destructive text-sm">{errorMessage}</p>
      )}
    </div>
  );
}

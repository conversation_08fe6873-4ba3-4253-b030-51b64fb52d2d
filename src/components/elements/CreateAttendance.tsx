import Table from '@/components/ui/table/index';
import CreateAttendanceForm, {
  useCreateAttendanceFormMethods
} from '@/forms/attendance/CreateAttendanceForm';

import { format } from 'date-fns';
import { CalendarPlusIcon } from 'lucide-react';
import { Button } from '../ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '../ui/dialog';
import { Separator } from '../ui/separator';
import { useAttendanceQuery } from '@/generated/graphql';

type Props = {
  locationId: string;
  shiftId: string;
  guards?: any;
  date?: Date;
};

const TableHeaderComponent = ({ locationId, shiftId, guards, date }: Props) => {
  const createAttendanceFormMethods = useCreateAttendanceFormMethods();

  createAttendanceFormMethods.methods.setValue('locationId', locationId);
  createAttendanceFormMethods.methods.setValue('shiftId', shiftId);

  return (
    <div className="mt-4 sm:flex sm:items-center">
      <div className="sm:flex-auto">
        <h1 className="text-base font-semibold text-gray-900">Attendances</h1>
        <p className="mt-2 text-sm text-gray-700">User attendances by shift</p>
      </div>
      <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
        {/* create attendance dialog modal*/}
        <Dialog>
          <DialogTrigger asChild>
            <Button>Create Attendance</Button>
          </DialogTrigger>
          <DialogContent>
            <>
              <div className="flex flex-col gap-2">
                <div
                  className="flex size-11 shrink-0 items-center justify-center rounded-full border border-border"
                  aria-hidden="true"
                >
                  <CalendarPlusIcon
                    className="opacity-80"
                    size={16}
                    strokeWidth={2}
                  />
                </div>
                <DialogHeader>
                  <DialogTitle className="text-left">
                    Create User Attendance
                  </DialogTitle>
                  <DialogDescription className="text-left">
                    create attendance for a user by shift
                  </DialogDescription>
                </DialogHeader>
              </div>
              <Separator />
              <CreateAttendanceForm
                {...createAttendanceFormMethods}
                guards={guards}
                date={date}
              />
            </>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default function CreateAttendance({
  locationId,
  shiftId,
  guards,
  date
}: Props) {
    const { data: attendance } = useAttendanceQuery({ input: { shiftId } }, {
        initialData: { attendance: [] }
    });
    if (!attendance?.attendance) return null;
    const rowItems = attendance?.attendance.map(att => {
    return [
      att.user?.fullname,
      format(att.date, 'dd-MM-yy'),
      att.timeSpentInMinutes
        ? format(new Date(att.timeSpentInMinutes), 'dd-MM-yy')
        : '-',
      format(new Date(att.startTime), 'dd-MM-yy HH:mm'),
      att.endTime ? format(new Date(att.endTime), 'dd-MM-yy HH:mm') : '-',
      att.overTime
        ? format(new Date(att.overTime), 'dd-MM-yy HH:mm')
        : '-'
    ];
  });

  return (
    <div>
      <Table.StickyTable
        tableHeadComponent={
          <TableHeaderComponent
            locationId={locationId}
            shiftId={shiftId}
            guards={guards}
            date={date}
          />
        }
        headerItems={[
          ['Full Name', 'Date', 'Time spent', 'Start', 'End', 'OT']
        ]}
        rowItems={rowItems}
      />
    </div>
  );
}

import { cn } from '@/lib/utils';
import React from 'react';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { Filter } from 'lucide-react';
import { SelectSeparator } from './ui/select';

type FilterWrapperProps = {
  children: React.ReactNode | React.ReactNode[];
  offset?: number;
  className?: string;
  popoverWrapperClass?: string;
};

export const FilterWrapper = ({
  children,
  offset = 2,
  className,
  popoverWrapperClass
}: FilterWrapperProps) => {
  children = Array.isArray(children) ? children : [children];
  offset = Math.min(offset, (children as React.ReactNode[]).length);

  const childrenWithOffset = (children as React.ReactNode[]).slice(0, offset);
  const shouldRenderPopOver =
    childrenWithOffset.length < (children as React.ReactNode[]).length;

  return (
    <div className={cn('flex items-center gap-4', className)}>
      <>{childrenWithOffset}</>

      {shouldRenderPopOver && (
        <Popover>
          <PopoverTrigger>
            <div
              className={
                'flex size-11 shrink-0 items-center justify-center rounded-full border border-border bg-white'
              }
              aria-hidden="true"
            >
              <Filter className="opacity-80" size={16} strokeWidth={2} />
            </div>
          </PopoverTrigger>
          <PopoverContent
            className={cn('w-auto p-2 space-y-2', popoverWrapperClass)}
            align="start"
          >
            <div className="font-medium text-lg">Apply Filters</div>
            <SelectSeparator />
            {children}
          </PopoverContent>
        </Popover>
      )}
    </div>
  );
};

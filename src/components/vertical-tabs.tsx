import Badge from '@/components/ui/badge';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';
import { Box, House, PanelsTopLeft } from 'lucide-react';
import React from 'react';

type Tab = {
  key: string;
  icon: React.ReactNode;
  content?: React.ReactNode;
  badgeCount?: number;
  tooltipContent?: React.ReactNode;
};

export type VerticalTabProps = {
  tabs: Tab[];
};

export default function VerticalTabs({ tabs }: VerticalTabProps) {
  if (!tabs.length) return null;

  return (
    <Tabs
      defaultValue={tabs[0].key}
      orientation="vertical"
      className="flex w-full gap-2 p-2 "
    >
      <TabsList className="flex-col h-full gap-1 p-1  ">
        {tabs.map(tab => {
          return (
            <TooltipProvider key={tab.key} delayDuration={0}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <span>
                    <TabsTrigger value={tab.key} className="group py-3 ">
                      <span className="relative">
                        {tab.icon}
                        {!!tab.badgeCount && (
                          <Badge className="absolute -top-2.5 left-full min-w-4 -translate-x-1.5 border-background px-0.5 text-[10px]/[.875rem] transition-opacity group-data-[state=inactive]:opacity-50">
                            {tab.badgeCount}
                          </Badge>
                        )}
                      </span>
                    </TabsTrigger>
                  </span>
                </TooltipTrigger>
                <TooltipContent side="right" className="px-2 py-1 text-xs">
                  {tab.tooltipContent}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}
      </TabsList>
      <div className="grow rounded-lg border border-border text-start">
        {tabs.map(tab => {
          return (
            <TabsContent key={tab.key} value={tab.key}>
              <p className="px-4 py-1.5 text-xs text-muted-foreground">
                {tab.content}
              </p>
            </TabsContent>
          );
        })}
      </div>
    </Tabs>
  );
}

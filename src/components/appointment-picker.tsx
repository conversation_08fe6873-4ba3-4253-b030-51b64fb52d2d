import { cn } from '@/lib/utils';
import { format, formatDate } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import React, { useId } from 'react';
import { useFormContext } from 'react-hook-form';
import { z } from 'zod';
import { Button } from './ui/button';
import { Calendar } from './ui/calendar';
import { Label } from './ui/label';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { ScrollArea } from './ui/scroll-area';

const timeSlots = [
  { time: '00:00', available: true },
  { time: '00:30', available: true },
  { time: '01:00', available: true },
  { time: '01:30', available: true },
  { time: '02:00', available: true },
  { time: '02:30', available: true },
  { time: '03:00', available: true },
  { time: '03:30', available: true },
  { time: '04:00', available: true },
  { time: '04:30', available: true },
  { time: '05:00', available: true },
  { time: '05:30', available: true },
  { time: '06:00', available: true },
  { time: '06:30', available: true },
  { time: '07:00', available: true },
  { time: '07:30', available: true },
  { time: '08:00', available: true },
  { time: '08:30', available: true },
  { time: '09:00', available: true },
  { time: '09:30', available: true },
  { time: '10:00', available: true },
  { time: '10:30', available: true },
  { time: '11:00', available: true },
  { time: '11:30', available: true },
  { time: '12:00', available: true },
  { time: '12:30', available: true },
  { time: '13:00', available: true },
  { time: '13:30', available: true },
  { time: '14:00', available: true },
  { time: '14:30', available: true },
  { time: '15:00', available: true },
  { time: '15:30', available: true },
  { time: '16:00', available: true },
  { time: '16:30', available: true },
  { time: '17:00', available: true },
  { time: '17:30', available: true },
  { time: '18:00', available: true },
  { time: '18:30', available: true },
  { time: '19:00', available: true },
  { time: '19:30', available: true },
  { time: '20:00', available: true },
  { time: '20:30', available: true },
  { time: '21:00', available: true },
  { time: '21:30', available: true },
  { time: '22:00', available: true },
  { time: '22:30', available: true },
  { time: '23:00', available: true },
  { time: '23:30', available: true }
];

export default function AppointmentPicker({
  className,
  date,
  setDate,
  time,
  setTime,
  ...props
}: React.ComponentPropsWithoutRef<'div'> & {
  date?: Date;
  setDate: (date: Date) => void;
  time?: string;
  setTime: (time: string | null) => void;
}) {
  const id = useId();

  return (
    <div className={cn('space-y-2', className)} {...props}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id={id}
            variant={'outline'}
            className={cn(
              'focus-visible:outline-ring/20 group w-full justify-between bg-background px-3 font-normal outline-offset-0 hover:bg-background focus-visible:border-ring focus-visible:outline-[3px]',
              !date && 'text-muted-foreground'
            )}
          >
            <span className={cn('truncate', !date && 'text-muted-foreground')}>
              {date
                ? `${formatDate(date, 'PPP')} ${time ?? ''}`
                : 'Pick a date'}
            </span>
            <CalendarIcon
              size={16}
              strokeWidth={2}
              className="text-muted-foreground/80 shrink-0 transition-colors group-hover:text-foreground"
              aria-hidden="true"
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-2" align="start">
          <div className="flex max-sm:flex-col">
            <Calendar
              mode="single"
              selected={date}
              onSelect={newDate => {
                if (newDate) {
                  setDate(newDate);
                  setTime(null);
                }
              }}
              className="p-2 sm:pe-5"
            />
            <div className="relative w-full max-sm:h-48 sm:w-40">
              <div className="absolute inset-0 border-border py-4 max-sm:border-t">
                <ScrollArea className="h-full border-border sm:border-s">
                  <div className="space-y-3">
                    <div className="flex h-5 shrink-0 items-center px-5">
                      <p className="text-sm font-medium">
                        {date ? format(date, 'EEEE, d') : 'Select a date'}
                      </p>
                    </div>
                    <div className="grid gap-1.5 px-5 max-sm:grid-cols-2">
                      {timeSlots.map(({ time: timeSlot, available }) => (
                        <Button
                          key={timeSlot}
                          variant={time === timeSlot ? 'default' : 'outline'}
                          size="sm"
                          className="w-full"
                          onClick={() => setTime(timeSlot)}
                          disabled={!available}
                        >
                          {timeSlot}
                        </Button>
                      ))}
                    </div>
                  </div>
                </ScrollArea>
              </div>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}

export const dateTimeSchema = z.object({
  date: z.date(),
  time: z.string().default('09:00')
});

export function FormAppointmentPicker({
  name,
  label
}: {
  name: string;
  label?: string;
}) {
  const methods = useFormContext();

  const date = methods.watch(name + '.date');
  const time = methods.watch(name + '.time');

  const errorMessage = methods.formState.errors[name];

  return (
    <div>
      {label && <Label>{label}</Label>}
      <AppointmentPicker
        date={date}
        time={time}
        setDate={date => methods.setValue(name + '.date', date)}
        setTime={time => methods.setValue(name + '.time', time)}
      />
      {!!errorMessage && (
        <p
          className="mt-2 text-xs text-destructive"
          role="alert"
          aria-live="polite"
        >
          {errorMessage?.message?.toString()}
        </p>
      )}
    </div>
  );
}

/**
 * Converts a date and time into a combined Date object
 *
 * @param date - The date to convert (can be Date object or string)
 * @param time - Time string in "HH:mm" format (e.g., "09:00")
 * @returns A new Date object combining the date and time
 * @example
 * ```typescript
 * const result = dateTimeToDate(new Date('2023-12-25'), '09:00');
 * // Returns: Date object for December 25, 2023 at 09:00
 * ```
 */
export function dateTimeToDate({
  date,
  time
}: {
  date: Date | string;
  time: string;
}) {
  const dateStr = format(new Date(date), 'yyyy-MM-dd');
  return new Date(`${dateStr}T${time}`);
}

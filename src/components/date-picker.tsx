'use client';

import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import React, { useId } from 'react';
import { useFormContext } from 'react-hook-form';
import { z } from 'zod';
import { Button } from './ui/button';
import { Calendar, CalendarProps } from './ui/calendar';
import { Label } from './ui/label';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';

export default function DatePicker({
  className,
  date,
  setDate,
  calendarProps,
  disabled,
  ...props
}: React.ComponentPropsWithoutRef<'div'> & {
  date: Date;
  setDate: (date: Date) => void;
  calendarProps?: CalendarProps;
  disabled?: boolean;
}) {
  const id = useId();

  return (
    <div className={cn('space-y-2', className)} {...props}>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id={id}
            variant={'outline'}
            disabled={disabled}
            className={cn(
              'focus-visible:outline-ring/20 group w-full justify-between bg-background px-3 font-normal outline-offset-0 hover:bg-background focus-visible:border-ring focus-visible:outline-[3px]',
              !date && 'text-muted-foreground',
              disabled && 'opacity-50 cursor-not-allowed'
            )}
          >
            <span className={cn('truncate', !date && 'text-muted-foreground')}>
              {date ? format(date, 'PPP') : 'Pick a date'}
            </span>
            <CalendarIcon
              size={16}
              strokeWidth={2}
              className={cn(
                "text-muted-foreground/80 shrink-0 transition-colors group-hover:text-foreground",
                disabled && "opacity-50"
              )}
              aria-hidden="true"
            />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-2" align="start">
          <Calendar
            mode="single"
            // @ts-expect-error
            selected={date}
            // @ts-expect-error
            onSelect={newDate => {
              if (newDate && !disabled) {
                setDate(newDate);
              }
            }}
            disabled={disabled}
            className={cn("p-2", disabled && "opacity-50")}
            {...calendarProps}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
}

export const dateSchema = z.object({
  date: z.date()
});

export function FormDatePicker({
  name,
  label,
  calendarProps,
  disabled
}: {
  name: string;
  label?: string;
  calendarProps?: CalendarProps;
  disabled?: boolean;
}) {
  const methods = useFormContext();
  const date = methods.watch(name);
  const errorMessage = methods.formState.errors[name];

  return (
    <div>
      {label && <Label>{label}</Label>}
      <DatePicker
        date={date}
        setDate={date => methods.setValue(name, date)}
        calendarProps={calendarProps}
        disabled={disabled}
      />
      {!!errorMessage && (
        <p
          className="mt-2 text-xs text-destructive"
          role="alert"
          aria-live="polite"
        >
          Required
        </p>
      )}
    </div>
  );
}

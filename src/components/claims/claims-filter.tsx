import { Card } from "@/components/ui/card";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { Select } from "@/components/ui/select";

export type ClaimsFilter = {
  dateRange?: DateRange;
  status?: string;
  type?: string;
};

export function ClaimsFilter({
  filter,
  onChange
}: {
  filter: ClaimsFilter;
  onChange: (filter: ClaimsFilter) => void;
}) {
  return (
    <Card className="p-4">
      <div className="flex gap-4">
        <DateRangePicker
          value={filter.dateRange}
          onChange={(range) => onChange({ ...filter, dateRange: range })}
          placeholder="Select date range"
        />
        <Select
          value={filter.status}
          onValueChange={(status) => onChange({ ...filter, status })}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="approved">Approved</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
          </SelectContent>
        </Select>
        <Select
          value={filter.type}
          onValueChange={(type) => onChange({ ...filter, type })}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="allowance">Allowance</SelectItem>
            <SelectItem value="expense">Expense</SelectItem>
            <SelectItem value="travel">Travel</SelectItem>
            <SelectItem value="site">Site</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </Card>
  );
}
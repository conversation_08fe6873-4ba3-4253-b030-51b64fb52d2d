import * as React from 'react';

import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail
} from '@/components/ui/sidebar';
import { Link, useMatches } from '@tanstack/react-router';
import { data } from '@/lib/data';
import { Button } from './ui/button';
import { LogOut } from 'lucide-react';
import { getDefaultStore } from 'jotai';
import { tokenAtom } from '@/hooks/useAuth';

const links = {
  navMain: [
    {
      title: 'Dashboard',
      url: '#',
      items: [
        {
          title: 'Analytics',
          url: '/'
        }
      ]
    },
    {
      title: 'Users & Permissions',
      url: '#',
      items: [
        {
          title: 'Users',
          url: '/users'
        },
      ]
    },
    {
      title: 'Location management',
      url: '#',
      items: [
        {
          title: 'Locations',
          url: '/locations'
        },
        {
          title: 'Attendance',
          url: '/attendance'
        },
        {
          title: 'Inventory',
          url: '/inventory'
        },
        {
          title: 'Incidents Monitoring',
          url: '/incidents-monitoring'
        },
      ]
    },
    {
      title: 'HR',
      url: '#',
      items: [
        {
          title: 'Leaves',
          url: '/leaves'
        },
        {
          title: 'Holidays',
          url: '/holidays'
        },
        {
          title: 'Anouncements',
          url: '/anouncements'
        },
        {
          title: 'Claims',
          url: '/claims'
        }
      ]
    }
  ]
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const matchedRoutes = useMatches();

  return (
    <Sidebar {...props}>
      <SidebarHeader>
        <Link to="/">
          <img src={data.logo} alt="logo" className="h-16 w-auto" />
        </Link>
      </SidebarHeader>
      <SidebarContent>
        {/* We create a SidebarGroup for each parent. */}
        {links.navMain.map(item => (
          <SidebarGroup key={item.title}>
            <SidebarGroupLabel>{item.title}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {item.items.map(item => {
                  const isActive = matchedRoutes.some(
                    route => route.pathname === item.url
                  );
                  return (
                    <SidebarMenuItem key={item.title}>
                      <SidebarMenuButton asChild isActive={isActive}>
                        <Link to={item.url}>{item.title}</Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}

        <div className="flex-1 flex flex-col justify-end px-4 mb-4">
          <Button
            size="sm"
            variant="destructive"
            onClick={() => {
              getDefaultStore().set(tokenAtom, null);
            }}
          >
            <LogOut />
            Logout
          </Button>
        </div>
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}

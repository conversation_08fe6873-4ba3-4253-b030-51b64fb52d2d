import { Calendar } from '@/components/ui/calendar';
import { format } from 'date-fns';
import React, { useState } from 'react';
import { DayButtonProps, OnSelectHandler } from 'react-day-picker';

export default function ShiftCalendar({
  data,
  date,
  setDate
}: {
  data: Record<string, React.ReactNode>;
  date: Date;
  setDate: OnSelectHandler<Date | undefined>;
}) {
  return (
    <div>
      <Calendar
        mode="single"
        selected={date}
        onSelect={setDate}
        captionLayout="dropdown-months"
        pagedNavigation
        className="rounded-lg p-2"
        classNames={{
          months: 'sm:flex-col md:flex-row gap-8',
          month:
            'relative first-of-type:before:hidden before:absolute max-md:before:inset-x-2 max-md:before:h-px max-md:before:-top-4 md:before:inset-y-2 md:before:w-px before:bg-border md:before:-left-4',
          weekday: 'w-12',
          day_button: 'size-12 rounded-none',
          today: '*:after:hidden',
          month_caption: 'mx-0',
          day: 'border',
          outside: 'bg-secondary',
          caption_label: 'hidden'
        }}
        defaultMonth={date}
        startMonth={new Date(1980, 6)}
        hideNavigation
        components={{
          DayButton: (props: DayButtonProps) => (
            <DayButton {...props} data={data} />
          )
        }}
      />
    </div>
  );
}

function DayButton(
  props: DayButtonProps & { data: Record<string, React.ReactNode> }
) {
  const { day, modifiers, data, ...buttonProps } = props;
  const Component = data[format(day.date, 'yyyy-MM-dd')];

  return (
    <button {...buttonProps}>
      <span className="flex flex-col">
        {props.children}
        {Component}
      </span>
    </button>
  );
}

import { Check, ChevronsUpDown } from 'lucide-react';
import * as React from 'react';

import { Form } from '@/@types';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import _ from 'lodash';
import { useController } from 'react-hook-form';

type ComboboxOptions = {
  value: string;
  label: string;
}[];

export function Combobox({
  options,
  label,
  placeholder,
  value,
  setValue
}: {
  options: ComboboxOptions;
  label?: string;
  placeholder?: string;
  value?: string;
  setValue: (value: string) => void;
}) {
  const [open, setOpen] = React.useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-[200px] justify-between"
        >
          {value
            ? options.find(option => option.value === value)?.label
            : placeholder || 'Select an option'}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder={placeholder || 'Select an option'} />
          <CommandList>
            <CommandEmpty>Option not found</CommandEmpty>
            <CommandGroup>
              {options.map(option => (
                <CommandItem
                  key={option.value}
                  value={option.value}
                  onSelect={currentValue => {
                    setValue(currentValue === value ? '' : currentValue);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      'mr-2 h-4 w-4',
                      value === option.value ? 'opacity-100' : 'opacity-0'
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}

export function FormComboBox({
  name,
  type,
  ...rest
}: Form<{ options: ComboboxOptions }>) {
  const {
    field: { onChange, value },
    fieldState: { error }
  } = useController({ name });

  const errorMessage = _.get(error, name) as string;

  return (
    <div className="w-full">
      <Combobox {...rest} value={value} setValue={onChange} />
      {errorMessage && (
        <div className="text-red-500 text-sm">{errorMessage}</div>
      )}
    </div>
  );
}

import { cn } from '@/lib/utils';
import React from 'react';

type StickyHeaderProps = {
  className?: string;
  tableHeadComponent?: React.ReactNode;
  headerItems?: React.ReactNode[][];
  rowItems?: React.ReactNode[][];
};

export function StickHeader({
  className,
  tableHeadComponent,
  headerItems,
  rowItems
}: StickyHeaderProps) {
  return (
    <div
      className={cn(
        'rounded-xl border bg-white px-4 sm:px-6 lg:px-8',
        className
      )}
    >
      {tableHeadComponent}
      <div className="mt-8 flow-root">
        <div className="-mx-4 -my-2 sm:-mx-6 lg:-mx-8">
          <div className="inline-block min-w-full py-2 align-middle">
            <table className="min-w-full border-separate border-spacing-0">
              <thead>
                {headerItems?.map((head, headIdx) => (
                  <tr key={headIdx}>
                    {head.map(
                      (component, idx) =>
                        component && (
                          <th
                            key={idx}
                            scope="col"
                            className="sticky top-0 z-10 border-b border-gray-300 bg-white/75 py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 backdrop-blur-sm sm:pl-6 lg:pl-8"
                          >
                            {component}
                          </th>
                        )
                    )}
                  </tr>
                ))}
              </thead>
              <tbody>
                {rowItems?.map((row, rowIdx) => (
                  <tr key={rowIdx}>
                    {row.map((cell, idx) => (
                      <td
                        key={idx}
                        className={cn(
                          'whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 lg:pl-8',
                          rowIdx < rowItems!.length - 1 &&
                            'border-b border-gray-200'
                        )}
                      >
                        {cell}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

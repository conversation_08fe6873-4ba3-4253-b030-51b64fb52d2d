import { cn } from '@/lib/utils';
import React from 'react';

export default function Badge({
  className,
  ...props
}: React.ComponentPropsWithoutRef<'span'>) {
  return (
    <span
      className={cn(
        'inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20',
        className
      )}
      {...props}
    >
      {props.children}
    </span>
  );
}

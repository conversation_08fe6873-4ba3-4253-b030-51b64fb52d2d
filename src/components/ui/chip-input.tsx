'use client';

import { Tag, TagInput } from 'emblor';
import React, { useId, useState } from 'react';

export default function ChipInput(
  props: React.ComponentProps<typeof TagInput>
) {
  const id = useId();
  const [activeTagIndex, setActiveTagIndex] = useState<number | null>(null);

  return (
    <TagInput
      id={id}
      styleClasses={{
        inlineTagsContainer:
          'border-input rounded-md bg-background shadow-xs transition-[color,box-shadow] focus-within:border-ring outline-none focus-within:ring-ring/50 p-1 gap-1',
        input: 'w-full min-w-[80px] shadow-none px-2 h-7',
        tag: {
          body: 'h-7 relative bg-background border border-input hover:bg-background rounded-md font-medium text-xs ps-2 pe-7',
          closeButton:
            'absolute -inset-y-px -end-px p-0 rounded-e-md flex size-7 transition-[color,box-shadow] outline-none focus-visible:border-ring focus-visible:ring-ring/50  text-muted-foreground/80 hover:text-foreground'
        }
      }}
      {...props}
      activeTagIndex={activeTagIndex}
      setActiveTagIndex={setActiveTagIndex}
    />
  );
}

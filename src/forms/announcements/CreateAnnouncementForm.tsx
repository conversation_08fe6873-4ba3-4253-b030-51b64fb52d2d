import { Button } from '@/components/ui/button';
import <PERSON><PERSON>ield from '@/components/forms/FormField';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import FormTextArea from '@/components/forms/FormTextArea';
import { FormDatePicker } from '@/components/date-picker';
import { useCreateAnnouncementMutation } from '@/generated/graphql';
import { FormPresignedUpload } from '@/components/forms/FormPresignedUpload';
import FormMultiSelect from '@/components/forms/FormMultiSelect';
import { UserRoles } from '@/generated/graphql';

// Update the schema to expect objects instead of strings
const schema = z.object({
  title: z.string().nonempty('Title is required'),
  description: z.string().nonempty('Description is required'),
  date: z.date({
    required_error: 'Date is required'
  }),
  userRoles: z
    .array(
      z.object({
        value: z.string(),
        label: z.string()
      })
    )
    .optional(),
  users: z
    .array(
      z.object({
        value: z.string(),
        label: z.string()
      })
    )
    .optional(),
  document: z
    .string()
    .optional()
    .describe('PDF, Word documents or images (PNG, JPG, GIF)')
});

type CreateAnnouncementFormValues = z.infer<typeof schema>;

export const useCreateAnnouncementForm = () => {
  const methods = useForm<CreateAnnouncementFormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      title: '',
      description: '',
      userRoles: [],
      users: [],
      document: ''
    }
  });

  return { methods };
};

interface CreateAnnouncementFormProps {
  users: { id: string; fullname: string }[];
}

export default function CreateAnnouncementForm({
  users
}: CreateAnnouncementFormProps) {
  const { methods } = useCreateAnnouncementForm();
  const { mutateAsync: createAnnouncement } = useCreateAnnouncementMutation();

  const onSubmit = async (data: CreateAnnouncementFormValues) => {
    try {
      await toast.promise(
        createAnnouncement({
          createAnnouncementInput: {
            title: data.title,
            description: data.description,
            date: data.date,
            users: data.users?.map(user => user.value) || [],
            userRoles: data.userRoles?.map(role => role.value as UserRoles) || [],
            document: data.document
          }
        }),
        {
          loading: 'Creating announcement...',
          success: 'Announcement created successfully',
          error: 'Failed to create announcement'
        }
      );
      methods.reset();
    } catch (error) {
      console.error('Error creating announcement:', error);
      toast.error('Failed to create announcement. Please try again.');
    }
  };

  // Add this function to handle submit errors
  const onError = (errors: any) => {
    console.log('Validation errors:', errors);
  };

  return (
    <FormProvider {...methods}>
      <form
        onSubmit={methods.handleSubmit(onSubmit, onError)}
        className="space-y-4"
        noValidate
      >

        <FormField
          name="title"
          label="Title"
          placeholder="Enter announcement title"
        />

        <FormTextArea
          name="description"
          label="Description"
          placeholder="Enter announcement description"
          className="min-h-24"
        />

        <FormDatePicker name="date" label="Announcement Date" />

        <FormMultiSelect
          name="userRoles"
          label="Target Roles"
          placeholder="Select target roles"
          options={Object.values(UserRoles).map(role => ({
            label: role,
            value: role
          }))}
        />

        <FormMultiSelect
          name="users"
          label="Users"
          placeholder="Select users"
          options={users.map((s: any) => ({
            value: s.id,
            label: s.fullname
          }))}
        />

        <FormPresignedUpload
          name="document"
          label="Upload Document"
          accept=".pdf,.doc,.docx,.png,.jpg,.jpeg,.gif"
          multiple={false}
        />

        <Button type="submit" className="w-full">
          Create Announcement
        </Button>
      </form>
    </FormProvider>
  );
}

import { FormDatePicker } from '@/components/date-picker';
import { But<PERSON> } from '@/components/ui/button';
import FormTextArea from '@/components/forms/FormTextArea';
import { FormPresignedUpload } from '@/components/forms/FormPresignedUpload';
import FormMultiSelect from '@/components/forms/FormMultiSelect';
import { errorHandler } from '@/lib/utils';
import FormField from '@/components/forms/FormField';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  FormProvider,
  SubmitHandler,
  useForm,
  UseFormReturn
} from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { UserRoles, useUpdateAnnoucementMutation } from '@/generated/graphql';
import { useState } from 'react';

// Define or import the Id type
type Id<T extends string> = string & { __type: T };

const schema = z.object({
  announcementId: z.string(),
  title: z.string().nonempty('Title is required'),
  description: z.string().nonempty('Description is required'),
  date: z.date({
    required_error: 'Date is required'
  }),
  userRoles: z
    .array(
      z.object({
        value: z.string(),
        label: z.string()
      })
    )
    .optional(),
  users: z
    .array(
      z.object({
        value: z.string(),
        label: z.string()
      })
    )
    .optional(),
  document: z
    .string()
    .optional()
    .describe('PDF, Word documents or images (PNG, JPG, GIF)')
});

type UpdateAnnouncementForm = z.infer<typeof schema>;

export type UpdateAnnouncementFormProps = {
  onSubmit: SubmitHandler<UpdateAnnouncementForm>;
  methods: UseFormReturn<UpdateAnnouncementForm>;
  users?: Array<{ id: string; fullname: string }>;
};

export const useUpdateAnnouncementFormMethods = () => {
  const { mutateAsync: updateAnnouncement } = useUpdateAnnoucementMutation();

  const methods = useForm<UpdateAnnouncementForm>({
    defaultValues: {
      title: '',
      description: '',
      date: new Date(),
      document: '',
      users: [],
      userRoles: []
    },
    resolver: zodResolver(schema)
  });

  const onSubmit: SubmitHandler<UpdateAnnouncementForm> = async data => {
    try {
      await updateAnnouncement({
        id: data.announcementId as Id<'announcements'>,
        updateAnouncementInput: {
          title: data.title,
          description: data.description,
          date: data.date,
          document: data.document,
          users: data.users?.map((user: any) => user.value) || [],
          userRoles:
            data.userRoles?.map((role: any) => role.value as UserRoles) || []
        }
      });

      toast.success('Announcement updated successfully');
    } catch (ex) {
      errorHandler(ex);
    }
  };

  return { methods, onSubmit };
};

export default function UpdateAnnouncementForm({
  methods,
  onSubmit,
  users
}: UpdateAnnouncementFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const handleSubmit = async (data: UpdateAnnouncementForm) => {
    try {
      setIsSubmitting(true);
      await onSubmit(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(handleSubmit)} className="space-y-4">
          <FormField
            name="title"
            label="Title"
            placeholder="Enter announcement title"
          />

          <FormTextArea
            name="description"
            label="Description"
            placeholder="Enter announcement description"
          />

          <FormDatePicker name="date" label="Date" />

          <FormPresignedUpload
            name="document"
            label="Document"
            accept=".pdf,.doc,.docx,.png,.jpg,.jpeg,.gif"
            multiple={false}
          />

          <FormMultiSelect
            name="users"
            label="Select Users"
            placeholder="Select users to notify"
            options={
              users?.map(user => ({
                label: user.fullname,
                value: user.id
              })) || []
            }
          />

          <FormMultiSelect
            name="userRoles"
            label="Select Roles"
            placeholder="Select roles to notify"
            options={Object.values(UserRoles).map(role => ({
              label: role,
              value: role
            }))}
          />

          <Button
            type="submit"
          >
            {isSubmitting ? 'Updating...' : 'Update Announcement'}
          </Button>
        </form>
      </FormProvider>
    </div>
  );
}

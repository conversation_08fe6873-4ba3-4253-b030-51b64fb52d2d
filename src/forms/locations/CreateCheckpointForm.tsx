import { FormProvider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import FormField from '@/components/forms/FormField';
import { useCreateCheckpointMutation } from '@/generated/graphql';
import { toast } from 'sonner';

const schema = z.object({
  name: z.string().nonempty('Name is required'),
});

type CreateCheckpointForm = z.infer<typeof schema>;

interface CreateCheckpointFormProps {
  locationId: string;
  onSuccess: () => void;
}

export default function CreateCheckpointForm({ locationId, onSuccess }: CreateCheckpointFormProps) {
  const methods = useForm<CreateCheckpointForm>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
    }
  });

  const { mutateAsync: createCheckpoint } = useCreateCheckpointMutation();

  const onSubmit = async (data: CreateCheckpointForm) => {
    toast.promise(
      createCheckpoint({ 
        createCheckpointInput: {
          ...data,
          location: locationId
        }
      }), 
      {
        loading: 'Creating checkpoint...',
        success: () => {
          onSuccess();
          return 'Checkpoint created successfully';
        },
        error: 'Failed to create checkpoint'
      }
    );
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <FormField name="name" label="Name" placeholder="Enter checkpoint name" />
        <Button type="submit">Create Checkpoint</Button>
      </form>
    </FormProvider>
  );
}

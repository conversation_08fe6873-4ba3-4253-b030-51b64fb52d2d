import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { FormProvider, useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';
import FormField from '@/components/forms/FormField';
import { Button } from '@/components/ui/button';
import FormPhoneInput from '@/components/forms/FormPhoneInput';
import FormTextArea from '@/components/forms/FormTextArea';
import FormGeoFence from '@/components/forms/FormGeoFence';
import { useCreateLocationMutation } from '@/generated/graphql';

const schema = z.object({
  name: z.string().nonempty('Name is required'),
  description: z.string().optional(),
  address: z.string().optional(),
  emergencyContact: z.string().optional(),
  geofence: z.object({
    center: z.array(z.number()).length(2),
    coords: z.array(z.array(z.number()).length(2))
  })
});

type CreateLocationForm = z.infer<typeof schema>;

export const useCreateLocationForm = () => {
  const methods = useForm<CreateLocationForm>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
      description: '',
      address: '',
      emergencyContact: '',
      geofence: {
        // Ensure proper type casting for center coordinates
        center: [3.140853, 101.693207],
        // Ensure coords are properly formatted
        coords: []
      }
    }
  });

  return { methods };
};

export default function CreateLocationForm() {
  const { methods } = useCreateLocationForm();
  const { mutateAsync: createLocation } = useCreateLocationMutation();

  const onSubmit = async (data: CreateLocationForm) => {
    console.log('Form Submitted:', data);
    
    try {
      await toast.promise(createLocation({ createLocationInput: data }), {
        loading: 'Creating location...',
        success: 'Location created successfully',
        error: 'Failed to create location'
      });
      methods.reset();
    } catch (error) {
      console.error('Submit Error:', error);
    }
  };

  const onError = (errors: any) => {
    console.error('Validation Errors:', errors);
  };

  return (
    <FormProvider {...methods}>
      <form 
        onSubmit={methods.handleSubmit(onSubmit, onError)} 
        className="space-y-4"
      >
        <FormField name="name" label="Name" placeholder="Enter location name" />
        <FormTextArea
          name="description"
          label="Description"
          placeholder="Enter location description"
        />
        <FormTextArea
          name="address"
          label="Address"
          placeholder="Enter location address"
          className="min-h-24"
        />
        <FormPhoneInput
          name="emergencyContact"
          label="Emergency Contact"
          placeholder="Emergency contact"
        />

        <FormGeoFence
          name="geofence"
          label="Location Boundary"
          placeholder="Search location address"
        />

        <Button type="submit">Create Location</Button>
      </form>
    </FormProvider>
  );
}

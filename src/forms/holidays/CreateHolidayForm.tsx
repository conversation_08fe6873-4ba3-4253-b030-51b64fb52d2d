import { Button } from '@/components/ui/button';
import <PERSON><PERSON>ield from '@/components/forms/FormField';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import FormTextArea from '@/components/forms/FormTextArea';
import { FormDatePicker } from '@/components/date-picker';
import { useCreateHolidayMutation } from '@/generated/graphql';

const schema = z.object({
  name: z.string().nonempty('Holiday name is required'),
  date: z.date({
    required_error: 'Date is required',
  }),
  description: z.string().nonempty('Description is required'),
});

type CreateHolidayFormValues = z.infer<typeof schema>;

export const useCreateHolidayForm = () => {
  const methods = useForm<CreateHolidayFormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
      description: '',
    }
  });

  return { methods };
};

export default function CreateHolidayForm() {
  const { methods } = useCreateHolidayForm();
  const { mutateAsync: createHoliday } = useCreateHolidayMutation();

  const onSubmit = async (data: CreateHolidayFormValues) => {
    toast.promise(createHoliday({ createHolidayInput: { ...data, date: data.date.toISOString() } }), {
      loading: 'Creating holiday...',
      success: 'Holiday created successfully',
      error: 'Failed to create holiday'
    });
    methods.reset();
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <FormField name="name" label="Name" placeholder="Enter holiday name" />

        <FormDatePicker
          name="date"
          label="Holiday Date"
        />

        <FormTextArea
          name="description"
          label="Description"
          placeholder="Enter holiday description"
          className="min-h-24"
        />

        <Button type="submit" className="w-full">
          Create Holiday
        </Button>
      </form>
    </FormProvider>
  );
}
import { Button } from '@/components/ui/button';
import <PERSON><PERSON>ield from '@/components/forms/FormField';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { toast } from 'sonner';
import FormTextArea from '@/components/forms/FormTextArea';
import { FormDatePicker } from '@/components/date-picker';
import { useUpdateHolidayMutation } from '@/generated/graphql';

const schema = z.object({
  id: z.string(),
  name: z.string().nonempty('Holiday name is required'),
  date: z.date({
    required_error: 'Date is required',
  }),
  description: z.string().nonempty('Description is required'),
});

type UpdateHolidayFormValues = z.infer<typeof schema>;

interface UpdateHolidayFormProps {
  methods: ReturnType<typeof useUpdateHolidayForm>['methods'];
}

export const useUpdateHolidayForm = () => {
  const methods = useForm<UpdateHolidayFormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: '',
      description: '',
    }
  });

  return { methods };
};

export default function UpdateHolidayForm({ methods }: UpdateHolidayFormProps) {
  const { mutateAsync: updateHoliday } = useUpdateHolidayMutation();

  const onSubmit = async (data: UpdateHolidayFormValues) => {
    toast.promise(
      updateHoliday({
        updateHolidayInput: {
          name: data.name,
          date: data.date.toISOString(),
          description: data.description
        },
        holidayId: data.id
      }),
      {
        loading: 'Updating holiday...',
        success: 'Holiday updated successfully',
        error: 'Failed to update holiday'
      }
    );
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <FormField 
          name="name" 
          label="Name" 
          placeholder="Enter holiday name" 
        />

        <FormDatePicker
          name="date"
          label="Holiday Date"
        />

        <FormTextArea
          name="description"
          label="Description"
          placeholder="Enter holiday description"
          className="min-h-24"
        />

        <Button type="submit" className="w-full">
          Update Holiday
        </Button>
      </form>
    </FormProvider>
  );
}
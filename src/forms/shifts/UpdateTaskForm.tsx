import FormCheckBox from '@/components/forms/FormCheckBox';
import <PERSON><PERSON>ield from '@/components/forms/FormField';
import FormMultiSelect, {
  multiSelectSchema
} from '@/components/forms/FormMultiSelect';
import FormTextArea from '@/components/forms/FormTextArea';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { errorHandler } from '@/lib/utils';
import {
  FormProvider,
  SubmitHandler,
  useForm,
  UseFormReturn
} from 'react-hook-form';
import { toast } from 'sonner';
import { date, z } from 'zod';

const schema = z.object({
  taskId: z.string(),
  updateRecurring: z.boolean().optional(),
  userIds: multiSelectSchema.min(1),
  title: z.string(),
  description: z.string()
});

type Form = z.infer<typeof schema>;

export type CreateTaskFormProps = {
  onSubmit: SubmitHandler<Form>;
  methods: UseFormReturn<Form>;
};

export const useUpdateTaskFormMethods = (c?: (s: boolean) => void) => {
  const updateTask = useMutation(api.tasks.updateTasks);

  const methods = useForm<Form>({
    defaultValues: {
      description: '',
      title: '',
      userIds: [],
      taskId: '',
      updateRecurring: false
    }
  });

  const onSubmit: SubmitHandler<Form> = async data => {
    try {
      await updateTask({
        description: data.description,
        title: data.title,
        taskId: data.taskId as Id<'tasks'>,
        userIds: data.userIds.map(u => u.value) as Id<'users'>[],
        updateRecurring: data.updateRecurring
      });
      methods.reset();
      c?.(false);

      toast.success('Task updated successfully');
    } catch (ex) {
      errorHandler(ex);
    }
  };

  return { methods, onSubmit };
};

export default function UpdateTaskForm({
  methods,
  onSubmit
}: CreateTaskFormProps) {
  const securityGuards = useQuery(api.users.getUsers, {
    roles: ['local-guard', 'nepal-guard', 'buffer-guard']
  });

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-2">
        <Separator />

        <FormField name="title" label="Title" placeholder="Enter title" />
        <FormTextArea
          name="description"
          label="Description"
          placeholder="Enter Description"
        />
        <FormMultiSelect
          name="userIds"
          label="Guards"
          placeholder="Select guards"
          options={(securityGuards ?? []).map(s => ({
            value: s._id,
            label: s.fullName
          }))}
        />
        <FormCheckBox name="updateRecurring" label="Recurring" />
        <Button type="submit" className="w-full">
          update Task
        </Button>
      </form>
    </FormProvider>
  );
}

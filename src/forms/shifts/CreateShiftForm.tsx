import {
  dateTimeSchema,
  FormAppointmentPicker
} from '@/components/appointment-picker';
import FormMultiSelect, {
  multiSelectSchema
} from '@/components/forms/FormMultiSelect';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { zodResolver } from '@hookform/resolvers/zod';
import { Clock, Plus } from 'lucide-react';
import { FormProvider, useForm } from 'react-hook-form';
import { z } from 'zod';
import FormCheckBox from '@/components/forms/FormCheckBox';
import { errorHandler, combineDateTime } from '@/lib/utils';
import { toast } from 'sonner';
import { useEffect } from 'react';
import {
  useCreateShiftMutation,
  UserRoles,
  useUsersQuery
} from '@/generated/graphql';

const schema = z
  .object({
    locationId: z.string(),
    startDateTime: dateTimeSchema,
    endDateTime: dateTimeSchema,
    overTime: dateTimeSchema.optional(),
    userIds: multiSelectSchema.min(1),
    isRecurring: z.boolean().optional(),
    recurringId: z.string().optional().default(crypto.randomUUID())
  })
  .superRefine((data, ctx) => {
    if (data.isRecurring) {
      data.recurringId = crypto.randomUUID();
    } else {
      // @ts-expect-error
      data.recurringId = undefined;
    }

    const startDate = combineDateTime(
      data.startDateTime.date,
      data.startDateTime.time
    );
    const endDate = combineDateTime(
      data.endDateTime.date,
      data.endDateTime.time
    );

    const overTime = data.overTime
      ? combineDateTime(data.overTime.date, data.overTime.time)
      : undefined;

    // endDateTime should be greater than startDateTime
    if (endDate < startDate) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        path: ['endDateTime'],
        message: 'End date&time should be greater than start date&time'
      });
    }

    // if overTime is set, it should be greater than startDateTime & less than endDateTime
    if (overTime) {
      if (overTime < startDate || overTime > endDate) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          path: ['overTime'],
          message:
            'Over time should be between start date&time and end date&time'
        });
      }
    }
  });

type Form = z.infer<typeof schema>;

export default function CreateShiftForm({
  locationId,
  date
}: {
  locationId: string;
  date?: Date;
}) {
  const methods = useForm<Form>({
    defaultValues: {
      startDateTime: { date: date ?? new Date(), time: '09:00' },
      endDateTime: { date: date ?? new Date(), time: '17:00' },
      overTime: undefined,
      isRecurring: true,
      locationId,
      recurringId: undefined,
      userIds: []
    },
    resolver: zodResolver(schema)
  });

  useEffect(() => {
    if (!date) return;
    methods.setValue('startDateTime.date', date);
    methods.setValue('endDateTime.date', date);
  }, [date]);

  const { data: securityGuards } = useUsersQuery(
    {
      input: {
        roles: [
          UserRoles.LocalGuard,
          UserRoles.NepalGuard,
          UserRoles.BufferGuard
        ]
      }
    },
    { initialData: { users: [] } }
  );

  const { mutateAsync: createShift } = useCreateShiftMutation();

  const submit = methods.handleSubmit(async data => {
    try {
      const startDateTime = combineDateTime(
        data.startDateTime.date,
        data.startDateTime.time
      );
      const endDateTime = combineDateTime(
        data.endDateTime.date,
        data.endDateTime.time
      );

      const overTime = data.overTime
        ? combineDateTime(data.overTime.date, data.overTime.time)
        : undefined;

      toast.promise(
        createShift({
          input: {
            recurringId: data.recurringId,
            startDateTime,
            endDateTime,
            locationId,
            overTime,
            userIds: data.userIds.map(s => s.value),
            isRecurring: data.isRecurring
          }
        }),
        {
          loading: 'Creating shift...',
          success: 'Shift created successfully',
          error: 'Failed to create shift'
        }
      );

      methods.reset();
    } catch (ex) {
      errorHandler(ex);
    }
  });

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button size="sm">
          <Plus size={16} />
          Create Shift
        </Button>
      </DialogTrigger>
      <DialogContent>
        <div className="flex flex-col gap-2">
          <div
            className="flex size-11 shrink-0 items-center justify-center rounded-full border border-border"
            aria-hidden="true"
          >
            <Clock className="opacity-80" size={16} strokeWidth={2} />
          </div>
          <DialogHeader>
            <DialogTitle className="text-left">Create Shift</DialogTitle>
            <DialogDescription className="text-left">
              Create & assign shift to users
            </DialogDescription>
          </DialogHeader>
        </div>
        <Separator />
        <FormProvider {...methods}>
          <form onSubmit={submit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormAppointmentPicker
                name="startDateTime"
                label="Start Date&Time"
              />
              <FormAppointmentPicker name="endDateTime" label="End Date&Time" />
              <FormAppointmentPicker name="overTime" label="Over Time" />
            </div>
            <FormMultiSelect
              name="userIds"
              label="Guards"
              placeholder="Select guards"
              options={securityGuards!.users.map(s => ({
                value: s.id,
                label: s.fullname
              }))}
            />
            <Separator />
            <FormCheckBox name="isRecurring" label="Recurring" />
            <div className="grid grid-cols-2 gap-4">
              <Button
                variant="destructive"
                className="w-full"
                effect="shineHover"
                size="sm"
                onClick={() => methods.reset()}
              >
                Reset
              </Button>
              <Button className="w-full" effect="shineHover" size="sm">
                Create Shift
              </Button>
            </div>
          </form>
        </FormProvider>
      </DialogContent>
    </Dialog>
  );
}

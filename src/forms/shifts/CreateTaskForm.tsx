import FormCheckBox from '@/components/forms/FormCheckBox';
import <PERSON><PERSON>ield from '@/components/forms/FormField';
import FormMultiSelect, {
  multiSelectSchema
} from '@/components/forms/FormMultiSelect';
import FormTextArea from '@/components/forms/FormTextArea';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

import {
  FormProvider,
  SubmitHandler,
  useForm,
  UseFormReturn
} from 'react-hook-form';
import { z } from 'zod';

const schema = z.object({
  locationId: z.string(),
  shiftId: z.string(),
  userIds: multiSelectSchema.min(1),
  title: z.string(),
  description: z.string(),
  isRecurring: z.boolean()
});

type Form = z.infer<typeof schema>;

export type CreateTaskFormProps = {
  onSubmit: SubmitHandler<Form>;
  methods: UseFormReturn<Form>;
  guards: any[];
};

export const useCreateTaskFormMethods = (c?: (s: boolean) => void) => {
  const methods = useForm<Form>({
    defaultValues: {
      description: '',
      locationId: '',
      shiftId: '',
      title: '',
      userIds: [],
      isRecurring: false
    }
  });

  const onSubmit: SubmitHandler<Form> = async data => {
    try {
    } catch (ex) {}
  };

  return { methods, onSubmit };
};

export default function CreateTaskForm({
  methods,
  onSubmit,
  guards
}: CreateTaskFormProps) {
  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-2">
        <Separator />

        <FormField name="title" label="Title" placeholder="Enter title" />
        <FormTextArea
          name="description"
          label="Description"
          placeholder="Enter Description"
        />
        <FormMultiSelect
          name="userIds"
          label="Guards"
          placeholder="Select guards"
          options={(guards ?? []).map(s => ({
            value: s._id,
            label: s.fullName
          }))}
        />
        <FormCheckBox name="isRecurring" label="Recurring" />
        <Button type="submit" className="w-full">
          Create Task
        </Button>
      </form>
    </FormProvider>
  );
}

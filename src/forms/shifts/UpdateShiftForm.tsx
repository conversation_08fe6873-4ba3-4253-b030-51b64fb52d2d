import FormMultiSelect, {
  multiSelectSchema
} from '@/components/forms/FormMultiSelect';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { zodResolver } from '@hookform/resolvers/zod';
import { FormProvider, useForm, UseFormReturn } from 'react-hook-form';
import { z } from 'zod';
import FormCheckBox from '@/components/forms/FormCheckBox';
import { errorHandler } from '@/lib/utils';
import { toast } from 'sonner';
import {
  UserRoles,
  useUsersQuery,
  useUpdateShiftMutation
} from '@/generated/graphql';

const schema = z.object({
  shiftId: z.string(),
  userIds: multiSelectSchema.min(1),
  updateRecurring: z.boolean().optional()
});

type Form = z.infer<typeof schema>;

export const useUpdateShiftFormMethods = (onSuccess?: () => void) => {
  const methods = useForm<Form>({
    defaultValues: {
      userIds: [],
      shiftId: '',
      updateRecurring: true
    },
    resolver: zodResolver(schema)
  });
  const { mutateAsync: updateShift } = useUpdateShiftMutation();

  const onSubmit = async (data: Form) => {
    try {
      toast.promise(
        updateShift({
          id: data.shiftId,
          input: {
            userIds: data.userIds.map(s => s.value),
            isRecurring: data.updateRecurring
          }
        }),
        {
          loading: 'Updating shift...',
          success: 'Shift(s) updated successfully',
          error: 'Failed to update shift(s)'
        }
      );
      onSuccess?.();
    } catch (ex) {
      errorHandler(ex);
    }
  };

  return { onSubmit, methods };
};

export default function UpdateShiftForm({
  methods,
  onSubmit
}: {
  methods: UseFormReturn<Form>;
  onSubmit: (data: Form) => Promise<void>;
}) {
  const { data: securityGuards } = useUsersQuery(
    {
      input: {
        roles: [
          UserRoles.LocalGuard,
          UserRoles.NepalGuard,
          UserRoles.BufferGuard
        ]
      }
    },
    { initialData: { users: [] } }
  );

  // Log on submit
  const handleSubmit = async (data: Form) => {
    await onSubmit(data);
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(handleSubmit)} className="space-y-4">
        <FormMultiSelect
          name="userIds"
          label="Guards"
          placeholder="Select guards"
          options={(securityGuards?.users ?? []).map(s => ({
            value: s.id,
            label: s.fullname
          }))}
        />
        <Separator />
        <FormCheckBox name="updateRecurring" label="Update Recurring Shifts" />
        <Button className="w-full" effect="shineHover">
          Update Shift
        </Button>
      </form>
    </FormProvider>
  );
}

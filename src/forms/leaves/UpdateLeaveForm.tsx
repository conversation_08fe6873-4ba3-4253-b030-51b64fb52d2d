import { zod<PERSON><PERSON><PERSON>ver } from '@hookform/resolvers/zod';
import { FormProvider, useForm, UseFormReturn } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import FormTextArea from '@/components/forms/FormTextArea';
import { FormDatePicker } from '@/components/date-picker';
import FormSelect from '@/components/forms/FormSelect';
import { SelectGroup, SelectItem, SelectLabel } from '@/components/ui/select';
import {
  useUpdateLeaveMutation,
  LeaveType,
  LeaveStatus
} from '@/generated/graphql';
import { enumToOptions } from '@/lib/utils';

// Update the schema to include status and rejectedReason
const schema = z.object({
  id: z.string(),
  leaveType: z.nativeEnum(LeaveType, {
    errorMap: () => ({ message: 'Leave type is required' })
  }),
  user: z.string().nonempty('User is required'),
  startDateTime: z.date({
    required_error: 'Start date is required'
  }),
  endDateTime: z.date({
    required_error: 'End date is required'
  }),
  reason: z.string().nonempty('Reason is required'),
  leaveStatus: z.nativeEnum(LeaveStatus, {
    errorMap: () => ({ message: 'Status is required' })
  }),
  rejectedReason: z.string().optional()
});

type UpdateLeaveForm = z.infer<typeof schema>;

// Add form methods hook
export const useUpdateLeaveFormMethods = () => {
  const methods = useForm<UpdateLeaveForm>({
    resolver: zodResolver(schema),
    defaultValues: {
      id: '',
      leaveType: undefined,
      user: '',
      startDateTime: new Date(),
      endDateTime: new Date(),
      reason: '',
      leaveStatus: undefined,
      rejectedReason: ''
    }
  });

  return { methods };
};

// Update component props
interface UpdateLeaveFormProps {
  methods: UseFormReturn<UpdateLeaveForm>;
  users: Array<{ id: string; fullname: string }>;
  currentUserId: string;
}

export default function UpdateLeaveForm({
  methods,
  users,
  currentUserId
}: UpdateLeaveFormProps) {
  const { mutateAsync: updateLeave } = useUpdateLeaveMutation();
  const { watch } = methods;
  const leaveStatus = watch('leaveStatus');

  const onSubmit = async (data: UpdateLeaveForm) => {
    const updateLeaveInput = {
      leaveStatus: data.leaveStatus,
      ...(data.leaveStatus === LeaveStatus.Rejected ? {
        rejectedReason: data.rejectedReason
      }: { rejectedReason: '' }),
      ...(data.leaveStatus === LeaveStatus.Approved && {
        approvedBy: currentUserId
      })
    };

    toast.promise(
      updateLeave({
        id: data.id,
        updateLeaveInput
      }),
      {
        loading: 'Updating leave request...',
        success: 'Leave request updated successfully',
        error: 'Failed to update leave request'
      }
    );
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <div className="self-start font-semibold">Leave Type</div>
          <div>
            <FormSelect
              name="leaveType"
              placeholder="Select leave type"
              disabled
            >
              <SelectGroup>
                <SelectLabel>Select Leave Type</SelectLabel>
                {enumToOptions(LeaveType).map(({ value, label }) => (
                  <SelectItem value={value} key={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </FormSelect>
          </div>
        </div>
        <div className="space-y-2">
          <div className="self-start font-semibold">User</div>
          <div>
            <FormSelect name="user" placeholder="Select user" disabled>
              <SelectGroup>
                <SelectLabel>Select User</SelectLabel>
                {users.map(user => (
                  <SelectItem value={user.id} key={user.id}>
                    {user.fullname}
                  </SelectItem>
                ))}
              </SelectGroup>
            </FormSelect>
          </div>
        </div>
        <FormDatePicker name="startDateTime" label="Start Date" disabled />
        <FormDatePicker name="endDateTime" label="End Date" disabled />
        <FormTextArea
          name="reason"
          label="Reason"
          placeholder="Enter reason for leave"
          className="min-h-24"
          disabled
        />
        {/* Status field remains editable */}
        <div className="space-y-2">
          <div className="self-start font-semibold">Status</div>
          <div>
            <FormSelect name="leaveStatus" placeholder="Select status">
              <SelectGroup>
                <SelectLabel>Select Status</SelectLabel>
                {enumToOptions(LeaveStatus).map(({ value, label }) => (
                  <SelectItem value={value} key={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </FormSelect>
          </div>
        </div>
        {/* Rejection reason field appears when status is Rejected */}
        {leaveStatus === LeaveStatus.Rejected && (
          <FormTextArea
            name="rejectedReason"
            label="Rejection Reason"
            placeholder="Enter reason for rejection"
            className="min-h-24"
          />
        )}
        <Button type="submit">Update</Button>
      </form>
    </FormProvider>
  );
}

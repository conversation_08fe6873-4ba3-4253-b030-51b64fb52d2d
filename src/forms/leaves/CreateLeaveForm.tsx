import { zodResolver } from '@hookform/resolvers/zod';
import { FormProvider, useForm } from 'react-hook-form';
import { z } from 'zod';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import FormTextArea from '@/components/forms/FormTextArea';
import { FormDatePicker } from '@/components/date-picker';
import FormSelect from '@/components/forms/FormSelect';
import { SelectGroup, SelectItem, SelectLabel } from "@/components/ui/select";
import { useCreateLeaveMutation, LeaveType } from '@/generated/graphql';
import { enumToOptions } from '@/lib/utils';

const schema = z.object({
  leaveType: z.nativeEnum(LeaveType, {
    errorMap: () => ({ message: 'Leave type is required' })
  }),
  user: z.string().nonempty('User is required'),
  startDateTime: z.date({
    required_error: 'Start date is required',
  }),
  endDateTime: z.date({
    required_error: 'End date is required',
  }),
  reason: z.string().nonempty('Reason is required'),
});

type CreateLeaveForm = z.infer<typeof schema>;

export const useCreateLeaveForm = () => {
  const methods = useForm<CreateLeaveForm>({
    resolver: zodResolver(schema),
    defaultValues: {
      leaveType: undefined,
      user: '',
      reason: '',
    }
  });

  return { methods };
};

interface CreateLeaveFormProps {
  users: Array<{ id: string; fullname: string }>;
}

export default function CreateLeaveForm({ users }: CreateLeaveFormProps) {
  const { methods } = useCreateLeaveForm();
  const { mutateAsync: createLeave } = useCreateLeaveMutation();

  const onSubmit = async (data: CreateLeaveForm) => {
    toast.promise(createLeave({ createLeaveInput: data }), {
      loading: 'Submitting leave request...',
      success: 'Leave request submitted successfully',
      error: 'Failed to submit leave request'
    });
    methods.reset();
  };

  return (
    <FormProvider {...methods}>
      <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
        <div className="space-y-2">
          <div className="self-start font-semibold">Leave Type</div>
          <div>
            <FormSelect name="leaveType" placeholder="Select leave type">
              <SelectGroup>
                <SelectLabel>Select Leave Type</SelectLabel>
                {enumToOptions(LeaveType).map(({ value, label }) => (
                  <SelectItem value={value} key={value}>
                    {label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </FormSelect>
          </div>
        </div>
        <div className="space-y-2">
          <div className="self-start font-semibold">User</div>
          <div>
            <FormSelect name="user" placeholder="Select user">
              <SelectGroup>
                <SelectLabel>Select User</SelectLabel>
                {users.map((user) => (
                  <SelectItem value={user.id} key={user.id}>
                    {user.fullname}
                  </SelectItem>
                ))}
              </SelectGroup>
            </FormSelect>
          </div>
        </div>
        <FormDatePicker
          name="startDateTime"
          label="Start Date"
        />
        <FormDatePicker
          name="endDateTime"
          label="End Date"
        />
        <FormTextArea
          name="reason"
          label="Reason"
          placeholder="Enter reason for leave"
          className="min-h-24"
        />
        <Button type="submit">Submit</Button>
      </form>
    </FormProvider>
  );
}
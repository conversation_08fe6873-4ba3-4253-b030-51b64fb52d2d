import {
  dateTimeSchema,
  dateTimeToDate,
  FormAppointmentPicker
} from '@/components/appointment-picker';
import { FormDatePicker } from '@/components/date-picker';
import FormSelect from '@/components/forms/FormSelect';
import { Button } from '@/components/ui/button';
import { SelectGroup, SelectItem, SelectLabel } from '@/components/ui/select';
import { errorHandler } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import { useEffect } from 'react';
import {
  FormProvider,
  SubmitHandler,
  useForm,
  UseFormReturn
} from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { useCreateAttendanceMutation } from '@/generated/graphql';

// Define or import the Id type
type Id<T extends string> = string & { __type: T };

const schema = z.object({
  shiftId: z.string(),
  userId: z.string(),
  date: z.date(),
  startTime: dateTimeSchema,
  endTime: dateTimeSchema,
  overtimeStartTime: dateTimeSchema.optional(),
  locationId: z.string()
});

type CreateAttendanceForm = z.infer<typeof schema>;

export type CreateAttendanceFormProps = {
  onSubmit: SubmitHandler<CreateAttendanceForm>;
  methods: UseFormReturn<CreateAttendanceForm>;
  guards?: Array<{ id: string; fullname: string }>;
  date?: Date;
};

export const useCreateAttendanceFormMethods = () => {
  const { mutateAsync: createAttendance } = useCreateAttendanceMutation();

  const methods = useForm<CreateAttendanceForm>({
    defaultValues: {
      shiftId: '',
      userId: '',
      date: new Date(),
      startTime: { date: new Date(), time: '09:00' },
      endTime: { date: new Date(), time: '17:00' },
      overtimeStartTime: undefined,
      locationId: ''
    },
    resolver: zodResolver(schema)
  });

  const onSubmit: SubmitHandler<CreateAttendanceForm> = async data => {
    try {
      await createAttendance({
        input: {
          date: dateTimeToDate({ date: data.date, time: '00:00' }),
          startTime: dateTimeToDate(data.startTime),
          endTime: dateTimeToDate(data.endTime),
          overTime: data.overtimeStartTime
            ? dateTimeToDate(data.overtimeStartTime)
            : new Date(0), // Provide a default Date value if undefined
          locationId: data.locationId as Id<'locations'>,
          shiftId: data.shiftId as Id<'shifts'>,
          userId: data.userId as Id<'users'>
        }
      });

      toast.success('Attendance created successfully');
      methods.reset();
    } catch (ex) {
      errorHandler(ex);
    }
  };

  return { methods, onSubmit };
};

export default function CreateAttendanceForm({
  methods,
  onSubmit,
  guards,
  date
}: CreateAttendanceFormProps) {
  useEffect(() => {
    if (date) {
      methods.setValue('date', date);
      methods.setValue('startTime', { date, time: '09:00' });
      methods.setValue('endTime', { date, time: '17:00' });
    }
  }, [date]);

  return (
    <div>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
          <FormSelect name="userId" label="Guards" placeholder="Select guards">
            <SelectGroup>
              <SelectLabel>Select Guard</SelectLabel>
              {guards?.map(sg => {
                if (!sg) return null;

                return (
                  <SelectItem value={sg.id} key={sg.id}>
                    {sg.fullname}
                  </SelectItem>
                );
              })}
            </SelectGroup>
          </FormSelect>
          <FormDatePicker name="date" label="Date" />
          <div className="flex items-center gap-4">
            <FormAppointmentPicker name="startTime" label="Start Time" />
            <FormAppointmentPicker name="endTime" label="End Time" />
          </div>
          <FormAppointmentPicker
            name="overtimeStartTime"
            label="Overtime Start Time"
          />
          <Button type="submit">Create Attendance</Button>
        </form>
      </FormProvider>
    </div>
  );
}

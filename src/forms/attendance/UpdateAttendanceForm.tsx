import {
  dateTimeSchema,
  dateTimeToDate,
  FormAppointmentPicker
} from '@/components/appointment-picker';
import { FormDatePicker } from '@/components/date-picker';
import FormSelect from '@/components/forms/FormSelect';
import { Button } from '@/components/ui/button';
import { SelectGroup, SelectItem, SelectLabel } from '@/components/ui/select';
import { errorHandler } from '@/lib/utils';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  FormProvider,
  SubmitHandler,
  useForm,
  UseFormReturn
} from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';
import { useUpdateAttendanceMutation } from '@/generated/graphql';
import { Skeleton } from '@/components/ui/skeleton';

// Define or import the Id type
type Id<T extends string> = string & { __type: T };

const schema = z.object({
  attendanceId: z.string(),
  userId: z.string(),
  date: z.date(),
  startTime: dateTimeSchema,
  endTime: dateTimeSchema,
  overtimeStartTime: dateTimeSchema.optional(),
  shiftId: z.string(),
  locationId: z.string()
});

type UpdateAttendanceForm = z.infer<typeof schema>;

export type UpdateAttendanceFormProps = {
  onSubmit: SubmitHandler<UpdateAttendanceForm>;
  methods: UseFormReturn<UpdateAttendanceForm>;
  guards?: Array<{ id: string; fullname: string }>;
};

export const useUpdateAttendanceFormMethods = () => {
  const { mutateAsync: updateAttendance } = useUpdateAttendanceMutation();

  const methods = useForm<UpdateAttendanceForm>({
    defaultValues: {
      attendanceId: '',
      userId: '',
      date: new Date(),
      startTime: { date: new Date(), time: '09:00' },
      endTime: { date: new Date(), time: '17:00' },
      overtimeStartTime: undefined,
      shiftId: '',
      locationId: ''
    },
    resolver: zodResolver(schema)
  });

  const onSubmit: SubmitHandler<UpdateAttendanceForm> = async data => {
    try {
      await updateAttendance({
        input: {
          id: data.attendanceId as Id<'attendances'>,
          date: dateTimeToDate({ date: data.date, time: '00:00' }),
          startTime: dateTimeToDate(data.startTime),
          endTime: dateTimeToDate(data.endTime),
          overTime: data.overtimeStartTime
            ? dateTimeToDate(data.overtimeStartTime)
            : new Date(0), // Provide a default Date value if undefined
          userId: data.userId as Id<'users'>,
          locationId: data.locationId as Id<'locations'>,
          shiftId: data.shiftId as Id<'shifts'>
        }
      });

      toast.success('Attendance updated successfully');
    } catch (ex) {
      errorHandler(ex);
    }
  };

  return { methods, onSubmit };
};

// Add this skeleton loader component
function UpdateAttendanceSkeleton() {
  return (
    <div className="space-y-4">
      {/* Guard Select Skeleton */}
      <div className="space-y-2">
        <Skeleton className="h-4 w-20" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Date Picker Skeleton */}
      <div className="space-y-2">
        <Skeleton className="h-4 w-16" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Time Pickers Skeleton */}
      <div className="flex items-center gap-4">
        <div className="space-y-2 flex-1">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-10 w-full" />
        </div>
        <div className="space-y-2 flex-1">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-10 w-full" />
        </div>
      </div>

      {/* Overtime Picker Skeleton */}
      <div className="space-y-2">
        <Skeleton className="h-4 w-36" />
        <Skeleton className="h-10 w-full" />
      </div>

      {/* Submit Button Skeleton */}
      <Skeleton className="h-10 w-full" />
    </div>
  );
}

// Update the loading check in the main component
export default function UpdateAttendanceForm({
  methods,
  onSubmit,
  guards
}: UpdateAttendanceFormProps) {
  if (!guards?.length) {
    return <UpdateAttendanceSkeleton />;
  }

  return (
    <div>
      <FormProvider {...methods}>
        <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-4">
          <FormSelect name="userId" label="Guards" placeholder="Select guards">
            <SelectGroup>
              <SelectLabel>Select Guard</SelectLabel>
              {guards?.map(sg => {
                if (!sg) return null;

                return (
                  <SelectItem value={sg.id} key={sg.id}>
                    {sg.fullname}
                  </SelectItem>
                );
              })}
            </SelectGroup>
          </FormSelect>
          <FormDatePicker name="date" label="Date" />
          <div className="flex items-center gap-4">
            <FormAppointmentPicker name="startTime" label="Start Time" />
            <FormAppointmentPicker name="endTime" label="End Time" />
          </div>
          <FormAppointmentPicker
            name="overtimeStartTime"
            label="Overtime Start Time"
          />
          <Button type="submit">Update Attendance</Button>
        </form>
      </FormProvider>
    </div>
  );
}

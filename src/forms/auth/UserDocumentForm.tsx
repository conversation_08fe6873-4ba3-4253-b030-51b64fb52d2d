import { z } from 'zod';
import { Form<PERSON>rovider, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Table,
  TableBody,
  TableCell,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Download, Trash2 } from 'lucide-react';
import { FormPresignedUpload } from '@/components/forms/FormPresignedUpload';
import { toast } from 'sonner';
import {
  useCreateUserDocumentMutation,
  useDeleteUserDocumentMutation,
  useUserDocumentsQuery
} from '@/generated/graphql';
import FormField from '@/components/forms/FormField';

const formSchema = z.object({
  documentName: z.string().min(1, 'Document name is required'),
  url: z.string().url('Valid URL is required'),
  user: z.string()
});

type FormValues = z.infer<typeof formSchema>;

interface UserDocumentFormProps {
  userId: string;
}

export function UserDocumentForm({ userId }: UserDocumentFormProps) {
  const { data: documents, refetch } = useUserDocumentsQuery({ userDocumentFilterInput: { user: userId } });
  console.log(documents)

  const { mutateAsync: createDocument } = useCreateUserDocumentMutation();
  const { mutateAsync: deleteDocument } = useDeleteUserDocumentMutation();

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      user: userId,
      documentName: '',
      url: ''
    }
  });

  const onSubmit = async (data: FormValues) => {
    try {
      await createDocument({
        input: {
          user: userId,
          documentName: data.documentName,
          url: data.url
        }
      });

      toast.success('Document uploaded successfully');
      form.reset();
      refetch();
    } catch (error) {
      toast.error('Failed to upload document');
      console.error('Upload error:', error);
    }
  };

  const handleDelete = async (documentId: string) => {
    try {
      await deleteDocument({ documentId });
      toast.success('Document deleted successfully');
      refetch();
    } catch (error) {
      toast.error('Failed to delete document');
      console.error('Delete error:', error);
    }
  };

  const handleDownload = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <Dialog>
      <DialogTrigger>
        <Button>Documents</Button>
      </DialogTrigger>

      <DialogContent className="min-w-[60vw]">
        <DialogHeader>
          <DialogTitle>Manage Documents</DialogTitle>
        </DialogHeader>

        <div className="flex gap-4">
          <ScrollArea className="h-[70vh] flex-1 border-r">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Download</TableCell>
                  <TableCell>Delete</TableCell>
                </TableRow>
              </TableHeader>
              <TableBody>
                {documents?.userDocuments?.map((doc) => (
                  <TableRow key={doc.id}>
                    <TableCell>{doc.documentName}</TableCell>
                    <TableCell>
                      <Download
                        className="cursor-pointer"
                        onClick={() => handleDownload(doc.url)}
                      />
                    </TableCell>
                    <TableCell>
                      <Trash2
                        className="cursor-pointer text-destructive"
                        onClick={() => handleDelete(doc.id)}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>

          <FormProvider {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="flex-1 space-y-6"
            >
              <FormField
                name="documentName"
                label="Document Name"
                placeholder="Enter Document Name"
              />

              <FormPresignedUpload
                name="url"
                label="Upload Document"
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                multiple={false}
              />

              <Button type="submit" className="w-full">
                Add Document
              </Button>
            </form>
          </FormProvider>
        </div>
      </DialogContent>
    </Dialog>
  );
}

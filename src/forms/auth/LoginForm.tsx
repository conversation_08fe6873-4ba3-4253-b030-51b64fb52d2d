import FormField from '@/components/forms/FormField';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { data } from '@/lib/data';
import { cn } from '@/lib/utils';
import { FormProvider, UseFormReturn } from 'react-hook-form';
import { createFileRoute } from '@tanstack/react-router';
import * as z from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useSigninMutation } from '@/generated/graphql';
import { useAtom } from 'jotai';
import { tokenAtom } from '@/hooks/useAuth';

const schema = z.object({
  phone: z.string().nonempty({ message: 'Phone is required' }),
  password: z.string().nonempty({ message: 'Password is required' })
});

type LoginForm = z.infer<typeof schema>;

export default function LoginForm() {
  const { mutateAsync: signIn, isPending } = useSigninMutation();
  const [, setToken] = useAtom(tokenAtom);

  const methods = useForm<LoginForm>({
    defaultValues: {
      phone: '',
      password: ''
    },
    resolver: zodResolver(schema)
  });

  const handleSubmit = methods.handleSubmit(async data => {
    try {
      const {
        signIn: { access_token }
      } = await signIn({
        input: { phone: data.phone, password: data.password }
      });
      setToken(access_token);
    } catch (error) {
      console.error(error);
    }
  });

  return (
    <div className="grid min-h-svh lg:grid-cols-2">
      <div className="flex flex-col gap-4 p-6 md:p-10">
        <div className="flex justify-center gap-2 md:justify-start">LOGO</div>
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-xs">
            <form className={cn('flex flex-col gap-6')} onSubmit={handleSubmit}>
              <FormProvider {...methods}>
                <div className="flex flex-col items-center gap-2 text-center">
                  <h1 className="text-2xl font-bold">Login to your account</h1>
                  <p className="text-balance text-sm text-muted-foreground">
                    Enter your phone number below to login to your account
                  </p>
                </div>
                <div className="grid gap-6">
                  <div className="grid gap-2" onSubmit={handleSubmit}>
                    <Label htmlFor="phone">Phone</Label>
                    <FormField
                      type="tel"
                      placeholder="Enter your phone number"
                      name="phone"
                    />
                  </div>
                  <div className="grid gap-2">
                    <div className="flex items-center">
                      <Label htmlFor="password">Password</Label>
                      <a
                        href="#"
                        className="ml-auto text-sm underline-offset-4 hover:underline"
                      >
                        Forgot your password?
                      </a>
                    </div>
                    <FormField
                      type="password"
                      placeholder="Enter your password"
                      name="password"
                    />
                  </div>
                  <Button type="submit" className="w-full" effect="shineHover">
                    Login
                  </Button>
                </div>
                <div className="text-center text-sm">
                  Don&apos;t have an account?{' '}
                  <a href="#" className="underline underline-offset-4">
                    Sign up
                  </a>
                </div>
              </FormProvider>
            </form>
          </div>
        </div>
      </div>
      <div className="relative hidden bg-muted lg:block">
        <img
          src={data.placeholderImage}
          alt="Image"
          className="absolute inset-0 h-full w-full object-cover dark:brightness-[0.2] dark:grayscale"
        />
      </div>
    </div>
  );
}

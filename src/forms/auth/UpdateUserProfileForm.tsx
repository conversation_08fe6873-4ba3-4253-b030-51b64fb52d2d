import { z } from 'zod';
import { use<PERSON><PERSON>, FormProvider, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import FormField from '@/components/forms/FormField';
import { FormDatePicker } from '@/components/date-picker';
import FormSelect from '@/components/forms/FormSelect';
import { useUpdateUserProfileMutation } from '@/generated/graphql';
import { toast } from 'sonner';
import { SelectGroup, SelectItem, SelectLabel } from '@/components/ui/select';
import { Trash2, Plus } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';

const contactSchema = z.object({
  countryCode: z.string().default('+60'),
  phone: z.string().min(1, 'Phone number is required')
});

const emergencyContactSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  relation: z.string().min(1, 'Relation is required'),
  contact: contactSchema
});

const userProfileSchema = z.object({
  ic: z.string().optional(),
  ID: z.string().optional(),
  passport: z.string().optional(),
  passportExpiresAt: z.date().optional(),
  permitNumber: z.string().optional(),
  permitExpiresAt: z.date().optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
  dob: z.date().optional(),
  placeOfBirth: z.string().optional(),
  currentAddress: z.string().optional(),
  joinedAt: z.date().optional(),
  maritalStatus: z
    .enum(['single', 'married', 'divorced', 'widowed'])
    .optional(),
  bankAccNumber: z.string().optional(),
  bankName: z.string().optional(),
  emergencyContact: z.array(emergencyContactSchema).optional()
});

type UserProfileFormValues = z.infer<typeof userProfileSchema>;

interface UserProfileFormProps {
  userId: string;
  initialData?: {
    ic?: string;
    ID?: string;
    passport?: string;
    passportExpiresAt?: Date;
    permitNumber?: string;
    permitExpiresAt?: Date;
    gender?: 'male' | 'female' | 'other';
    dob?: Date;
    placeOfBirth?: string;
    currentAddress?: string;
    joinedAt?: Date;
    maritalStatus?: 'single' | 'married' | 'divorced' | 'widowed';
    bankAccNumber?: string;
    bankName?: string;
    emergencyContact?: Array<{
      name: string;
      relation: string;
      contact: {
        countryCode: string;
        phone: string;
      };
    }>;
  };
}

// Add export to the hook
export const useUpdateUserProfileFormMethods = (
  initialData?: UserProfileFormProps['initialData']
) => {
  return useForm<UserProfileFormValues>({
    resolver: zodResolver(userProfileSchema),
    defaultValues: {
      ic: initialData?.ic ?? '',
      ID: initialData?.ID ?? '',
      passport: initialData?.passport ?? '',
      passportExpiresAt: initialData?.passportExpiresAt,
      permitNumber: initialData?.permitNumber ?? '',
      permitExpiresAt: initialData?.permitExpiresAt,
      gender: initialData?.gender,
      dob: initialData?.dob,
      placeOfBirth: initialData?.placeOfBirth ?? '',
      currentAddress: initialData?.currentAddress ?? '',
      joinedAt: initialData?.joinedAt,
      maritalStatus: initialData?.maritalStatus,
      bankAccNumber: initialData?.bankAccNumber ?? '',
      bankName: initialData?.bankName ?? '',
      emergencyContact: initialData?.emergencyContact ?? []
    }
  });
};

// Update the component to use the hook
export default function UserProfileForm({
  userId,
  initialData
}: UserProfileFormProps) {
  const methods = useUpdateUserProfileFormMethods(initialData);
  const { mutateAsync: updateUserProfile } = useUpdateUserProfileMutation();
  const { fields, append, remove } = useFieldArray({
    control: methods.control,
    name: 'emergencyContact'
  });

  const onSubmit = async (data: UserProfileFormValues) => {
    try {
      await toast.promise(
        updateUserProfile({
          userId,
          updateUserProfileInput: {
            ...data,
            passportExpiresAt: data?.passportExpiresAt ?? undefined,
            permitExpiresAt: data?.permitExpiresAt ?? undefined,
            dob: data?.dob ?? undefined,
            joinedAt: data?.joinedAt ?? undefined
          }
        }),
        {
          // loading: 'Updating profile...',
          success: 'Profile updated successfully',
          error: 'Failed to update profile'
        }
      );
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Personal Information</CardTitle>
      </CardHeader>
      <CardContent>
        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                name="ic"
                label="IC Number"
                placeholder="Enter IC number"
              />

              <FormField
                name="ID"
                label="ID Number"
                placeholder="Enter ID number"
              />

              <FormField
                name="passport"
                label="Passport Number"
                placeholder="Enter passport number"
              />

              <FormDatePicker
                name="passportExpiresAt"
                label="Passport Expiry Date"
              />

              <FormField
                name="permitNumber"
                label="Permit Number"
                placeholder="Enter permit number"
              />

              <FormDatePicker
                name="permitExpiresAt"
                label="Permit Expiry Date"
              />

              <FormSelect
                name="gender"
                label="Gender"
                placeholder="Select gender"
              >
                <SelectGroup>
                  <SelectLabel>Select Gender</SelectLabel>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectGroup>
              </FormSelect>

              <FormDatePicker name="dob" label="Date of Birth" />

              <FormField
                name="placeOfBirth"
                label="Place of Birth"
                placeholder="Enter place of birth"
              />

              <FormField
                name="currentAddress"
                label="Current Address"
                placeholder="Enter current address"
              />

              <FormDatePicker name="joinedAt" label="Joined Date" />

              <FormSelect
                name="maritalStatus"
                label="Marital Status"
                placeholder="Select marital status"
              >
                <SelectGroup>
                  <SelectLabel>Select Marital Status</SelectLabel>
                  <SelectItem value="single">Single</SelectItem>
                  <SelectItem value="married">Married</SelectItem>
                  <SelectItem value="divorced">Divorced</SelectItem>
                  <SelectItem value="widowed">Widowed</SelectItem>
                </SelectGroup>
              </FormSelect>

              <FormField
                name="bankAccNumber"
                label="Bank Account Number"
                placeholder="Enter bank account number"
              />

              <FormField
                name="bankName"
                label="Bank Name"
                placeholder="Enter bank name"
              />
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Emergency Contacts</h3>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    append({
                      name: '',
                      relation: '',
                      contact: {
                        countryCode: '+60',
                        phone: ''
                      }
                    })
                  }
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Contact
                </Button>
              </div>

              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Relation</TableHead>
                      <TableHead>Contact Number</TableHead>
                      <TableHead className="w-[70px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {fields.length === 0 ? (
                      <TableRow>
                        <TableCell
                          colSpan={4}
                          className="text-center text-muted-foreground"
                        >
                          No emergency contacts added
                        </TableCell>
                      </TableRow>
                    ) : (
                      fields.map((field, index) => (
                        <TableRow key={field.id}>
                          <TableCell>
                            <FormField
                              name={`emergencyContact.${index}.name`}
                              placeholder="Enter contact name"
                            />
                          </TableCell>
                          <TableCell>
                            <FormField
                              name={`emergencyContact.${index}.relation`}
                              placeholder="Enter relation"
                            />
                          </TableCell>
                          <TableCell>
                            <div className="flex gap-2">
                              <FormSelect
                                name={`emergencyContact.${index}.contact.countryCode`}
                                className="w-24"
                              >
                                <SelectGroup>
                                  <SelectItem value="+60">+60</SelectItem>
                                </SelectGroup>
                              </FormSelect>
                              <FormField
                                name={`emergencyContact.${index}.contact.phone`}
                                placeholder="Phone number"
                                className="flex-1"
                              />
                            </div>
                          </TableCell>
                          <TableCell>
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              onClick={() => remove(index)}
                              className="h-8 w-8 text-destructive"
                            >
                              <Trash2 className="h-4 w-4" />
                              <span className="sr-only">Remove contact</span>
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>

            <div className="mt-6">
              <Button type="submit" className="w-full">
                Update Profile
              </Button>
            </div>
          </form>
        </FormProvider>
      </CardContent>
    </Card>
  );
}

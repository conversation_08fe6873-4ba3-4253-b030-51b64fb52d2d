import { Field, Float, InputType } from '@nestjs/graphql';
import { IsMongoId, IsNotEmpty, IsOptional, IsString } from 'class-validator';

@InputType()
export class CreateCheckpointInput {
  @Field(() => String)
  @IsString()
  @IsNotEmpty()
  name: string;

  @Field(() => String)
  @IsMongoId()
  @IsNotEmpty()
  location: string;

  @Field(() => [Float], { nullable: true })
  @IsOptional()
  locationCoordinates?: [number, number];
}

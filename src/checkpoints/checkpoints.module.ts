import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Checkpoint, CheckpointSchema } from './entities/checkpoint.entity';
import { Location, LocationSchema } from '../location/entities/location.entity';
import { CheckpointsService } from './checkpoints.service';
import { CheckpointsResolver } from './checkpoints.resolver';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Checkpoint.name, schema: CheckpointSchema },
      { name: Location.name, schema: LocationSchema },
    ]),
  ],
  providers: [CheckpointsService, CheckpointsResolver],
  exports: [CheckpointsService],
})
export class CheckpointsModule {}

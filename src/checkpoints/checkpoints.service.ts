import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { FilterQuery, Model } from 'mongoose';
import { Checkpoint } from './entities/checkpoint.entity';
import { Location } from 'src/location/entities/location.entity';
import { CreateCheckpointInput } from './dto/create-checkpoint.input';
import { UpdateCheckpointInput } from './dto/update-checkpoint.input';

@Injectable()
export class CheckpointsService {
  constructor(
    @InjectModel(Checkpoint.name) private checkpoint: Model<Checkpoint>,
    @InjectModel(Location.name) private location: Model<Location>,
  ) {}

  async create(createCheckpointInput: CreateCheckpointInput) {
    // Check if checkpoint with same name exists in the location
    const existingCheckpoint = await this.checkpoint.findOne({
      name: createCheckpointInput.name,
      location: createCheckpointInput.location,
    });

    if (existingCheckpoint) {
      throw new Error(
        'Checkpoint with this name already exists in this location',
      );
    }

    const checkpoint = await this.checkpoint.create(createCheckpointInput);

    // Update location's checkpoints array if not already present
    await this.location.findByIdAndUpdate(
      createCheckpointInput.location,
      {
        $addToSet: { checkpoints: checkpoint._id }, // $addToSet ensures no duplicates
      },
      { new: true },
    );

    return checkpoint;
  }

  findAll(filter: FilterQuery<Checkpoint> = {}) {
    return this.checkpoint.find(filter);
  }

  findOne(id: string) {
    return this.checkpoint.findById(id);
  }

  update(id: string, updateCheckpointInput: UpdateCheckpointInput) {
    return this.checkpoint.findByIdAndUpdate(id, updateCheckpointInput, {
      new: true,
    });
  }

  async softDelete(id: string) {
    return this.checkpoint.findByIdAndUpdate(
      id,
      {
        isDeleted: true,
        deletedAt: new Date(),
      },
      { new: true },
    );
  }
}

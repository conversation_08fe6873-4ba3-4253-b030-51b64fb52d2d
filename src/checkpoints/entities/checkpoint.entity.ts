import { Field, ObjectType, Float } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';
import { MongooseSchema } from 'src/common/common.entity';
import { Location } from 'src/location/entities/location.entity';

@ObjectType()
@Schema()
export class Checkpoint extends MongooseSchema {
  @Field(() => String)
  @Prop({ required: true })
  name: string;

  @Field(() => Location)
  @Prop({ required: true, type: mongoose.Schema.Types.ObjectId })
  location: string;

  @Field(() => [Float], { nullable: true })
  @Prop({ type: [Number], index: '2dsphere' }) // [latitude, longitude]
  locationCoordinates?: [number, number];

  @Field(() => Boolean)
  @Prop({ default: false })
  isDeleted: boolean;

  @Field(() => Date, { nullable: true })
  @Prop()
  deletedAt?: Date;
}

export const CheckpointSchema = SchemaFactory.createForClass(Checkpoint);

// Add a default query to exclude soft deleted records
CheckpointSchema.pre('find', function () {
  this.where({ isDeleted: { $ne: true } });
});

CheckpointSchema.pre('findOne', function () {
  this.where({ isDeleted: { $ne: true } });
});

CheckpointSchema.pre('findOneAndUpdate', function () {
  this.where({ isDeleted: { $ne: true } });
});

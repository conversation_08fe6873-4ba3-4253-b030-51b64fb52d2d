import {
  Resolver,
  Query,
  Mutation,
  Args,
  Parent,
  ResolveField,
  Context,
} from '@nestjs/graphql';
import { CheckpointsService } from './checkpoints.service';
import { Checkpoint } from './entities/checkpoint.entity';
import { CreateCheckpointInput } from './dto/create-checkpoint.input';
import { UpdateCheckpointInput } from './dto/update-checkpoint.input';
import { FindCheckpointsInput } from './dto/find-checkpoint.input';
import { GqlContext } from 'src/app.module';
import { Location } from 'src/location/entities/location.entity';

@Resolver(() => Checkpoint)
export class CheckpointsResolver {
  constructor(private readonly checkpointsService: CheckpointsService) {}

  @ResolveField(() => Location, { name: 'location' })
  getLocation(
    @Parent() checkpoint: Checkpoint,
    @Context() context: GqlContext,
  ) {
    return context.loaders.locationLoader.load(checkpoint.location);
  }

  @Mutation(() => Checkpoint)
  createCheckpoint(
    @Args('createCheckpointInput') createCheckpointInput: CreateCheckpointInput,
  ) {
    return this.checkpointsService.create(createCheckpointInput);
  }

  @Query(() => [Checkpoint], { name: 'checkpoints' })
  findAll(
    @Args('filter', { type: () => FindCheckpointsInput, nullable: true })
    filter?: FindCheckpointsInput,
  ) {
    return this.checkpointsService.findAll(filter);
  }

  @Query(() => Checkpoint, { name: 'checkpoint' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.checkpointsService.findOne(id);
  }

  @Mutation(() => Checkpoint)
  updateCheckpoint(
    @Args('id', { type: () => String }) id: string,
    @Args('updateCheckpointInput') updateCheckpointInput: UpdateCheckpointInput,
  ) {
    return this.checkpointsService.update(id, updateCheckpointInput);
  }

  @Mutation(() => Checkpoint)
  removeCheckpoint(@Args('id', { type: () => String }) id: string) {
    return this.checkpointsService.softDelete(id);
  }
}

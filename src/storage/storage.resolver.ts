import { Args, Mutation, Query, Resolver } from '@nestjs/graphql';
import {
  Storage,
  StorageItemSource,
  StorageItemType,
} from './entities/storage.entity';
import { StorageService } from './storage.service';
import { CreateFolderDto } from './dto/create-folder.dto';

@Resolver(() => Storage)
export class StorageResolver {
  constructor(private readonly storageService: StorageService) {}

  @Query(() => [Storage])
  storageItems(
    @Args('parentId', { type: () => String, nullable: true }) parentId?: string,
  ) {
    return this.storageService.findMany({
      type: StorageItemType.FILE,
      parent: parentId || null,
    });
  }

  @Query(() => [Storage])
  systemFiles() {
    return this.storageService.findMany({ source: StorageItemSource.SYSTEM });
  }

  @Query(() => [Storage])
  storageFolders(
    @Args('parentId', { type: () => String, nullable: true }) parentId?: string,
  ) {
    return this.storageService.findMany({
      type: StorageItemType.FOLDER,
      parent: parentId || null,
    });
  }

  @Mutation(() => Storage)
  async createFolder(
    @Args('createFolderInput') createFolderInput: CreateFolderDto,
  ) {
    return this.storageService.createFolder(createFolderInput);
  }
}

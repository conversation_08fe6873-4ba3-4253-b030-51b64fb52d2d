import { Field, ObjectType, registerEnumType } from '@nestjs/graphql';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { IsMongoId } from 'class-validator';
import mongoose from 'mongoose';
import { MongooseSchema } from 'src/common/common.entity';

export enum StorageSystemFolders {
  LOCATIONS = 'LOCATIONS',
  BIN = 'BIN',
}

export enum StorageItemType {
  FILE = 'FILE',
  FOLDER = 'FOLDER',
}

export enum StorageItemSource {
  USER = 'USER',
  SYSTEM = 'SYSTEM',
}

registerEnumType(StorageItemType, { name: 'StorageItemType' });
registerEnumType(StorageItemSource, { name: 'StorageItemSource' });
registerEnumType(StorageSystemFolders, { name: 'StorageSystemFolders' });

@ObjectType()
@Schema()
export class Storage extends MongooseSchema {
  @Field()
  @Prop({ required: true })
  name: string;

  @Field(() => StorageItemType)
  @Prop({ required: true, type: String, enum: StorageItemType })
  type: StorageItemType;

  @Field(() => Storage, { nullable: true })
  @Prop({
    required: false,
    type: mongoose.Schema.Types.ObjectId,
    default: null,
  })
  @IsMongoId()
  parent?: string;

  @Field(() => Number)
  @Prop({ default: 0 })
  size: number;

  @Field({ nullable: true })
  @Prop()
  path?: string;

  @Field(() => StorageItemSource)
  @Prop({
    required: true,
    type: String,
    enum: StorageItemSource,
    default: StorageItemSource.USER,
  })
  source: StorageItemSource;

  @Field({ nullable: true })
  @Prop()
  metadata?: string;
}

export const StorageSchema = SchemaFactory.createForClass(Storage);

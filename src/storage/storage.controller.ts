import {
  Body,
  Controller,
  MaxFileSizeValidator,
  ParseFilePipe,
  Post,
  UploadedFile,
  UseInterceptors,
} from '@nestjs/common';
import { InjectConnection } from '@nestjs/mongoose';
import { FileInterceptor } from '@nestjs/platform-express';
import { Connection } from 'mongoose';
import { CreateFolderDto } from './dto/create-folder.dto';
import { UploadFileDto } from './dto/upload-file.dto';
import { StorageService } from './storage.service';

@Controller('storage')
export class StorageController {
  constructor(
    private storageService: StorageService,
    @InjectConnection() private readonly connection: Connection,
  ) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @Body() uploadFileDto: UploadFileDto,
    @UploadedFile(
      new ParseFilePipe({
        validators: [new MaxFileSizeValidator({ maxSize: 50 * 1024 * 1024 })],
      }),
    )
    file: Express.Multer.File,
  ) {
    const filePath = this.storageService.createUniqueFilename(file);
    const { _id } = await this.storageService.createFile({
      fileName: uploadFileDto.fileName,
      path: filePath,
      size: file.size,
      parentFolderId: uploadFileDto.parentFolderId,
    });
    await this.storageService.saveFileToS3({ file, filePath });

    return {
      success: true,
      message: 'File uploaded successfully',
      filePath,
      fileId: _id.toHexString(),
    };
  }

  @Post('create-folder')
  async createFolder(@Body() createFolderDto: CreateFolderDto) {
    return this.storageService.createFolder(createFolderDto);
  }
}

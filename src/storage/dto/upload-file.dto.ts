import {
  IsMongoId,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON>al,
  IsString,
  Min<PERSON><PERSON>th,
} from 'class-validator';

export class UploadFileDto {
  @IsString()
  @MinLength(1)
  fileName: string;

  @IsOptional()
  @IsString()
  @IsMongoId()
  parentFolderId?: string;
}

export class CreateStorageItem {
  @IsString()
  @MinLength(1)
  fileName: string;

  @IsOptional()
  @IsString()
  @IsMongoId()
  parentFolderId?: string;

  @IsString()
  /**File Path */
  path: string;

  @IsNumber()
  size: number;
}

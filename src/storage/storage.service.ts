import { ConflictException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { v4 as uuid } from 'uuid';
import { FilterQuery, Model } from 'mongoose';
import { CreateFolderDto } from './dto/create-folder.dto';
import { CreateStorageItem } from './dto/upload-file.dto';
import { Storage, StorageItemType } from './entities/storage.entity';

import {
  PutObjectCommand,
  PutObjectCommandInput,
  S3Client,
} from '@aws-sdk/client-s3';
import { ConfigService } from '@nestjs/config';

const S3_REGION = 'ap-southeast-1';
const client = new S3Client({ region: S3_REGION });

@Injectable()
export class StorageService {
  constructor(
    @InjectModel(Storage.name) private storage: Model<Storage>,
    private readonly configService: ConfigService,
  ) {}

  private storageItemExistsInPath(
    storageItemName?: string,
    parentFolderId?: string,
  ) {
    return this.storage.exists({
      parent: parentFolderId || null,
      name: storageItemName,
    });
  }

  createUniqueFilename(file: Express.Multer.File) {
    const uniqueFilename = `${uuid()}-${file.originalname}`;
    return uniqueFilename;
  }

  /** … LOGIC */
  async createFolder({ folder, path, parentFolderId }: CreateFolderDto) {
    // check if folder name exists in the same path
    const folderExists = await this.storageItemExistsInPath(
      folder, // Changed order: folder name first
      parentFolderId, // Changed order: parentFolderId second
    );

    if (folderExists)
      throw new ConflictException('Folder with the same name exists');

    return this.storage.create({
      name: folder,
      parent: parentFolderId || null,
      path,
      size: 0,
      type: StorageItemType.FOLDER,
    });
  }

  async createFile({
    fileName,
    path,
    size,
    parentFolderId,
  }: CreateStorageItem) {
    // check if file name exists in the same path
    const fileExists = await this.storage.findOne({
      parent: parentFolderId || null,
      name: fileName,
      type: StorageItemType.FILE,
    });

    if (fileExists) {
      return fileExists;
    }

    return this.storage.create({
      name: fileName,
      parent: parentFolderId || null,
      path,
      size,
      type: StorageItemType.FILE,
    });
  }

  async saveFileToS3({
    file,
    filePath,
  }: {
    file: Express.Multer.File;
    filePath: string;
  }) {
    const input: PutObjectCommandInput = {
      Bucket: this.configService.get('AWS_BUCKET'),
      Key: filePath,
      Body: file.buffer,
      ACL: 'public-read-write',
    };

    const command = new PutObjectCommand(input);
    await client.send(command);

    return { filePath };
  }

  async findMany(query: FilterQuery<Storage> = {}) {
    return this.storage.find(query);
  }

  getStorageItem(id: string) {
    return this.storage.findOne({ _id: id });
  }
}

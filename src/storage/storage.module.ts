import { Global, Module } from '@nestjs/common';
import { StorageService } from './storage.service';
import { StorageResolver } from './storage.resolver';
import { MongooseModule } from '@nestjs/mongoose';
import { Storage, StorageSchema } from './entities/storage.entity';
import { StorageController } from './storage.controller';
import { ConfigModule } from '@nestjs/config';

@Global()
@Module({
  imports: [
    MongooseModule.forFeature([{ name: Storage.name, schema: StorageSchema }]),
    ConfigModule,
  ],
  providers: [StorageResolver, StorageService],
  controllers: [StorageController],
})
export class StorageModule {}

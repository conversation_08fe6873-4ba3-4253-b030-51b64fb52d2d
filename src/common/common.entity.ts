import { Field, ID, ObjectType } from '@nestjs/graphql';
import { Prop, Schema, Virtual } from '@nestjs/mongoose';
import { Types } from 'mongoose';

@ObjectType()
@Schema({ timestamps: true })
export class MongooseSchema {
  @Field(() => ID)
  _id: Types.ObjectId;

  @Virtual({
    get(this) {
      return this._id.toString();
    },
  })
  @Field(() => ID)
  id: string;

  @Field(() => Date)
  createdAt: Date;

  @Field(() => Date)
  updatedAt: Date;
}

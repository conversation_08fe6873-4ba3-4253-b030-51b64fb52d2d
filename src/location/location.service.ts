import { Injectable } from '@nestjs/common';
import { CreateLocationInput } from './dto/create-location.input';
import { UpdateLocationInput } from './dto/update-location.input';
import { InjectModel } from '@nestjs/mongoose';
import { Location } from './entities/location.entity';
import { FilterQuery, Model } from 'mongoose';

@Injectable()
export class LocationService {
  constructor(@InjectModel(Location.name) private location: Model<Location>) {}
  create(createLocationInput: CreateLocationInput) {
    return this.location.create({ ...createLocationInput, image: '#' });
  }

  findAll(query: FilterQuery<Location> = {}) {
    return this.location.find(query);
  }

  findOne(id: string) {
    return this.location.findById(id);
  }

  update(id: string, updateLocationInput: UpdateLocationInput) {
    return this.location.findByIdAndUpdate(id, updateLocationInput);
  }

  remove(id: string) {
    return this.location.findByIdAndDelete(id);
  }
}

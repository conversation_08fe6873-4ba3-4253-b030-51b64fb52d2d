import { Field, InputType } from '@nestjs/graphql';
import { Type } from 'class-transformer';
import { IsArray, ValidateNested } from 'class-validator';

@InputType()
export class GeoFenceInput {
  @Field(() => [Number])
  @IsArray()
  center: number[];

  @Field(() => [[Number]])
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Number)
  coords: number[][];
}

@InputType()
export class CreateLocationInput {
  @Field(() => String)
  name: string;

  @Field(() => String, { nullable: true })
  description?: string;

  @Field(() => String, { nullable: true })
  address?: string;

  @Field(() => String, { nullable: true })
  emergencyContact?: string;

  @Field(() => GeoFenceInput)
  geofence: GeoFenceInput;
}

import { Args, ID, Mutation, Query, Resolver } from '@nestjs/graphql';
import { DataloaderService } from 'src/dataloader/dataloader.service';
import { CreateLocationInput } from './dto/create-location.input';
import { UpdateLocationInput } from './dto/update-location.input';
import { Location } from './entities/location.entity';
import { LocationService } from './location.service';

@Resolver(() => Location)
export class LocationResolver {
  constructor(
    private readonly locationService: LocationService,
    private readonly loaderService: DataloaderService,
  ) {}

  @Mutation(() => Location)
  createLocation(
    @Args('createLocationInput') createLocationInput: CreateLocationInput,
  ) {
    return this.locationService.create(createLocationInput);
  }

  @Query(() => [Location], { name: 'locations' })
  findAll() {
    return this.locationService.findAll();
  }

  @Query(() => Location, { name: 'location' })
  findOne(@Args('id', { type: () => String }) id: string) {
    return this.locationService.findOne(id);
  }

  @Mutation(() => Location)
  updateLocation(
    @Args('id', { type: () => ID }) id: string,
    @Args('updateLocationInput') updateLocationInput: UpdateLocationInput,
  ) {
    return this.locationService.update(id, updateLocationInput);
  }

  @Mutation(() => Location)
  removeLocation(@Args('id', { type: () => ID }) id: string) {
    return this.locationService.remove(id);
  }
}

import { Field, InputType, Int } from '@nestjs/graphql';
import { IsMongoId, IsOptional, IsNumber, Min } from 'class-validator';
import { DateRangeInput } from 'src/analytics/dto/analytics-filter.input';

@InputType()
export class AttendanceInput {
  @Field(() => String)
  @IsMongoId()
  shiftId?: string;
}

@InputType()
export class AttendanceFilterInput {
  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsMongoId({ each: true })
  locationIds?: string[];

  @Field(() => DateRangeInput, { nullable: true })
  @IsOptional()
  dateRange?: DateRangeInput;

  @Field(() => [String], { nullable: true })
  @IsOptional()
  @IsMongoId({ each: true })
  userIds?: string[];

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsNumber()
  @Min(0)
  timeSpentLte?: number;
}

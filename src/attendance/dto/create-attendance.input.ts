import { Field, InputType } from '@nestjs/graphql';
import { IsDate, IsMongoId, IsNotEmpty } from 'class-validator';

@InputType()
export class CreateAttendanceInput {
  @Field()
  @IsMongoId()
  @IsNotEmpty()
  shiftId: string;

  @Field()
  @IsMongoId()
  @IsNotEmpty()
  userId: string;

  @Field()
  @IsMongoId()
  @IsNotEmpty()
  locationId: string;

  @Field()
  @IsDate()
  date: Date;

  @Field()
  @IsDate()
  startTime: Date;

  @Field()
  @IsDate()
  endTime: Date;

  @Field()
  @IsDate()
  overTime: Date;
}

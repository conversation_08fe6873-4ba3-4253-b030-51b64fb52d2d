import { ObjectType, Field, Int } from '@nestjs/graphql';
import { Prop, Schema } from '@nestjs/mongoose';
import { Types } from 'mongoose';
import { User } from 'src/users/entities/user.entity';

@ObjectType()
@Schema()
export class PaymentCorrection {
  @Field()
  @Prop({ required: true })
  amount: number;

  @Field(() => User)
  @Prop({ required: true, type: Types.ObjectId })
  user: User;

  @Field()
  @Prop({ required: true })
  reason: string;

  @Field()
  @Prop({ required: true, type: Date })
  date: Date;

  @Field(() => User)
  @Prop({ required: true, type: Types.ObjectId })
  correctedBy: User;
}

import { Injectable } from '@nestjs/common';
import { CreatePaymentCorrectionInput } from './dto/create-payment-correction.input';
import { UpdatePaymentCorrectionInput } from './dto/update-payment-correction.input';

@Injectable()
export class PaymentCorrectionsService {
  create(createPaymentCorrectionInput: CreatePaymentCorrectionInput) {
    return 'This action adds a new paymentCorrection';
  }

  findAll() {
    return `This action returns all paymentCorrections`;
  }

  findOne(id: number) {
    return `This action returns a #${id} paymentCorrection`;
  }

  update(id: number, updatePaymentCorrectionInput: UpdatePaymentCorrectionInput) {
    return `This action updates a #${id} paymentCorrection`;
  }

  remove(id: number) {
    return `This action removes a #${id} paymentCorrection`;
  }
}

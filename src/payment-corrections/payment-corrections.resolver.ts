import { Resolver, Query, Mutation, Args, Int } from '@nestjs/graphql';
import { PaymentCorrectionsService } from './payment-corrections.service';
import { PaymentCorrection } from './entities/payment-correction.entity';
import { CreatePaymentCorrectionInput } from './dto/create-payment-correction.input';
import { UpdatePaymentCorrectionInput } from './dto/update-payment-correction.input';

@Resolver(() => PaymentCorrection)
export class PaymentCorrectionsResolver {
  constructor(private readonly paymentCorrectionsService: PaymentCorrectionsService) {}

  @Mutation(() => PaymentCorrection)
  createPaymentCorrection(@Args('createPaymentCorrectionInput') createPaymentCorrectionInput: CreatePaymentCorrectionInput) {
    return this.paymentCorrectionsService.create(createPaymentCorrectionInput);
  }

  @Query(() => [PaymentCorrection], { name: 'paymentCorrections' })
  findAll() {
    return this.paymentCorrectionsService.findAll();
  }

  @Query(() => PaymentCorrection, { name: 'paymentCorrection' })
  findOne(@Args('id', { type: () => Int }) id: number) {
    return this.paymentCorrectionsService.findOne(id);
  }

  @Mutation(() => PaymentCorrection)
  updatePaymentCorrection(@Args('updatePaymentCorrectionInput') updatePaymentCorrectionInput: UpdatePaymentCorrectionInput) {
    return this.paymentCorrectionsService.update(updatePaymentCorrectionInput.id, updatePaymentCorrectionInput);
  }

  @Mutation(() => PaymentCorrection)
  removePaymentCorrection(@Args('id', { type: () => Int }) id: number) {
    return this.paymentCorrectionsService.remove(id);
  }
}

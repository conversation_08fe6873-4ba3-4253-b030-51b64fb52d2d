import { StatusBar } from "@/components/ui/StatusBar";
import { Colors } from "@/constants/Colors";
import {
  Leave,
  LeaveStatus,
  LeaveType,
  useGetLeavesQuery,
} from "@/generated/graphql";
import { enumToOptions } from "@/lib/utils";
import { useSession } from "@/providers/auth-provider";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { useFocusEffect } from "@react-navigation/native";
import React, { useState, useMemo, useCallback } from "react";
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withSpring,
} from "react-native-reanimated";
import { DateRangeFilter } from "@/components/filters/DateRangeFilter";

/**
 * Leave Item Component with animations
 */
interface LeaveItemProps {
  leave: Leave;
  index: number;
  onPress: (leave: any) => void;
}

function LeaveItem({ leave, onPress }: LeaveItemProps) {
  // Handle press
  const handlePress = () => {
    // Call the onPress callback
    onPress(leave);
  };

  // Format date range
  const formatDateRange = (start: Date, end: Date) => {
    const startDate = new Date(start);
    const endDate = new Date(end);

    const options: Intl.DateTimeFormatOptions = {
      month: "short",
      day: "numeric",
    };
    const startFormatted = startDate.toLocaleDateString("en-US", options);

    if (startDate.getTime() === endDate.getTime()) {
      return startFormatted;
    }
    const endFormatted = endDate.toLocaleDateString("en-US", options);
    return `${startFormatted} - ${endFormatted}`;
  };

  // Get status color
  const getStatusColor = (status: LeaveStatus) => {
    switch (status) {
      case LeaveStatus.Approved:
        return Colors.success;
      case LeaveStatus.Rejected:
        return Colors.error;
      case LeaveStatus.Pending:
      default:
        return Colors.warning;
    }
  };

  // Get leave type label
  const getLeaveTypeLabel = (type: LeaveType) => {
    return type === LeaveType.Fullday ? "Full Day" : "Half Day";
  };

  return (
    <View style={styles.leaveItemContainer}>
      <TouchableOpacity
        style={styles.leaveItem}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        <View style={styles.leaveHeader}>
          <Text style={styles.leaveReason}>{leave.reason}</Text>
          <View
            style={[
              styles.statusBadge,
              {
                backgroundColor: getStatusColor(
                  leave.leaveStatus as LeaveStatus
                ),
              },
            ]}
          >
            {<Text style={styles.statusText}>{leave.leaveStatus}</Text>}
          </View>
        </View>

        <View style={styles.leaveDetails}>
          <View style={styles.detailRow}>
            <Ionicons
              name="calendar-outline"
              size={16}
              color={Colors.textSecondary}
            />
            <Text style={styles.detailText}>
              {formatDateRange(leave.startDateTime, leave.endDateTime)}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Ionicons
              name="time-outline"
              size={16}
              color={Colors.textSecondary}
            />
            <Text style={styles.detailText}>
              {getLeaveTypeLabel(leave.leaveType)}
            </Text>
          </View>
        </View>

        {leave.rejectedReason && (
          <View style={styles.rejectionContainer}>
            <Text style={styles.rejectionReason}>
              Reason: {leave.rejectedReason}
            </Text>
          </View>
        )}
      </TouchableOpacity>
    </View>
  );
}

/**
 * Filter Button Component
 */
export interface FilterButtonProps {
  option: { value: string; label: string };
  isActive: boolean;
  onPress: (value: string) => void;
}

export function FilterButton({ option, isActive, onPress }: FilterButtonProps) {
  return (
    <TouchableOpacity
      style={[styles.filterButton, isActive && styles.activeFilterButton]}
      onPress={() => onPress(option.label)}
    >
      <Text
        style={[
          styles.filterButtonText,
          isActive && styles.activeFilterButtonText,
        ]}
      >
        {option.label}
      </Text>
    </TouchableOpacity>
  );
}

/**
 * Leave History Screen
 */
export default function LeaveHistoryScreen() {
  const router = useRouter();

  const [leaveStatus, setLeaveStatus] = useState(LeaveStatus.Pending);

  // Calculate default date range (last 30 days)
  const defaultDateRange = useMemo(() => {
    const today = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(today.getDate() - 30);

    return {
      fromDate: thirtyDaysAgo.toISOString().split("T")[0],
      toDate: today.toISOString().split("T")[0],
    };
  }, []);

  // Date range state
  const [fromDate, setFromDate] = useState<string | null>(
    defaultDateRange.fromDate
  );
  const [toDate, setToDate] = useState<string | null>(defaultDateRange.toDate);

  // Animation values for the FAB
  const fabScale = useSharedValue(0);
  const fabTranslateY = useSharedValue(100);
  const { session } = useSession();

  // Handle date range change
  const handleDateRangeChange = (
    newFromDate: string | null,
    newToDate: string | null
  ) => {
    setFromDate(newFromDate);
    setToDate(newToDate);
  };

  // State for pull-to-refresh
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch leaves using GraphQL query
  const {
    data: leaves,
    isLoading,
    error,
    refetch,
  } = useGetLeavesQuery(
    {
      input: {
        // Use the user ID from the me query
        user: session!.userId,
        leaveStatus,
        // Add date range filter
        dateRange:
          fromDate && toDate
            ? {
                startDate: new Date(fromDate),
                endDate: new Date(toDate),
              }
            : undefined,
      },
    },
    {
      enabled: !!session && !!session.userId,
      initialData: {
        leaves: [],
      },
    }
  );

  // Start animations when component mounts
  React.useEffect(() => {
    // Animate FAB with a spring effect
    fabScale.value = withDelay(
      500,
      withSpring(1, { damping: 8, stiffness: 100 })
    );

    fabTranslateY.value = withDelay(
      500,
      withSpring(0, { damping: 12, stiffness: 100 })
    );
  }, []);

  // Refetch data when filter parameters change
  React.useEffect(() => {
    if (session?.userId) {
      refetch();
    }
  }, [leaveStatus, fromDate, toDate, session?.userId, refetch]);

  // Refetch data when screen comes into focus (e.g., returning from leave request screen)
  useFocusEffect(
    useCallback(() => {
      if (session?.userId) {
        refetch();
      }
    }, [])
  );

  // FAB animated style
  const fabAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: fabScale.value }, { translateY: fabTranslateY.value }],
  }));

  // Handle leave item press
  const handleLeavePress = (leave: any) => {
    router.push({
      pathname: "/leave/[id]",
      params: { id: leave.id },
    });
  };

  // Handle request new leave button press
  const handleRequestLeave = () => {
    router.push("/leave/request");
  };

  // Handle back button press
  const handleBackPress = () => {
    router.back();
  };

  // Handle pull-to-refresh
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error("Failed to refresh leaves:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={handleBackPress}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <Ionicons name="chevron-back" size={24} color={Colors.white} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Leave History</Text>
      </View>

      {/* Filter Buttons */}
      <View style={styles.filterContainer}>
        {/* Date Range Filter */}
        <Text style={styles.filterLabel}>Date Range:</Text>
        <DateRangeFilter
          fromDate={fromDate}
          toDate={toDate}
          onDateRangeChange={handleDateRangeChange}
          containerStyle={styles.dateRangeFilter}
        />

        {/* Status Filters */}
        <Text style={styles.filterLabel}>Status:</Text>
        <FlatList
          data={enumToOptions(LeaveStatus)}
          keyExtractor={(item) => item.value}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.filterList}
          renderItem={({ item }) => (
            <FilterButton
              option={item}
              isActive={leaveStatus === item.value}
              // @ts-expect-error
              onPress={setLeaveStatus}
            />
          )}
        />
      </View>

      {/* Loading State */}
      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Loading leave requests...</Text>
        </View>
      )}

      {/* Error State */}
      {error && (
        <View style={styles.errorContainer}>
          <Ionicons
            name="alert-circle-outline"
            size={64}
            color={Colors.error}
          />
          <Text style={styles.errorText}>Failed to load leave requests</Text>
          <Text style={styles.errorSubText}>
            {error instanceof Error ? error.message : "Unknown error occurred"}
          </Text>
        </View>
      )}

      {/* Leave List */}
      {!isLoading && !error && (
        <FlatList
          data={leaves.leaves}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.leaveList}
          refreshControl={
            <RefreshControl
              refreshing={isRefreshing}
              onRefresh={handleRefresh}
              colors={[Colors.primary]}
              tintColor={Colors.primary}
            />
          }
          renderItem={({ item, index }) => (
            <LeaveItem
              leave={item as Leave}
              index={index}
              onPress={handleLeavePress}
            />
          )}
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons
                name="calendar-outline"
                size={64}
                color={Colors.textLight}
              />
              <Text style={styles.emptyText}>No leave requests found</Text>
            </View>
          }
        />
      )}

      {/* FAB for requesting new leave */}
      <Animated.View style={[styles.fab, fabAnimatedStyle]}>
        <TouchableOpacity
          style={styles.fabButton}
          onPress={handleRequestLeave}
          activeOpacity={0.8}
        >
          <Ionicons name="add" size={24} color={Colors.white} />
        </TouchableOpacity>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.primary,
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 16,
    flexDirection: "row",
    alignItems: "center",
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: "bold",
    color: Colors.white,
  },
  filterContainer: {
    paddingVertical: 12,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.textSecondary,
    marginLeft: 16,
    marginTop: 8,
    marginBottom: 4,
  },
  filterList: {
    paddingHorizontal: 16,
    marginBottom: 8,
  },
  dateRangeFilter: {
    marginHorizontal: 16,
    marginBottom: 8,
  },
  filterButton: {
    paddingHorizontal: 16,
    paddingVertical: 4,
    borderRadius: 20,
    marginRight: 8,
    backgroundColor: Colors.lightGray,
  },
  activeFilterButton: {
    backgroundColor: Colors.primaryLight,
  },
  filterButtonText: {
    fontSize: 14,
    color: Colors.textSecondary,
  },
  activeFilterButtonText: {
    color: Colors.white,
    fontWeight: "500",
  },
  leaveList: {
    padding: 16,
  },
  leaveItemContainer: {
    marginBottom: 16,
  },
  leaveItem: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
  leaveHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  leaveReason: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
    flex: 1,
    marginRight: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: "500",
    color: Colors.white,
    textTransform: "capitalize",
  },
  leaveDetails: {
    marginBottom: 8,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  detailText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginLeft: 8,
  },
  rejectionContainer: {
    marginTop: 8,
    padding: 8,
    backgroundColor: "rgba(255,0,0,0.05)",
    borderRadius: 8,
  },
  rejectionReason: {
    fontSize: 14,
    color: Colors.error,
  },
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textLight,
    marginTop: 16,
  },
  loadingContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textSecondary,
    marginTop: 16,
  },
  errorContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    padding: 32,
  },
  errorText: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.error,
    marginTop: 16,
  },
  errorSubText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 8,
    textAlign: "center",
  },
  fab: {
    position: "absolute",
    bottom: 24,
    right: 24,
  },
  fabButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.primary,
    alignItems: "center",
    justifyContent: "center",
  },
});

import { Stack } from "expo-router";
import { Colors } from "@/constants/Colors";

/**
 * Layout for the leave section
 * Handles navigation structure for leave-related screens
 */
export default function LeaveLayout() {
  return (
    <Stack
      screenOptions={{
        headerStyle: {
          backgroundColor: Colors.primary,
        },
        headerTintColor: Colors.white,
        headerShadowVisible: false,
        headerShown: false, // Hide the default header, we're using custom headers
      }}
    >
      <Stack.Screen name="index" options={{ title: "Leave History" }} />
      <Stack.Screen name="[id]" options={{ title: "Leave Details" }} />
      <Stack.Screen name="request" options={{ title: "Request Leave" }} />
    </Stack>
  );
}

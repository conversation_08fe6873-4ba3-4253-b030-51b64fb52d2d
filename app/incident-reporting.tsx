import {
  FormEvidenceUpload,
  FormInput,
  FormRadioGroup,
  FormSelect,
  FormSubmitButton,
} from "@/components/forms";
import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "expo-router";
import { StatusBar } from "expo-status-bar";
import React, { useState } from "react";
import { useFieldArray, useForm, useWatch } from "react-hook-form";
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

import {
  EvidenceType,
  PriorityLevel,
  useCreateIncidentMutation,
} from "@/generated/graphql";
import { enumToOptions, errorToast, successToast } from "@/lib/utils";
import { z } from "zod";

const evidenceSchema = z.object({
  type: z.nativeEnum(EvidenceType),
  url: z.string().min(1, "Evidence URL is required"),
});

const incidentReportSchema = z.object({
  description: z
    .string()
    .min(10, { message: "Description must be at least 10 characters" })
    .max(1000, { message: "Description must be less than 1000 characters" }),
  priorityLevel: z.nativeEnum(PriorityLevel, {
    errorMap: () => ({ message: "Please select a priority level" }),
  }),
  evidence: z.array(evidenceSchema),
});

// Evidence Item Component to avoid hooks in loops
interface EvidenceItemProps {
  field: { id: string };
  index: number;
  control: any;
  remove: (index: number) => void;
}

type IncidentReportFormData = z.infer<typeof incidentReportSchema>;

const defaultIncidentReportValues: IncidentReportFormData = {
  description: "",
  priorityLevel: PriorityLevel.Medium,
  evidence: [],
};

function EvidenceItem({ index, control, remove }: EvidenceItemProps) {
  // Watch the evidence type for this specific item
  const evidenceType = useWatch({
    control,
    name: `evidence.${index}.type`,
    defaultValue: EvidenceType.Image,
  });

  return (
    <View style={styles.evidenceItem}>
      <View style={styles.evidenceItemHeader}>
        <Text style={styles.evidenceItemTitle}>Evidence {index + 1}</Text>
        <TouchableOpacity
          style={styles.removeEvidenceButton}
          onPress={() => remove(index)}
        >
          <Ionicons name="trash" size={16} color={Colors.error} />
        </TouchableOpacity>
      </View>

      <FormRadioGroup
        name={`evidence.${index}.type`}
        control={control}
        label="Evidence Type"
        options={enumToOptions(EvidenceType)}
        horizontal={true}
      />

      <FormEvidenceUpload
        name={`evidence.${index}.url`}
        control={control}
        label={`Upload ${evidenceType} Evidence`}
        evidenceType={evidenceType}
        placeholder={`Upload ${evidenceType.toLowerCase()} file`}
      />
    </View>
  );
}

export default function IncidentReportingScreen() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { mutateAsync: createIncidentReport } = useCreateIncidentMutation();

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    reset,
    formState: { isValid },
  } = useForm<IncidentReportFormData>({
    defaultValues: defaultIncidentReportValues,
    resolver: zodResolver(incidentReportSchema),
    mode: "onChange",
  });

  // Field array for evidence management
  const { fields, append, remove } = useFieldArray({
    control,
    name: "evidence",
  });

  // Handle form submission
  const onSubmit = async (data: IncidentReportFormData) => {
    try {
      setIsSubmitting(true);
      await createIncidentReport({
        input: {
          description: data.description,
          priorityLevel: data.priorityLevel,
          evidence: data.evidence,
        },
      });
      successToast("Incident reported successfully!");
      reset(defaultIncidentReportValues);
      router.push("/");
    } catch (err) {
      console.error("Form submission error:", err);
      errorToast("Failed to submit incident report");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Add new evidence item
  const addEvidence = () => {
    append({
      type: EvidenceType.Image,
      url: "",
    });
  };

  return (
    <View style={styles.flex}>
      <StatusBar style="dark" />
      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : undefined}
        keyboardVerticalOffset={100}
        style={styles.container}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          {/* Description Input */}
          <FormInput
            name="description"
            control={control}
            label="Description"
            placeholder="Describe the incident in detail..."
            multiline
            numberOfLines={4}
          />

          {/* Priority Level Dropdown */}
          <FormSelect
            name="priorityLevel"
            control={control}
            label="Priority Level"
            placeholder="Select priority level"
            options={enumToOptions(PriorityLevel)}
          />

          {/* Evidence Section */}
          <View style={styles.evidenceSection}>
            <View style={styles.evidenceHeader}>
              <Text style={styles.evidenceLabel}>Evidence</Text>
              <TouchableOpacity
                style={styles.addEvidenceButton}
                onPress={addEvidence}
              >
                <Ionicons name="add" size={20} color={Colors.white} />
                <Text style={styles.addEvidenceText}>Add Evidence</Text>
              </TouchableOpacity>
            </View>

            {fields.map((field, index) => (
              <EvidenceItem
                key={field.id}
                field={field}
                index={index}
                control={control}
                remove={remove}
              />
            ))}

            {fields.length === 0 && (
              <Text style={styles.noEvidenceText}>
                No evidence added yet. Tap "Add Evidence" to include supporting
                materials.
              </Text>
            )}
          </View>

          {/* Submit Button */}
          <FormSubmitButton
            submitLabel={isSubmitting ? "Submitting..." : "Submit Report"}
            onSubmit={handleSubmit(onSubmit)}
            isValid={isValid && !isSubmitting}
            style={styles.submitButton}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    paddingBottom: 50,
    paddingTop: 16,
    paddingHorizontal: 16,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
  submitButton: {
    marginTop: 20,
  },
  evidenceSection: {
    marginTop: 20,
  },
  evidenceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  evidenceLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
  },
  addEvidenceButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.primary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 4,
  },
  addEvidenceText: {
    fontSize: 14,
    fontWeight: "500",
    color: Colors.white,
  },
  evidenceItem: {
    backgroundColor: Colors.white,
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  evidenceItemHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12,
  },
  evidenceItemTitle: {
    fontSize: 14,
    fontWeight: "600",
    color: Colors.text,
  },
  removeEvidenceButton: {
    padding: 4,
  },
  noEvidenceText: {
    fontSize: 14,
    color: Colors.textLight,
    textAlign: "center",
    fontStyle: "italic",
    paddingVertical: 20,
  },
  flex: { flex: 1 },
});

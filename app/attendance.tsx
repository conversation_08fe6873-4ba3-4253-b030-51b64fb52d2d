import { AttendanceOverview } from "@/components/attendance/AttendanceOverview";
import { MonthSelector } from "@/components/attendance/MonthSelector";
import { Colors } from "@/constants/Colors";
import { useGetUserAttendanceQuery } from "@/generated/graphql";
import { useSession } from "@/providers/auth-provider";
import { startOfMonth, endOfMonth } from "date-fns";

import { StatusBar } from "expo-status-bar";
import React, { useState } from "react";
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
  ActivityIndicator,
} from "react-native";

export default function AttendanceScreen() {
  const { session } = useSession();
  const [selectedMonth, setSelectedMonth] = useState(new Date());

  // Calculate date range for selected month
  const startDate = startOfMonth(selectedMonth);
  const endDate = endOfMonth(selectedMonth);

  // Fetch attendance data for selected month
  const {
    data: attendanceData,
    isLoading,
    error,
  } = useGetUserAttendanceQuery(
    {
      filter: {
        dateRange: {
          startDate,
          endDate,
        },
        userIds: [session!.userId],
      },
    },
    {
      enabled: !!session && !!session.userId,
      initialData: {
        myAttendances: [],
      },
    }
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />
      <View style={styles.header}>
        <Text style={styles.title}>Attendance</Text>
        <Text style={styles.subtitle}>View your attendance history</Text>
      </View>

      {/* Month Selector */}
      <MonthSelector
        selectedMonth={selectedMonth}
        onMonthChange={setSelectedMonth}
      />

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Loading State */}
        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
            <Text style={styles.loadingText}>Loading attendance data...</Text>
          </View>
        )}

        {/* Error State */}
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>Failed to load attendance data</Text>
          </View>
        )}

        {/* Attendance Overview Component */}
        {!isLoading && !error && attendanceData && (
          <AttendanceOverview
            attendanceData={attendanceData.myAttendances}
            selectedMonth={selectedMonth}
          />
        )}

        {/* Additional attendance content can be added here */}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F0F0F0", // Very light grey background
  },
  header: {
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 10,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingVertical: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40,
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: Colors.textSecondary,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 16,
    color: Colors.error,
    textAlign: "center",
  },
});

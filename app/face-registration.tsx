import React, { useRef, useState } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Pressable,
  Image,
} from "react-native";
import { useRouter } from "expo-router";
import { Colors } from "@/constants/Colors";
import { Ionicons } from "@expo/vector-icons";
import { CameraView, useCameraPermissions } from "expo-camera";
import { useSession } from "@/providers/auth-provider";
import { useIndexFaceMutation } from "@/generated/graphql";
import { useQueryClient } from "@tanstack/react-query";

export default function FaceRegistrationScreen() {
  const router = useRouter();
  const { session } = useSession();
  const queryClient = useQueryClient();
  const ref = useRef<CameraView>(null);

  const [showCamera, setShowCamera] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [permission, requestPermission] = useCameraPermissions();

  // Face registration mutation
  const { mutateAsync: indexFace, isLoading: isIndexingFace } =
    useIndexFaceMutation({
      onSuccess: () => {
        // Invalidate the Me query to refresh user data
        queryClient.invalidateQueries(["Me"]);

        Alert.alert(
          "Success!",
          "Your face has been registered successfully. You can now use face recognition for attendance.",
          [
            {
              text: "OK",
              onPress: () => router.back(),
            },
          ]
        );
      },
      onError: (error) => {
        console.error("Face registration failed:", error);
        Alert.alert(
          "Registration Failed",
          "Failed to register your face. Please try again.",
          [{ text: "OK" }]
        );
        setCapturedImage(null);
        setShowCamera(false);
        setIsRegistering(false);
      },
    });

  const handleStartRegistration = async () => {
    if (!permission?.granted) {
      const result = await requestPermission();
      if (!result.granted) {
        Alert.alert(
          "Camera Permission Required",
          "Please allow camera access to register your face for attendance.",
          [{ text: "OK" }]
        );
        return;
      }
    }
    setShowCamera(true);
  };

  const takePicture = async () => {
    try {
      const photo = await ref.current?.takePictureAsync({
        base64: true,
        quality: 0.5,
      });

      if (photo?.base64) {
        const base64Img = `data:image/jpeg;base64,${photo.base64}`;
        setCapturedImage(base64Img);
        setShowCamera(false);
      }
    } catch (error) {
      console.error("Failed to take picture:", error);
      Alert.alert("Error", "Failed to capture your photo. Please try again.", [
        { text: "OK" },
      ]);
    }
  };

  const handleConfirmRegistration = async () => {
    if (!session?.userId || !capturedImage) {
      Alert.alert(
        "Error",
        "User session or image not found. Please try again."
      );
      return;
    }

    try {
      setIsRegistering(true);

      await indexFace({
        indexFaceInput: {
          base64Img: capturedImage,
          userId: session.userId,
        },
      });
    } catch (error) {
      console.error("Failed to register face:", error);
      Alert.alert("Error", "Failed to register your face. Please try again.", [
        { text: "OK" },
      ]);
      setIsRegistering(false);
    }
  };

  const handleRetake = () => {
    setCapturedImage(null);
    setShowCamera(true);
  };

  const handleBackPress = () => {
    if (showCamera) {
      setShowCamera(false);
    } else if (capturedImage) {
      setCapturedImage(null);
    } else {
      router.back();
    }
  };

  if (!permission) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  if (showCamera) {
    return (
      <View style={styles.cameraContainer}>
        <CameraView
          style={styles.camera}
          ref={ref}
          facing="front"
          mute={false}
          responsiveOrientationWhenOrientationLocked
        >
          {/* Back button */}
          <TouchableOpacity onPress={handleBackPress} style={styles.backButton}>
            <Ionicons name="close" size={32} color="white" />
          </TouchableOpacity>

          {/* Face guide overlay */}
          <View style={styles.faceGuideContainer}>
            <View style={styles.faceGuide}>
              <View style={styles.faceGuideCorner} />
              <View style={[styles.faceGuideCorner, styles.topRight]} />
              <View style={[styles.faceGuideCorner, styles.bottomLeft]} />
              <View style={[styles.faceGuideCorner, styles.bottomRight]} />
            </View>
            <Text style={styles.guideText}>
              Position your face within the frame
            </Text>
          </View>

          {/* Capture button */}
          <View style={styles.shutterContainer}>
            <Pressable
              onPress={takePicture}
              disabled={isRegistering || isIndexingFace}
            >
              {({ pressed }) => (
                <View
                  style={[
                    styles.shutterBtn,
                    {
                      opacity:
                        pressed || isRegistering || isIndexingFace ? 0.5 : 1,
                    },
                  ]}
                >
                  {isRegistering || isIndexingFace ? (
                    <ActivityIndicator size="small" color="white" />
                  ) : (
                    <View style={styles.shutterBtnInner} />
                  )}
                </View>
              )}
            </Pressable>
          </View>
        </CameraView>
      </View>
    );
  }

  // Show image preview screen
  if (capturedImage) {
    return (
      <View style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            onPress={handleBackPress}
            style={styles.headerBackButton}
          >
            <Ionicons name="arrow-back" size={24} color={Colors.text} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Preview</Text>
        </View>

        {/* Image Preview */}
        <View style={styles.previewContainer}>
          <View style={styles.imageContainer}>
            <Image
              source={{ uri: capturedImage }}
              style={styles.previewImage}
            />
          </View>

          <Text style={styles.previewTitle}>How does this look?</Text>
          <Text style={styles.previewDescription}>
            Make sure your face is clearly visible and well-lit for the best
            results.
          </Text>

          {/* Action Buttons */}
          <View style={styles.previewActions}>
            <TouchableOpacity
              style={styles.retakeButton}
              onPress={handleRetake}
              activeOpacity={0.8}
            >
              <Ionicons name="camera" size={20} color={Colors.primary} />
              <Text style={styles.retakeButtonText}>Retake</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.confirmButton,
                (isRegistering || isIndexingFace) &&
                  styles.confirmButtonDisabled,
              ]}
              onPress={handleConfirmRegistration}
              activeOpacity={0.8}
              disabled={isRegistering || isIndexingFace}
            >
              {isRegistering || isIndexingFace ? (
                <ActivityIndicator size="small" color={Colors.white} />
              ) : (
                <Ionicons name="checkmark" size={20} color={Colors.white} />
              )}
              <Text style={styles.confirmButtonText}>
                {isRegistering || isIndexingFace
                  ? "Registering..."
                  : "Confirm & Register"}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={handleBackPress}
          style={styles.headerBackButton}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Face Registration</Text>
      </View>

      {/* Content */}
      <View style={styles.content}>
        {/* Icon */}
        <View style={styles.iconContainer}>
          <Ionicons name="scan" size={80} color={Colors.primary} />
        </View>

        {/* Title */}
        <Text style={styles.title}>Register Your Face</Text>

        {/* Description */}
        <Text style={styles.description}>
          Register your face for quick and secure attendance tracking using AI
          technology.
        </Text>

        {/* Benefits */}
        <View style={styles.benefitsContainer}>
          <View style={styles.benefitItem}>
            <Ionicons
              name="checkmark-circle"
              size={20}
              color={Colors.success}
            />
            <Text style={styles.benefitText}>Fast attendance check-in</Text>
          </View>
          <View style={styles.benefitItem}>
            <Ionicons
              name="checkmark-circle"
              size={20}
              color={Colors.success}
            />
            <Text style={styles.benefitText}>Secure and accurate</Text>
          </View>
          <View style={styles.benefitItem}>
            <Ionicons
              name="checkmark-circle"
              size={20}
              color={Colors.success}
            />
            <Text style={styles.benefitText}>
              No need to remember passwords
            </Text>
          </View>
        </View>

        {/* Instructions */}
        <View style={styles.instructionsContainer}>
          <Text style={styles.instructionsTitle}>For best results:</Text>
          <Text style={styles.instructionText}>• Ensure good lighting</Text>
          <Text style={styles.instructionText}>
            • Look directly at the camera
          </Text>
          <Text style={styles.instructionText}>
            • Keep your face within the frame
          </Text>
          <Text style={styles.instructionText}>
            • Remove glasses if possible
          </Text>
        </View>

        {/* Register button */}
        <TouchableOpacity
          style={styles.registerButton}
          onPress={handleStartRegistration}
          activeOpacity={0.8}
        >
          <Ionicons name="camera" size={20} color={Colors.white} />
          <Text style={styles.registerButtonText}>Start Registration</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  cameraContainer: {
    flex: 1,
  },
  camera: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    backgroundColor: Colors.white,
  },
  headerBackButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: Colors.text,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingVertical: 32,
    alignItems: "center",
  },
  iconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: `${Colors.primary}15`,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 12,
    textAlign: "center",
  },
  description: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 32,
  },
  benefitsContainer: {
    width: "100%",
    marginBottom: 32,
  },
  benefitItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  benefitText: {
    fontSize: 16,
    color: Colors.text,
    marginLeft: 12,
  },
  instructionsContainer: {
    width: "100%",
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 20,
    marginBottom: 32,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  instructionsTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.text,
    marginBottom: 12,
  },
  instructionText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 6,
    lineHeight: 20,
  },
  registerButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.primary,
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
    width: "100%",
  },
  registerButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.white,
    marginLeft: 8,
  },
  backButton: {
    position: "absolute",
    top: 50,
    left: 20,
    zIndex: 99,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    borderRadius: 20,
    padding: 8,
  },
  faceGuideContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
  },
  faceGuide: {
    width: 200,
    height: 250,
    position: "relative",
    marginBottom: 20,
  },
  faceGuideCorner: {
    position: "absolute",
    width: 30,
    height: 30,
    borderColor: Colors.white,
    borderWidth: 3,
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    left: "auto",
    borderLeftWidth: 0,
    borderRightWidth: 3,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    top: "auto",
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
    borderBottomWidth: 3,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    top: "auto",
    left: "auto",
    borderLeftWidth: 0,
    borderTopWidth: 0,
    borderRightWidth: 3,
    borderBottomWidth: 3,
  },
  guideText: {
    color: Colors.white,
    fontSize: 16,
    textAlign: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  shutterContainer: {
    position: "absolute",
    bottom: 50,
    alignSelf: "center",
  },
  shutterBtn: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 4,
    borderColor: Colors.white,
  },
  shutterBtnInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: Colors.white,
  },
  // Preview screen styles
  previewContainer: {
    flex: 1,
    paddingHorizontal: 24,
    paddingVertical: 32,
    alignItems: "center",
  },
  imageContainer: {
    width: 250,
    height: 300,
    borderRadius: 20,
    overflow: "hidden",
    marginBottom: 24,
    borderWidth: 3,
    borderColor: Colors.primary,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  previewImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  previewTitle: {
    fontSize: 22,
    fontWeight: "bold",
    color: Colors.text,
    marginBottom: 8,
    textAlign: "center",
  },
  previewDescription: {
    fontSize: 16,
    color: Colors.textSecondary,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: 32,
  },
  previewActions: {
    flexDirection: "row",
    width: "100%",
    gap: 16,
  },
  retakeButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.white,
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.primary,
  },
  retakeButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.primary,
    marginLeft: 8,
  },
  confirmButton: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: Colors.primary,
    paddingVertical: 16,
    borderRadius: 12,
  },
  confirmButtonDisabled: {
    opacity: 0.7,
  },
  confirmButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: Colors.white,
    marginLeft: 8,
  },
});

import { View, Text, StyleSheet } from "react-native";
import React from "react";
import { useLocalSearchParams } from "expo-router";
import { Colors } from "@/constants/Colors";
import { ClaimStatus, ClaimType, useClaimQuery } from "@/generated/graphql";
import AllowanceForm from "@/forms/claims/allowance-form";
import ExpenseForm from "@/forms/claims/expense-form";
import TravelForm from "@/forms/claims/travel-form";
import LoadingScreen from "@/components/LoadingView";
import UpdateAllowanceForm from "@/forms/claims/update-allowance-form";

export default function UpdateClaim() {
  const { claimId } = useLocalSearchParams<{ claimId: string }>();

  const { data, isLoading } = useClaimQuery({ input: claimId });
  const claimType = data?.claim.claimType;
  const claimStatus = data?.claim.status;
  return (
    <View style={styles.container}>
      <Text style={styles.heading}>Update Claim</Text>
      {isLoading ? (
        <View style={{ flex: 1 }}>
          <LoadingScreen />
        </View>
      ) : (
        <View style={{ flex: 1 }}>
          {claimType === ClaimType.Allowance &&
            claimStatus === ClaimStatus.Pending && (
              <UpdateAllowanceForm claimId={claimId} />
            )}
          {claimType === ClaimType.Expense && <ExpenseForm />}
          {claimType === ClaimType.Travel && <TravelForm />}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, paddingHorizontal: 16, backgroundColor: Colors.white },
  heading: {
    fontSize: 20,
    fontWeight: "500",
    color: Colors.text,
    marginVertical: 16,
  },
});

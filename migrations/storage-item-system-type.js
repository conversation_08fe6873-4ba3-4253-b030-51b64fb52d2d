"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const storage_entity_1 = require("../src/storage/entities/storage.entity");
module.exports = {
    async up(db) {
        const system_folders = Object.values(storage_entity_1.StorageSystemFolders);
        const bulkWriteOperation = system_folders.map((folder) => ({
            updateOne: {
                filter: { name: folder, type: storage_entity_1.StorageItemType.FOLDER },
                update: {
                    $set: {
                        type: storage_entity_1.StorageItemType.FOLDER,
                        parent: null,
                        size: 0,
                        path: '/',
                        source: storage_entity_1.StorageItemSource.SYSTEM,
                    },
                },
                upsert: true,
            },
        }));
        await db.collection('storages').bulkWrite(bulkWriteOperation);
    },
};
//# sourceMappingURL=storage-item-system-type.js.map
import { mongo } from 'mongoose';

module.exports = {
  async up(db: mongo.Db) {
    // Remove the location field from all existing incident documents
    await db.collection('incident-monitoring').updateMany(
      {},
      {
        $unset: { location: '' }
      }
    );
  },

  async down(db: mongo.Db) {
    // Note: This down migration cannot fully restore the location data
    // as we don't have a way to determine what the original location values were.
    // This is a destructive migration.
    console.warn('Warning: Cannot restore location field data. This is a destructive migration.');
    
    // Optionally, you could set a default location or leave it null
    // Uncomment the following if you want to add a placeholder location field:
    /*
    await db.collection('incident-monitoring').updateMany(
      {},
      {
        $set: { location: null }
      }
    );
    */
  },
};

import { mongo } from 'mongoose';
import {
  StorageItemSource,
  StorageItemType,
  StorageSystemFolders,
} from 'src/storage/entities/storage.entity';

module.exports = {
  async up(db: mongo.Db) {
    const system_folders = Object.values(StorageSystemFolders);

    const bulkWriteOperation = system_folders.map((folder) => ({
      updateOne: {
        filter: { name: folder, type: StorageItemType.FOLDER },
        update: {
          $set: {
            type: StorageItemType.FOLDER,
            parent: null,
            size: 0,
            path: '/',
            source: StorageItemSource.SYSTEM,
          },
        },
        upsert: true,
      },
    }));

    await db.collection('storages').bulkWrite(bulkWriteOperation);
  },
};

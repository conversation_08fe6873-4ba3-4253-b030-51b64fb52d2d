import { z } from "zod";

// Define priority levels to match GraphQL enum
export enum PriorityLevel {
  Low = "Low",
  Medium = "Medium",
  High = "High",
  Critical = "Critical",
}

// Define evidence types to match GraphQL enum
export enum EvidenceType {
  Image = "Image",
  Video = "Video",
  Document = "Document",
}

// Evidence schema for file uploads
export const evidenceSchema = z.object({
  type: z.nativeEnum(EvidenceType),
  url: z.string().min(1, "Evidence URL is required"),
});

// Schema for incident reporting form - matches GraphQL mutation exactly
export const incidentReportSchema = z.object({
  description: z
    .string()
    .min(10, { message: "Description must be at least 10 characters" })
    .max(1000, { message: "Description must be less than 1000 characters" }),
  priorityLevel: z.nativeEnum(PriorityLevel, {
    errorMap: () => ({ message: "Please select a priority level" }),
  }),
  evidence: z.array(evidenceSchema),
});

// Type for incident report form
export type IncidentReportFormData = z.infer<typeof incidentReportSchema>;

// Type for evidence
export type EvidenceData = z.infer<typeof evidenceSchema>;

// Default values for incident report form
export const defaultIncidentReportValues: IncidentReportFormData = {
  description: "",
  priorityLevel: PriorityLevel.Medium,
  evidence: [],
};

// Helper function to get priority level options for dropdown
export const getPriorityLevelOptions = () => {
  return Object.entries(PriorityLevel).map(([key, value]) => ({
    label: key,
    value,
  }));
};

// Helper function to get evidence type options
export const getEvidenceTypeOptions = () => {
  return Object.entries(EvidenceType).map(([key, value]) => ({
    label: key,
    value,
  }));
};

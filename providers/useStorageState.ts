import * as SecureStore from "expo-secure-store";
import { useCallback, useEffect, useReducer } from "react";
import { Session } from "./auth-provider";

type UseStateHook<T> = [[boolean, T | null], (value: T | null) => void];

function useAsyncState<T>(
  initialValue: [boolean, T | null] = [true, null]
): UseStateHook<T> {
  return useReducer(
    (_: [boolean, T | null], action: T | null): [boolean, T | null] => [
      false,
      action,
    ],
    initialValue
  ) as UseStateHook<T>;
}

async function setStorageItemAsync<T>(key: string, value: T | null) {
  if (value == null) {
    await SecureStore.deleteItemAsync(key);
  } else {
    await SecureStore.setItemAsync(key, JSON.stringify(value));
  }
}

async function getStorageItemAsync<T>(key: string): Promise<T | null> {
  const storedValue = await SecureStore.getItemAsync(key);
  if (storedValue == null) return null;

  try {
    return JSON.parse(storedValue) as T;
  } catch {
    return null;
  }
}

export function useStorageState<T = Session>(key: string): UseStateHook<T> {
  const [state, setState] = useAsyncState<T>();

  useEffect(() => {
    getStorageItemAsync<T>(key).then((value) => {
      setState(value);
    });
  }, [key]);

  const setValue = useCallback(
    (value: T | null) => {
      setState(value);
      setStorageItemAsync(key, value);
    },
    [key]
  );

  return [state, setValue];
}

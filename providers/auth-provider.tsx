import {
  MeQ<PERSON>y,
  MeQueryVariables,
  SignInInput,
  useMeQuery,
  useSigninMutation,
} from "@/generated/graphql";
import { useQueryClient } from "@tanstack/react-query";
import {
  createContext,
  type PropsWithChildren,
  useContext,
  useState,
} from "react";
import { useStorageState } from "./useStorageState";
import { fetchData } from "@/client";

export interface Session {
  access_token: string;
  userId: string;
  fullname: string;
}

// Match the SignInPayload type with the GraphQL SignInInput type
type SignInPayload = {
  phone: string;
  password: string;
};

const AuthContext = createContext<{
  signIn: (payload: SignInPayload) => Promise<void>;
  signOut: () => void;
  session: Session | null;
  isLoading: boolean;
  error: string | null;
}>({
  signIn: async () => {},
  signOut: () => null,
  session: null,
  isLoading: false,
  error: null,
});

// This hook can be used to access the user info.
export function useSession() {
  const value = useContext(AuthContext);
  if (process.env.NODE_ENV !== "production") {
    if (!value) {
      throw new Error("useSession must be wrapped in a <SessionProvider />");
    }
  }

  return value;
}

export function SessionProvider({ children }: PropsWithChildren) {
  const [[isLoading, session], setSession] = useStorageState("session");
  const [error, setError] = useState<string | null>(null);
  const queryClient = useQueryClient();

  // Use the generated SignIn mutation hook
  const { mutateAsync: signInMutation, isLoading: isMutationLoading } =
    useSigninMutation({
      onSuccess: async (data) => {
        // Reset any previous errors
        setError(null);

        // Store the token in secure storage
        if (data.signIn.access_token) {
          // get user data
          const {
            me: { id: userId, fullname },
          } = await fetchData<MeQuery, MeQueryVariables>(
            useMeQuery.document,
            undefined,
            {
              Authorization: `Bearer ${data.signIn.access_token}`,
            }
          )();

          setSession({
            access_token: data.signIn.access_token,
            fullname,
            userId,
          });
        }
      },
      onError: (error) => {
        setError(error.message || "Authentication failed");
      },
    });

  return (
    <AuthContext.Provider
      value={{
        signIn: async (payload) => {
          try {
            // Call the GraphQL mutation
            await signInMutation({
              input: payload as SignInInput,
            });
            // Navigation will be handled by AuthGuard
          } catch (error) {
            if (error instanceof Error) {
              setError(error.message);
            } else {
              setError("Authentication failed");
            }
            throw error;
          }
        },
        signOut: () => {
          // Clear the token from storage
          setSession(null);

          // Clear any cached queries
          queryClient.clear();

          // Navigation will be handled by AuthGuard
        },
        session,
        isLoading: isLoading || isMutationLoading,
        error,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

# This GraphQL query retrieves the user profile information based on the provided user ID.
query UserProfile($userId: String!) {
  userProfile(id: $userId) {
    _id
    permitExpiresAt
    gender
    dob
    placeOfBirth
    currentAddress
    joinedAt
    maritalStatus
    bankAccNumber
    bankName
    emergencyContact {
      name
      relation
      contact {
        countryCode
        phone
      }
    }
    id
    ID
    ic
    passport
    passportExpiresAt
    permitNumber
  }
}

# This GraphQL query retrieves the user profile information based on the provided user ID.
mutation UpdateUserProfile(
  $userId: String!
  $updateUserProfileInput: UpdateUserProfileInput!
) {
  updateUserProfile(
    id: $userId
    updateUserProfileInput: $updateUserProfileInput
  ) {
    id
  }
}

# This GraphQL mutation updates the user profile information for a specific user ID.
mutation IndexFace($indexFaceInput: IndexFaceInput!) {
  indexFace(indexFaceInput: $indexFaceInput) {
    id
    fullname
    userStatus
    role
  }
}

# This GraphQL mutation clears the face data for a specific user.
mutation ClearFace($input: ClearFaceInput!) {
  clearFace(clearFaceInput: $input)
}

mutation CreateInventoryRequest($input: CreateInventoryRequestInput!) {
  createInventoryRequest(input: $input) {
    _id
    id
    createdAt
    updatedAt
    status
    acceptedAt
    inventoryType
    inventory {
      _id
      id
      createdAt
      updatedAt
      item
      description
      type
      attributes {
        attibuteName
        attributeValues
      }
      items {
        item
        quantity
        sku
        costPrice
        sellingPrice
      }
    }
    acceptedBy {
      _id
      id
      createdAt
      updatedAt
      fullname
      phone
      userStatus
      role
      profilePicture
    }
    requestedBy {
      _id
      id
      createdAt
      updatedAt
      fullname
      phone
      userStatus
      role
      profilePicture
    }
  }
}

query GetInventory {
  inventory {
    _id
    id
    createdAt
    updatedAt
    item
    description
    type
    items {
      item
      quantity
      sku
      costPrice
      sellingPrice
    }
    attributes {
      attibuteName
      attributeValues
    }
  }
}
